<Project Sdk="Godot.NET.Sdk/4.4.1">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <EnableDynamicLoading>true</EnableDynamicLoading>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Nullable>enable</Nullable>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
  </PropertyGroup>
  <PropertyGroup>
    <DefineConstants Condition="'$(Configuration)'!='ExportRelease' And&#xA;        $(DefineConstants.Contains('GODOT_PC'))">$(DefineConstants);IMGUI</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="SpaceGame\Assets\**" />
    <Content Include="**\*.uid" />
    <Content Include="**\*.tres" />
    <Content Include="project.godot" />
    <Content Include="**\*.md" />
    <Content Include="**\*.tscn" />
    <Folder Include="SpaceGame\Data\Weapons\" />
    <Folder Include="SpaceGame\Scripts\Debug\UI\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="ImGui.NET" Version="********" />
    <PackageReference Include="R3" Version="1.3.0" />
  </ItemGroup>
</Project>