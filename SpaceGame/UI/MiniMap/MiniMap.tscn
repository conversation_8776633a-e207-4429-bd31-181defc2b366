[gd_scene load_steps=4 format=3 uid="uid://clpmrpypk5svs"]

[ext_resource type="Script" uid="uid://di3c515aj75ct" path="res://SpaceGame/UI/MiniMap/MiniMap.cs" id="1_b5sqo"]
[ext_resource type="Theme" uid="uid://c6udgeioox4qf" path="res://SpaceGame/Assets/SpaceGameTheme.tres" id="2_wqfo5"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tn3xe"]
bg_color = Color(0.0784314, 0.0784314, 0.0784314, 0.8)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.14902, 0.14902, 0.14902, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5
shadow_color = Color(0, 0, 0, 0.25098)
shadow_size = 4
shadow_offset = Vector2(2, 2)

[node name="MiniMap" type="Control"]
layout_mode = 3
anchors_preset = 0
offset_right = 200.0
offset_bottom = 200.0
script = ExtResource("1_b5sqo")

[node name="PanelContainer" type="PanelContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_tn3xe")

[node name="Panel" type="Panel" parent="PanelContainer"]
layout_mode = 2
mouse_filter = 2
theme = ExtResource("2_wqfo5")
