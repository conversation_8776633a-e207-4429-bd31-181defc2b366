[gd_scene load_steps=49 format=3 uid="uid://di5n85lsgqn7"]

[ext_resource type="PackedScene" uid="uid://00u8dmcuuurs" path="res://SpaceGame/Scenes/Battle/ShipEntity.tscn" id="1_hmxqk"]
[ext_resource type="Script" uid="uid://bemg0nfrq4ef4" path="res://SpaceGame/Scripts/Battle/Data/ShipEntityData.cs" id="2_00hpu"]
[ext_resource type="Script" uid="uid://us8ronbet75f" path="res://SpaceGame/Scripts/Ships/Data/WeaponSlotData.cs" id="2_8sejb"]
[ext_resource type="Texture2D" uid="uid://bgnn6fxyft4t7" path="res://SpaceGame/Assets/Factions/Federation/Scout/Kla'ed - Scout - Base.png" id="2_cy7xy"]
[ext_resource type="Resource" uid="uid://cclvtb167swbl" path="res://SpaceGame/Resources/AI/DefaultBehaviors/ScoutDefaultBehavior.tres" id="2_iewyw"]
[ext_resource type="Texture2D" uid="uid://b55hcb1mf6orl" path="res://SpaceGame/Assets/Factions/Federation/Fighter/Kla'ed - Fighter - Engine.png" id="3_1340u"]
[ext_resource type="Texture2D" uid="uid://06ljig8umh7w" path="res://SpaceGame/Assets/Factions/Federation/Scout/Kla'ed - Scout - Destruction.png" id="5_7isco"]
[ext_resource type="Texture2D" uid="uid://d3f8hlnfw48sl" path="res://SpaceGame/Assets/Factions/Federation/Scout/Kla'ed - Scout - Shield.png" id="5_bkjwj"]
[ext_resource type="Script" uid="uid://78faoxod14rx" path="res://SpaceGame/Scripts/Battle/WeaponMount.cs" id="5_bo57w"]

[sub_resource type="Resource" id="Resource_8sejb"]
script = ExtResource("2_8sejb")
MountPointId = "FrontMount"
WeaponId = "KineticCannonMk1"
DisplayVisuals = false
metadata/_custom_type_script = "uid://us8ronbet75f"

[sub_resource type="Resource" id="Resource_u4k2w"]
script = ExtResource("2_00hpu")
MaxSpeed = 1600.0
Acceleration = 400.0
Deceleration = 300.0
DragFactor = 0.8
RotationSpeed = 720.0
MovementThreshold = 2.0
RotationThreshold = 1.0
SlowingDistance = 50.0
ShipDetailType = 6
WeaponSlots = Array[ExtResource("2_8sejb")]([SubResource("Resource_8sejb")])
DefaultBehavior = ExtResource("2_iewyw")
MaxShields = 50.0
ShieldRegenRate = 2.0
ShieldResistance = 1.0
Id = "c824081c-2d94-4ce9-9214-f5654a409406"
Name = "Scout MK I"
MaxHealth = 100.0
BaseType = 0
metadata/_custom_type_script = "uid://bemg0nfrq4ef4"

[sub_resource type="AtlasTexture" id="AtlasTexture_hbks4"]
atlas = ExtResource("3_1340u")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_bnvel"]
atlas = ExtResource("3_1340u")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7tljb"]
atlas = ExtResource("3_1340u")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_bvgrg"]
atlas = ExtResource("3_1340u")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xo20c"]
atlas = ExtResource("3_1340u")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_gcwpl"]
atlas = ExtResource("3_1340u")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_g1hbk"]
atlas = ExtResource("3_1340u")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_5pprd"]
atlas = ExtResource("3_1340u")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7gwmp"]
atlas = ExtResource("3_1340u")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ke3y"]
atlas = ExtResource("3_1340u")
region = Rect2(576, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_rqthd"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_hbks4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bnvel")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7tljb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bvgrg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xo20c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gcwpl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g1hbk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5pprd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7gwmp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ke3y")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_0xgv6"]
atlas = ExtResource("5_7isco")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_603wi"]
atlas = ExtResource("5_7isco")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_6ar3i"]
atlas = ExtResource("5_7isco")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ditdp"]
atlas = ExtResource("5_7isco")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ivjjl"]
atlas = ExtResource("5_7isco")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2rxfr"]
atlas = ExtResource("5_7isco")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2qi8b"]
atlas = ExtResource("5_7isco")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_a138a"]
atlas = ExtResource("5_7isco")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_x124l"]
atlas = ExtResource("5_7isco")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_gw45g"]
atlas = ExtResource("5_7isco")
region = Rect2(576, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_u4k2w"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("2_cy7xy")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_0xgv6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_603wi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6ar3i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ditdp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ivjjl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2rxfr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2qi8b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a138a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x124l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gw45g")
}],
"loop": true,
"name": &"destruction",
"speed": 10.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_os8xx"]
atlas = ExtResource("5_bkjwj")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7isco"]
atlas = ExtResource("5_bkjwj")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_8sejb"]
atlas = ExtResource("5_bkjwj")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_iewyw"]
atlas = ExtResource("5_bkjwj")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_c5jyh"]
atlas = ExtResource("5_bkjwj")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ae0yb"]
atlas = ExtResource("5_bkjwj")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_1ciyt"]
atlas = ExtResource("5_bkjwj")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_qp6pq"]
atlas = ExtResource("5_bkjwj")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_4885v"]
atlas = ExtResource("5_bkjwj")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_s145j"]
atlas = ExtResource("5_bkjwj")
region = Rect2(576, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ylinb"]
atlas = ExtResource("5_bkjwj")
region = Rect2(640, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_lijme"]
atlas = ExtResource("5_bkjwj")
region = Rect2(704, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_wa26i"]
atlas = ExtResource("5_bkjwj")
region = Rect2(768, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_es3w7"]
atlas = ExtResource("5_bkjwj")
region = Rect2(832, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_0xgv6"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_os8xx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7isco")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8sejb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iewyw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c5jyh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ae0yb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1ciyt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qp6pq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4885v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s145j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ylinb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lijme")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wa26i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_es3w7")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]

[node name="Scout" node_paths=PackedStringArray("CollisionShape", "EntitySprite") instance=ExtResource("1_hmxqk")]
CollisionShape = NodePath("CollisionShape2D")
EntitySprite = NodePath("EntitySprite")
EntityData = SubResource("Resource_u4k2w")

[node name="ThrusterComponent" parent="." index="0" node_paths=PackedStringArray("ThrusterSprite", "ThrusterFlame")]
Mode = 2
ThrusterSprite = NodePath("ThrusterSprite")
ThrusterFlame = NodePath("ThrusterFlame")

[node name="ThrusterSprite" parent="ThrusterComponent" index="0"]
sprite_frames = SubResource("SpriteFrames_rqthd")
autoplay = "default"
frame_progress = 0.908105

[node name="ThrusterFlame" parent="ThrusterComponent" index="1"]
position = Vector2(0, 15)
MaxFlameLength = 25.0
FlameBaseWidth = 5.0
FlameCoreColor = Color(1, 1, 1, 0.9)
FlameEdgeColor = Color(0.2, 0.6, 1, 0.7)
FlickerIntensity = 0.06
FlickerSpeed = 10.0

[node name="TrailComponent" parent="." index="9"]
TrailColor = Color(0.2, 0.6, 1, 0.7)
TrailWidth = 2.5
MaxTrailPoints = 40
MinPointDistance = 4.0
FadeRate = 0.025
MinVelocityThreshold = 15.0

[node name="EntitySprite" parent="." index="1"]
sprite_frames = SubResource("SpriteFrames_u4k2w")
frame_progress = 0.630501

[node name="ShieldSprite" parent="." index="2"]
visible = false
sprite_frames = SubResource("SpriteFrames_0xgv6")

[node name="FrontMount" type="Node2D" parent="WeaponMounts" index="0" node_paths=PackedStringArray("MountIndicator")]
position = Vector2(0, -10)
script = ExtResource("5_bo57w")
MountId = "FrontMount"
MountName = "FrontMount"
AllowedWeaponTypeFlags = 1
MountIndicator = NodePath("FrontMountIndicator")
metadata/_custom_type_script = "uid://78faoxod14rx"

[node name="FrontMountIndicator" type="Sprite2D" parent="WeaponMounts/FrontMount" index="0"]
