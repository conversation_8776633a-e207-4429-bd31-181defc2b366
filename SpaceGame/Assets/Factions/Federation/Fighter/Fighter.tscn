[gd_scene load_steps=41 format=3 uid="uid://cwnj86jml15wo"]

[ext_resource type="PackedScene" uid="uid://00u8dmcuuurs" path="res://SpaceGame/Scenes/Battle/ShipEntity.tscn" id="1_bnvel"]
[ext_resource type="Texture2D" uid="uid://pju6dvlc26qn" path="res://SpaceGame/Assets/Factions/Federation/Fighter/Kla'ed - Fighter - Base.png" id="2_7tljb"]
[ext_resource type="Resource" uid="uid://d3ddjwljmlq4y" path="res://SpaceGame/Assets/Factions/Federation/Fighter/FighterShipData.tres" id="2_t11bl"]
[ext_resource type="Texture2D" uid="uid://b55hcb1mf6orl" path="res://SpaceGame/Assets/Factions/Federation/Fighter/Kla'ed - Fighter - Engine.png" id="3_bvgrg"]
[ext_resource type="Script" uid="uid://78faoxod14rx" path="res://SpaceGame/Scripts/Battle/WeaponMount.cs" id="4_xo20c"]
[ext_resource type="Texture2D" uid="uid://biwhpjknmn43v" path="res://SpaceGame/Assets/Factions/Federation/Fighter/Kla'ed - Fighter - Shield.png" id="5_gcwpl"]
[ext_resource type="Texture2D" uid="uid://di6dmrs4w2x4" path="res://SpaceGame/Assets/Factions/Federation/Fighter/Kla'ed - Fighter - Destruction.png" id="5_t11bl"]

[sub_resource type="AtlasTexture" id="AtlasTexture_hbks4"]
atlas = ExtResource("3_bvgrg")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_bnvel"]
atlas = ExtResource("3_bvgrg")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7tljb"]
atlas = ExtResource("3_bvgrg")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_bvgrg"]
atlas = ExtResource("3_bvgrg")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xo20c"]
atlas = ExtResource("3_bvgrg")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_gcwpl"]
atlas = ExtResource("3_bvgrg")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_g1hbk"]
atlas = ExtResource("3_bvgrg")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_5pprd"]
atlas = ExtResource("3_bvgrg")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7gwmp"]
atlas = ExtResource("3_bvgrg")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ke3y"]
atlas = ExtResource("3_bvgrg")
region = Rect2(576, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_rqthd"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_hbks4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bnvel")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7tljb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bvgrg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xo20c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gcwpl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g1hbk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5pprd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7gwmp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ke3y")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_3ur30"]
atlas = ExtResource("2_7tljb")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vjoyy"]
atlas = ExtResource("5_t11bl")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xm6p0"]
atlas = ExtResource("5_t11bl")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_p0to4"]
atlas = ExtResource("5_t11bl")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_b22dh"]
atlas = ExtResource("5_t11bl")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ga8de"]
atlas = ExtResource("5_t11bl")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_mrae6"]
atlas = ExtResource("5_t11bl")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_44sog"]
atlas = ExtResource("5_t11bl")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_gnbvi"]
atlas = ExtResource("5_t11bl")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vybtl"]
atlas = ExtResource("5_t11bl")
region = Rect2(512, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_u4k2w"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3ur30")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_vjoyy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xm6p0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p0to4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b22dh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ga8de")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mrae6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_44sog")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gnbvi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vybtl")
}],
"loop": true,
"name": &"destruction",
"speed": 10.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_u4k2w"]
atlas = ExtResource("5_gcwpl")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_t11bl"]
atlas = ExtResource("5_gcwpl")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_dst1h"]
atlas = ExtResource("5_gcwpl")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xwfpn"]
atlas = ExtResource("5_gcwpl")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vq6wi"]
atlas = ExtResource("5_gcwpl")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_alpg0"]
atlas = ExtResource("5_gcwpl")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_5l06t"]
atlas = ExtResource("5_gcwpl")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_gwbyy"]
atlas = ExtResource("5_gcwpl")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_07ceu"]
atlas = ExtResource("5_gcwpl")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_c4kg4"]
atlas = ExtResource("5_gcwpl")
region = Rect2(576, 0, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_vjoyy"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_u4k2w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t11bl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dst1h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xwfpn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vq6wi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_alpg0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5l06t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gwbyy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_07ceu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c4kg4")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]

[node name="Fighter" node_paths=PackedStringArray("CollisionShape", "EntitySprite") instance=ExtResource("1_bnvel")]
CollisionShape = NodePath("CollisionShape2D")
EntitySprite = NodePath("EntitySprite")
EntityData = ExtResource("2_t11bl")

[node name="ThrusterComponent" parent="." index="0" node_paths=PackedStringArray("ThrusterSprite")]
ThrusterSprite = NodePath("ThrusterSprite")

[node name="ThrusterSprite" parent="ThrusterComponent" index="0"]
sprite_frames = SubResource("SpriteFrames_rqthd")
autoplay = "default"
frame_progress = 0.908105

[node name="ThrusterParticles" parent="ThrusterComponent" index="1"]
position = Vector2(0, 15)

[node name="EntitySprite" parent="." index="1"]
sprite_frames = SubResource("SpriteFrames_u4k2w")
animation = &"destruction"
frame_progress = 0.93937

[node name="ShieldSprite" parent="." index="2"]
visible = false
sprite_frames = SubResource("SpriteFrames_vjoyy")

[node name="LeftMount" type="Node2D" parent="WeaponMounts" index="0" node_paths=PackedStringArray("MountIndicator")]
position = Vector2(-10, -10)
script = ExtResource("4_xo20c")
MountId = "LeftMount"
MountName = "LeftMount"
AllowedWeaponTypeFlags = 1
MountIndicator = NodePath("LeftMountIndicator")
metadata/_custom_type_script = "uid://78faoxod14rx"

[node name="LeftMountIndicator" type="Sprite2D" parent="WeaponMounts/LeftMount" index="0"]

[node name="RightMount" type="Node2D" parent="WeaponMounts" index="1" node_paths=PackedStringArray("MountIndicator")]
position = Vector2(10, -10)
script = ExtResource("4_xo20c")
MountId = "RightMount"
MountName = "RightMount"
AllowedWeaponTypeFlags = 1
MountIndicator = NodePath("RightMountIndicator")
metadata/_custom_type_script = "uid://78faoxod14rx"

[node name="RightMountIndicator" type="Sprite2D" parent="WeaponMounts/RightMount" index="0"]
