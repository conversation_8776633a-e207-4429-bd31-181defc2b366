using Godot;
using SpaceGame.Battle.AI;

namespace SpaceGame.Battle.AI.Components
{
    public abstract partial class MovementBehaviorComponent : EnemyBehaviorComponent
    {
        [Export]
        public float ThrustStrength { get; set; } = 1.0f;
        
        [Export]
        public float RotationSpeed { get; set; } = 2.0f;
        
        public abstract MovementBehaviorType BehaviorType { get; }
    }
}
