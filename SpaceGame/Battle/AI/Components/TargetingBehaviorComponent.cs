using Godot;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Battle.AI.Components
{
    public abstract partial class TargetingBehaviorComponent : EnemyBehaviorComponent
    {
        [Export]
        public float DetectionRange { get; set; } = 800f;
        
        public ShipEntity CurrentTarget { get; set; }
        
        public ShipEntity GetCurrentTarget() => CurrentTarget;
        
        public abstract TargetingBehaviorType BehaviorType { get; }
    }
}
