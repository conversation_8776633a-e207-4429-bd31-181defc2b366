using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Battle.Registry;
using SpaceGame.Helpers;
using SpaceGame.Debug;

namespace SpaceGame.Battle.AI.Components.Targeting
{
    public partial class ProximityTargetingBehavior : TargetingBehaviorComponent
    {
        [Export]
        public float TargetUpdateInterval { get; set; } = 1.0f;
        
        [Export]
        public float DefaultDetectionRange { get; set; } = 800.0f;
        
        [Export]
        public float FallbackScanInterval { get; set; } = 3.0f;
        
        [Export]
        public float ExtendedDetectionRange { get; set; } = 1200.0f;
        
        [Export]
        public bool EnableDebugVisuals { get; set; } = false;
        
        private double _timeSinceLastUpdate = 0.0;
        private double _timeSinceLastFallbackScan = 0.0;
        private BattleEntityTracker _battleEntityTracker => BattleEntityTracker.Instance;
        private Node2D _debugVisuals;
        
        // Track when we lost a target to implement "memory" of targets
        private BaseBattleEntity _lastTarget;
        private double _timeSinceLostTarget = 0.0;
        private const double TARGET_MEMORY_DURATION = 5.0; // How long to remember a lost target

        public override TargetingBehaviorType BehaviorType => TargetingBehaviorType.Proximity;
        
        public override void Initialize(ShipEntity ship)
        {
            // Ensure we have a valid detection range
            if (DetectionRange <= 0)
            {
                Logger.Debug($"{ship.Name}: Setting default detection range to {DefaultDetectionRange}");
                DetectionRange = DefaultDetectionRange;
            }
            
            if (EnableDebugVisuals)
            {
                SetupDebugVisuals(ship);
            }
            
            // Register with debug registry
            DebugRegistry.RegisterObject($"{ship.Name} Proximity Targeting", this, "AI");
        }
        
        public override void UpdateBehavior(ShipEntity ship, double delta)
        {
            _timeSinceLastUpdate += delta;
            _timeSinceLastFallbackScan += delta;
            
            // If we have a last target but no current target, track time since lost
            if (CurrentTarget == null && _lastTarget != null)
            {
                _timeSinceLostTarget += delta;
                
                // If we've remembered the target long enough, forget it
                if (_timeSinceLostTarget > TARGET_MEMORY_DURATION)
                {
                    _lastTarget = null;
                }
            }
            
            // Only update target periodically to save performance
            if (_timeSinceLastUpdate >= TargetUpdateInterval)
            {
                _timeSinceLastUpdate = 0;
                UpdateTarget(ship);
            }
            
            // If we don't have a target, try a fallback scan with extended range
            if (CurrentTarget == null && _timeSinceLastFallbackScan >= FallbackScanInterval)
            {
                _timeSinceLastFallbackScan = 0;
                FallbackTargetScan(ship);
            }
            
            // Update debug visuals if enabled
            if (EnableDebugVisuals && _debugVisuals != null)
            {
                UpdateDebugVisuals(ship);
            }
        }
        
        private void UpdateTarget(ShipEntity ship)
        {
            if (DetectionRange <= 0)
            {
                Logger.Warning($"{ship.Name}: Invalid detection range {DetectionRange}, using default {DefaultDetectionRange}");
                DetectionRange = DefaultDetectionRange;
            }
            
            // Find all enemy ships within detection range
            var potentialTargets = _battleEntityTracker
                .GetEntitiesInRadiusByType<ShipEntity>(ship.Position, DetectionRange)
                .Where(e => !e.IsDead && e.CurrentAllegiance != ship.CurrentAllegiance)
                .ToList();
            
            // Sort by distance and select the closest one
            if (potentialTargets.Count > 0)
            {
                // Prioritize our last target if it's still in range (target persistence)
                if (_lastTarget != null && potentialTargets.Contains(_lastTarget))
                {
                    CurrentTarget = _lastTarget as ShipEntity;
                    Logger.Debug($"{ship.Name}: Maintaining lock on previous target {CurrentTarget.Name}");
                }
                else
                {
                    // Otherwise pick the closest
                    CurrentTarget = potentialTargets
                        .OrderBy(s => ship.Position.DistanceTo(s.Position))
                        .FirstOrDefault();
                    
                    Logger.Debug($"{ship.Name}: Acquired new target {CurrentTarget.Name} at distance {ship.Position.DistanceTo(CurrentTarget.Position)}");
                }
                
                // Remember this target
                _lastTarget = CurrentTarget;
                _timeSinceLostTarget = 0;
            }
            else if (CurrentTarget != null)
            {
                // We lost our target, remember it before clearing
                _lastTarget = CurrentTarget;
                _timeSinceLostTarget = 0;
                
                CurrentTarget = null;
                Logger.Debug($"{ship.Name}: Lost target, will remember {_lastTarget.Name} for {TARGET_MEMORY_DURATION} seconds");
            }
        }
        
        private void FallbackTargetScan(ShipEntity ship)
        {
            // Only do fallback scan if we don't have a current target
            if (CurrentTarget != null)
                return;
                
            Logger.Debug($"{ship.Name}: Performing fallback scan with extended range {ExtendedDetectionRange}");
            
            // Use extended range for fallback scan
            var potentialTargets = _battleEntityTracker
                .GetEntitiesInRadiusByType<ShipEntity>(ship.Position, ExtendedDetectionRange)
                .Where(e => !e.IsDead && e.CurrentAllegiance != ship.CurrentAllegiance)
                .ToList();
                
            if (potentialTargets.Count > 0)
            {
                // If we recently lost a target, prioritize it if it's in extended range
                if (_lastTarget != null && _timeSinceLostTarget < TARGET_MEMORY_DURATION && 
                    potentialTargets.Contains(_lastTarget))
                {
                    CurrentTarget = _lastTarget as ShipEntity;
                    Logger.Debug($"{ship.Name}: Reacquired previous target {CurrentTarget.Name} in fallback scan");
                }
                else
                {
                    // Otherwise pick the closest
                    CurrentTarget = potentialTargets
                        .OrderBy(s => ship.Position.DistanceTo(s.Position))
                        .FirstOrDefault();
                        
                    Logger.Debug($"{ship.Name}: Fallback scan found target {CurrentTarget.Name} at distance {ship.Position.DistanceTo(CurrentTarget.Position)}");
                }
                
                // Remember this target
                _lastTarget = CurrentTarget;
                _timeSinceLostTarget = 0;
            }
        }
        
        private void SetupDebugVisuals(ShipEntity ship)
        {
            // Create debug visuals container if it doesn't exist
            _debugVisuals = new Node2D();
            _debugVisuals.Name = "TargetingDebugVisuals";
            AddChild(_debugVisuals);
            
            // Create detection range circle using a Line2D
            var rangeCircle = new Line2D();
            rangeCircle.Name = "DetectionRangeCircle";
            rangeCircle.Width = 1.0f;
            rangeCircle.DefaultColor = new Color(0, 1, 0, 0.3f); // Green with low alpha
            
            // Create circle points
            const int numPoints = 32;
            var points = new Vector2[numPoints];
            for (int i = 0; i < numPoints; i++)
            {
                float angle = (float)i / numPoints * Mathf.Pi * 2;
                points[i] = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * DetectionRange;
            }
            rangeCircle.Points = points;
            
            _debugVisuals.AddChild(rangeCircle);
            
            // Create target line
            var targetLine = new Line2D();
            targetLine.Name = "TargetLine";
            targetLine.Width = 2.0f;
            targetLine.DefaultColor = new Color(1, 0, 0); // Red
            targetLine.Points = new Vector2[] { Vector2.Zero, Vector2.Zero }; // Will be updated in UpdateDebugVisuals
            
            _debugVisuals.AddChild(targetLine);
        }
        
        private void UpdateDebugVisuals(ShipEntity ship)
        {
            if (_debugVisuals == null)
                return;
                
            // Update target line if we have a target
            var targetLine = _debugVisuals.GetNode<Line2D>("TargetLine");
            if (targetLine != null)
            {
                if (CurrentTarget != null)
                {
                    // Calculate relative position from ship to target
                    Vector2 relativePos = CurrentTarget.Position - ship.Position;
                    
                    // Update line points
                    targetLine.Points = new Vector2[] { Vector2.Zero, relativePos };
                    targetLine.Visible = true;
                }
                else
                {
                    targetLine.Visible = false;
                }
            }
        }
        
        public override void _ExitTree()
        {
            // Unregister from debug registry
            DebugRegistry.UnregisterObject($"{GetParent()?.GetParent()?.Name ?? "Unknown"} Proximity Targeting");
            
            base._ExitTree();
        }
    }
}
