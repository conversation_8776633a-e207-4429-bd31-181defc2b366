using Godot;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Debug;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Battle.Registry;
using SpaceGame.Helpers;
using SpaceGame.Scripts;

namespace SpaceGame.Battle.AI.Components.Targeting
{
    public partial class PriorityTargetingBehavior : TargetingBehaviorComponent
    {
        [Export]
        public float TargetUpdateInterval { get; set; } = 1.5f;
        
        // Using a private field for the array since ShipType[] is not exportable
        // Note: Using a default value of 0 since ShipType enum might not have Flagship
        private ShipType[] _priorityShipTypes = new ShipType[] { 0 };
        
        // Property to access the array
        public ShipType[] PriorityShipTypes
        {
            get => _priorityShipTypes;
            set => _priorityShipTypes = value;
        }
        
        private double _timeSinceLastUpdate = 0.0;
        private BattleEntityTracker _battleEntityTracker => BattleEntityTracker.Instance;

        public override TargetingBehaviorType BehaviorType => TargetingBehaviorType.Priority;

        public override void Initialize(ShipEntity ship)
        {
            DebugRegistry.RegisterObject($"{ship.Name} Priority Targeting", this, "AI");
        }
        
        public override void UpdateBehavior(ShipEntity ship, double delta)
        {
            _timeSinceLastUpdate += delta;
            
            // Only update target periodically to save performance
            if (_timeSinceLastUpdate >= TargetUpdateInterval)
            {
                _timeSinceLastUpdate = 0;
                UpdateTarget(ship);
            }
        }
        
        private void UpdateTarget(ShipEntity ship)
        {
            // Find all enemy ships within detection range
            var potentialTargets = _battleEntityTracker
                .GetEntitiesInRadiusByType<ShipEntity>(ship.Position, DetectionRange)
                .Where(e => !e.IsDead && e.CurrentAllegiance != ship.CurrentAllegiance)
                .ToList();
            
            if (potentialTargets.Count == 0)
            {
                CurrentTarget = null;
                Logger.Debug("PriorityTargetingBehavior: No targets found within detection range");
                return;
            }
            
            // Try to find priority targets first
            var priorityTargets = potentialTargets
                .Where(s => s.EntityData is ShipEntityData shipData && 
                       PriorityShipTypes.Contains(shipData.ShipDetailType))
                .ToList();
            
            if (priorityTargets.Count > 0)
            {
                // Sort priority targets by distance
                CurrentTarget = priorityTargets
                    .OrderBy(s => ship.Position.DistanceTo(s.Position))
                    .FirstOrDefault();
                    
                Logger.Debug($"PriorityTargetingBehavior: Found priority target {CurrentTarget.Name} at distance {ship.Position.DistanceTo(CurrentTarget.Position)}");
            }
            else
            {
                // If no priority targets found, fall back to closest ship
                CurrentTarget = potentialTargets
                    .OrderBy(s => ship.Position.DistanceTo(s.Position))
                    .FirstOrDefault();
                    
                Logger.Debug($"PriorityTargetingBehavior: No priority targets found, using closest target {CurrentTarget.Name}");
            }
        }
    }
}
