using Godot;
using SpaceGame.Battle.AI.Components;
using SpaceGame.Debug;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Helpers;

namespace SpaceGame.Battle.AI.Components.Movement
{
    public partial class InterceptMovementBehavior : MovementBehaviorComponent
    {
        [Export]
        public float OptimalDistance { get; set; } = 120f; // Distance to maintain from target
        
        [Export]
        public bool FaceTarget { get; set; } = true; // Whether to face the target directly
        
        [Export]
        public float ApproachSpeed { get; set; } = 0.8f; // Speed factor when approaching target
        
        [Export]
        public float RetreatSpeed { get; set; } = 0.7f; // Speed factor when moving away from target
        
        // Used to track if we're currently in a controlled retreat
        private bool _inControlledRetreat = false;
        private Vector2 _lastTargetPosition = Vector2.Zero;
        
        private TargetingBehaviorComponent? _targetingBehavior;
        
        public override MovementBehaviorType BehaviorType => MovementBehaviorType.Intercept;
        
        public override void Initialize(ShipEntity ship)
        {
            // We'll use MoveTo instead of direct control
            _targetingBehavior = AttemptToGetTargetingBehavior(ship);
            if (_targetingBehavior == null)
            {
                Logger.Warning("InterceptMovementBehavior: No targeting behavior found");
            }
            
            DebugRegistry.RegisterObject($"{ship.Name} Intercept Movement", this, "AI");
        }

        public override void UpdateBehavior(ShipEntity ship, double delta)
        {
            _targetingBehavior ??= AttemptToGetTargetingBehavior(ship);
            
            var target = _targetingBehavior?.GetCurrentTarget();
            if (target == null || target.IsDead)
            {
                // No target, stop moving
                ship.MovementController?.StopMovement();
                return;
            }
            
            // Calculate vector to target
            Vector2 targetPosition = target.GlobalPosition;
            Vector2 shipToTarget = targetPosition - ship.GlobalPosition;
            float distanceToTarget = shipToTarget.Length();
            Vector2 directionToTarget = shipToTarget.Normalized();
            
            // Calculate the desired position at optimal distance from target
            Vector2 idealPosition = targetPosition - directionToTarget * OptimalDistance;
            Vector2 desiredPosition = idealPosition; // Default to ideal position
            
            // Track if the target has moved significantly
            bool targetMovedSignificantly = (_lastTargetPosition - targetPosition).LengthSquared() > 100f;
            _lastTargetPosition = targetPosition;
            
            // If we're too close to the target, we need a special approach to avoid the yo-yo effect
            if (distanceToTarget < OptimalDistance * 0.8f)
            {
                // We're too close to the target, need to back up in a controlled manner
                _inControlledRetreat = true;
                
                if (ship.MovementController != null)
                {
                    // Instead of using MoveTo, we'll directly control the velocity
                    // This gives us much finer control and prevents overshooting
                    
                    // Calculate the retreat direction (away from target)
            Vector2 retreatDirection = -directionToTarget;
            
            // Instead of moving backward, we need to rotate the ship to face away from the target
            // and then move forward in that direction
            
            // Calculate the angle to face away from the target
            float retreatAngle = retreatDirection.Angle();
            
            // Add a +90 degree offset to align with the ship's forward direction
            retreatAngle += Mathf.Pi/2;
            
            // Convert from radians to degrees
            float retreatAngleDegrees = Mathf.RadToDeg(retreatAngle);
            
            // Rotate the ship to face away from the target
            ship.MovementController.RotateTowards(retreatAngleDegrees);
            
            // Calculate how fast we should be moving based on how close we are to optimal distance
            float distanceRatio = distanceToTarget / OptimalDistance;
            float targetSpeed = 0f;
            
            if (distanceRatio < 0.5f)
            {
                // Very close, move away quickly
                targetSpeed = 120f * RetreatSpeed;
            }
            else if (distanceRatio < 0.7f)
            {
                // Getting closer to optimal, moderate speed
                targetSpeed = 80f * RetreatSpeed;
            }
            else
            {
                // Almost at optimal distance, lower speed
                targetSpeed = 40f * RetreatSpeed;
            }
            
            // Check if the ship is facing approximately in the retreat direction
            float currentAngle = Mathf.DegToRad(ship.GlobalRotationDegrees);
            float angleDifference = Mathf.Abs(Mathf.AngleDifference(currentAngle, retreatAngle));
            
            if (angleDifference < 0.3f) // About 17 degrees tolerance
            {
                // Ship is facing approximately in the retreat direction, apply forward thrust
                Vector2 forwardDirection = Vector2.Up.Rotated(currentAngle);
                Vector2 targetVelocity = forwardDirection * targetSpeed;
                
                // Set the velocity to move forward in the current facing direction
                ship.MovementController.SetCurrentVelocity(targetVelocity);
            }
            else
            {
                // Ship is still turning, slow down until we're facing the right direction
                ship.MovementController.StopMovement();
            }
                    
                    // We've handled movement directly, so we'll skip the MoveTo call
                    desiredPosition = ship.GlobalPosition; // Just stay where we are
                }
            }
            else if (distanceToTarget > OptimalDistance * 1.2f)
            {
                // We're too far, need to move closer - use the ideal position directly
                desiredPosition = idealPosition;
                _inControlledRetreat = false;
            }
            else
            {
                // We're at a good distance, reset the retreat flag
                _inControlledRetreat = false;
            }
            // else we're at a good distance, maintain it with the default idealPosition
            
            // Use the MoveTo method to navigate to the desired position
            if (ship.MovementController != null)
            {
                // Only use MoveTo when we're not in a controlled retreat
                if (!_inControlledRetreat)
                {
                    // We're either at a good distance or too far
                    if (distanceToTarget > OptimalDistance * 1.2f)
                    {
                        // We're too far, approach at normal speed
                        ship.MovementController.MoveTo(desiredPosition);
                    }
                    else
                    {
                        // We're at a good distance, maintain position
                        // Get current velocity
                        Vector2 currentVelocity = ship.MovementController.GetCurrentVelocity();
                        float currentSpeed = currentVelocity.Length();
                        
                        // If we're moving too fast, slow down first
                        if (currentSpeed > 40f)
                        {
                            ship.MovementController.StopMovement();
                        }
                        else
                        {
                            // We're moving at a reasonable speed, use MoveTo
                            ship.MovementController.MoveTo(desiredPosition);
                        }
                    }
                }
                // else we're in a controlled retreat and already handled movement
                
                // Determine what direction to face
                if (FaceTarget)
                {
                    // Face the target directly (for combat)
                    float targetAngle = directionToTarget.Angle();
                    
                    // Add a +90 degree offset (in radians) to align with the ship's forward direction
                    // This converts from "right is 0 degrees" to "up is 0 degrees"
                    targetAngle += Mathf.Pi/2;
                    
                    ship.MovementController.RotateTowards(Mathf.RadToDeg(targetAngle));
                }
                else
                {
                    // Face the direction of movement
                    Vector2 moveDirection = (desiredPosition - ship.GlobalPosition).Normalized();
                    float moveAngle = moveDirection.Angle();
                    ship.MovementController.RotateTowards(moveAngle);
                }
            }
        }
    }
}
