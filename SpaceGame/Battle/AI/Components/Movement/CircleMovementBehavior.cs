using Godot;
using SpaceGame.Debug;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Helpers;

namespace SpaceGame.Battle.AI.Components.Movement
{
    public partial class CircleMovementBehavior : MovementBehaviorComponent
    {
        [Export]
        public float CircleDistance { get; set; } = 150f; // Distance to maintain from target
        
        [Export]
        public float CircleSpeed { get; set; } = 0.8f; // How fast to move around the circle
        
        [Export]
        public bool ClockwiseCircling { get; set; } = true; // Direction to circle
        
        private TargetingBehaviorComponent? _targetingBehavior;
        private float _circleAngle = 0f;
        
        public override MovementBehaviorType BehaviorType => MovementBehaviorType.Circle;
        
        public override void Initialize(ShipEntity ship)
        {
            // We'll use MoveTo instead of direct control
            _targetingBehavior = AttemptToGetTargetingBehavior(ship);
            if (_targetingBehavior == null)
            {
                Logger.Warning("CircleMovementBehavior: No targeting behavior found");
            }
            
            // Initialize with a random angle
            _circleAngle = (float)GD.RandRange(0, Mathf.Tau);
            DebugRegistry.RegisterObject($"{ship.Name} Circle Movement", this, "AI");
        }
        
        public override void UpdateBehavior(ShipEntity ship, double delta)
        {
            _targetingBehavior ??= AttemptToGetTargetingBehavior(ship);
            
            var target = _targetingBehavior?.GetCurrentTarget();
            if (target == null || target.IsDead)
            {
                // No target, stop moving
                ship.MovementController?.StopMovement();
                return;
            }
            
            // Update the circle angle based on time
            float circleDirection = ClockwiseCircling ? 1f : -1f;
            _circleAngle += circleDirection * CircleSpeed * (float)delta;
            
            // Calculate the desired position on the circle around the target
            Vector2 targetPosition = target.GlobalPosition;
            Vector2 circleOffset = new Vector2(
                Mathf.Cos(_circleAngle) * CircleDistance,
                Mathf.Sin(_circleAngle) * CircleDistance
            );
            Vector2 desiredPosition = targetPosition + circleOffset;
            
            // Use the MoveTo method to navigate to the desired position
            if (ship.MovementController != null)
            {
                // Move to the calculated position
                ship.MovementController.MoveTo(desiredPosition);
                
                // Optionally, we can also tell the ship to face the target
                // This makes combat more effective as weapons can fire at the target
                Vector2 directionToTarget = (targetPosition - ship.GlobalPosition).Normalized();
                float targetAngle = directionToTarget.Angle();
                ship.MovementController.RotateTowards(targetAngle);
            }
        }
    }
}
