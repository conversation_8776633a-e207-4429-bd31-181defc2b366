using Godot;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Ships;
using SpaceGame.Helpers;

namespace SpaceGame.Battle.AI.Components.Weapon
{
    public partial class StrategicWeaponBehavior : WeaponBehaviorComponent
    {
        [Export]
        public float OptimalFiringDistance { get; set; } = 500f;
        
        [Export]
        public float DistanceTolerance { get; set; } = 200f;
        
        [Export]
        public float BurstDuration { get; set; } = 2.0f;
        
        [Export]
        public float CooldownDuration { get; set; } = 3.0f;
        
        private TargetingBehaviorComponent _targetingBehavior;
        private ShipWeaponController _weaponController;
        private double _firingTimer = 0.0;
        private bool _isFiring = false;
        
        public override WeaponBehaviorType BehaviorType => WeaponBehaviorType.Strategic;
        
        public override void Initialize(ShipEntity ship)
        {
            var behaviorController = ship.GetNode<ShipBehaviorController>("BehaviorController");
            if (behaviorController != null)
            {
                _targetingBehavior = behaviorController.GetTargetingBehavior();
            }
            
            _weaponController = ship.GetNode<ShipWeaponController>("ShipWeaponController");
            
            if (_targetingBehavior == null)
            {
                Logger.Warning("StrategicWeaponBehavior: No targeting behavior found");
            }
            
            if (_weaponController == null)
            {
                Logger.Warning("StrategicWeaponBehavior: No weapon controller found");
            }
        }
        
        public override void UpdateBehavior(ShipEntity ship, double delta)
        {
            if (_targetingBehavior == null || _weaponController == null)
            {
                return;
            }
            
            var target = _targetingBehavior.GetCurrentTarget();
            if (target == null || target.IsDead)
            {
                // No target, don't fire and reset timers
                _isFiring = false;
                _firingTimer = 0.0;
                return;
            }
            
            // Calculate direction to target
            Vector2 targetPosition = target.GlobalPosition;
            Vector2 direction = (targetPosition - ship.GlobalPosition).Normalized();
            
            // Calculate distance to target
            float distance = ship.GlobalPosition.DistanceTo(targetPosition);
            
            // Calculate angle difference
            float targetAngle = direction.Angle();
            float currentAngle = ship.Rotation;
            float angleDifference = Mathf.Abs(Mathf.AngleDifference(currentAngle, targetAngle));
            
            // Update firing timer
            _firingTimer += delta;
            
            // Check if we should switch between firing and cooldown states
            if (_isFiring && _firingTimer >= BurstDuration)
            {
                _isFiring = false;
                _firingTimer = 0.0;
            }
            else if (!_isFiring && _firingTimer >= CooldownDuration)
            {
                _isFiring = true;
                _firingTimer = 0.0;
            }
            
            // Only fire if in firing state, within angle threshold and distance range
            bool withinAngle = angleDifference <= FiringAngleThreshold;
            bool withinDistance = distance >= (OptimalFiringDistance - DistanceTolerance) && 
                                 distance <= (OptimalFiringDistance + DistanceTolerance);
            
            if (_isFiring && withinAngle && withinDistance)
            {
                FireAllWeapons();
            }
        }
        
        // Helper method to fire all weapons
        private void FireAllWeapons()
        {
            if (_weaponController == null)
            {
                return;
            }
            
            // Get the parent ship entity
            var parentShip = GetParent()?.GetParent() as ShipEntity;
            if (parentShip != null)
            {
                // Use the ship's FirePrimaryWeapons method
                parentShip.FirePrimaryWeapons();
            }
        }
    }
}
