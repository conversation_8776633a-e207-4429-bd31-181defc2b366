using Godot;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Ships;
using SpaceGame.Helpers;

namespace SpaceGame.Battle.AI.Components.Weapon
{
    public partial class OpportunisticWeaponBehavior : WeaponBehaviorComponent
    {
        [Export]
        public float OptimalFiringDistance { get; set; } = 500f;
        
        [Export]
        public float DistanceTolerance { get; set; } = 200f;
        
        private TargetingBehaviorComponent _targetingBehavior;
        private ShipWeaponController _weaponController;
        
        public override WeaponBehaviorType BehaviorType => WeaponBehaviorType.Opportunistic;
        
        public override void Initialize(ShipEntity ship)
        {
            var behaviorController = ship.GetNode<ShipBehaviorController>("BehaviorController");
            if (behaviorController != null)
            {
                _targetingBehavior = behaviorController.GetTargetingBehavior();
            }
            
            _weaponController = ship.GetNode<ShipWeaponController>("ShipWeaponController");
            
            if (_targetingBehavior == null)
            {
                Logger.Warning("OpportunisticWeaponBehavior: No targeting behavior found");
            }
            
            if (_weaponController == null)
            {
                Logger.Warning("OpportunisticWeaponBehavior: No weapon controller found");
            }
        }
        
        public override void UpdateBehavior(ShipEntity ship, double delta)
        {
            if (_targetingBehavior == null || _weaponController == null)
            {
                return;
            }
            
            var target = _targetingBehavior.GetCurrentTarget();
            if (target == null || target.IsDead)
            {
                // No target, don't fire
                return;
            }
            
            // Calculate direction to target
            Vector2 targetPosition = target.GlobalPosition;
            Vector2 direction = (targetPosition - ship.GlobalPosition).Normalized();
            
            // Calculate distance to target
            float distance = ship.GlobalPosition.DistanceTo(targetPosition);
            
            // Calculate angle difference
            float targetAngle = direction.Angle();
            float currentAngle = ship.Rotation;
            float angleDifference = Mathf.Abs(Mathf.AngleDifference(currentAngle, targetAngle));
            
            // Only fire if within angle threshold and distance range
            bool withinAngle = angleDifference <= FiringAngleThreshold;
            bool withinDistance = distance >= (OptimalFiringDistance - DistanceTolerance) && 
                                 distance <= (OptimalFiringDistance + DistanceTolerance);
            
            if (withinAngle)
            {
                // Always try to fire if aiming at target, regardless of distance
                // Fire all weapons by iterating through weapon mounts
                FireAllWeapons(ship);
            }
            else if (withinDistance && angleDifference <= FiringAngleThreshold * 2)
            {
                // If within good distance but not perfectly aligned, fire occasionally
                if (GD.Randf() < 0.3f)
                {
                    FireAllWeapons(ship);
                }
            }
        }
        
        // Helper method to fire all weapons
        private void FireAllWeapons(ShipEntity ship)
        {
            // Use the ship's FirePrimaryWeapons method
            ship.FirePrimaryWeapons();
        }
    }
}
