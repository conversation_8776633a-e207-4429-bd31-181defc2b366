using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Battle.AI.Components
{
    public abstract partial class EnemyBehaviorComponent : Node
    {
        [Export]
        public bool IsEnabled { get; set; } = true;

        public abstract void Initialize(ShipEntity ship);
        public abstract void UpdateBehavior(ShipEntity ship, double delta);
        
        protected TargetingBehaviorComponent? AttemptToGetTargetingBehavior(ShipEntity ship)
        {
            Logger.Debug($"{ship.Name}: Attempting to get targeting behavior");
            return ship.GetFirstNodeOfType<ShipBehaviorController>()?.GetTargetingBehavior();
        }

    }
}
