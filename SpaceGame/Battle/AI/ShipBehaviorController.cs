using Godot;
using SpaceGame.Battle.AI.Components;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Helpers;
using SpaceGame.Debug;

namespace SpaceGame.Battle.AI
{
    public partial class ShipBehaviorController : Node
    {
        private ShipEntity _ship;
        private MovementBehaviorComponent _movementBehavior;
        private TargetingBehaviorComponent _targetingBehavior;
        private WeaponBehaviorComponent _weaponBehavior;
        
        // Debug visuals
        private bool _debugMode = true;
        private Node2D _debugVisuals;
        private Label _statusLabel;

        public override void _Ready()
        {
            _ship = GetParent() as ShipEntity;
            if (_ship == null)
            {
                Logger.Error("ShipBehaviorController must be a child of a ShipEntity");
                return;
            }
            
            Logger.Debug($"Initializing ShipBehaviorController for {_ship.Name}");
            
            // Find and initialize behavior components
            int componentsFound = 0;
            foreach (var child in GetChildren())
            {
                if (child is MovementBehaviorComponent movementBehavior)
                {
                    _movementBehavior = movementBehavior;
                    _movementBehavior.Initialize(_ship);
                    Logger.Debug($"{_ship.Name}: Found MovementBehavior: {movementBehavior.GetType().Name}");
                    componentsFound++;
                }
                else if (child is TargetingBehaviorComponent targetingBehavior)
                {
                    _targetingBehavior = targetingBehavior;
                    _targetingBehavior.Initialize(_ship);
                    Logger.Debug($"{_ship.Name}: Found TargetingBehavior: {targetingBehavior.GetType().Name}");
                    componentsFound++;
                }
                else if (child is WeaponBehaviorComponent weaponBehavior)
                {
                    _weaponBehavior = weaponBehavior;
                    _weaponBehavior.Initialize(_ship);
                    Logger.Debug($"{_ship.Name}: Found WeaponBehavior: {weaponBehavior.GetType().Name}");
                    componentsFound++;
                }
            }
            
            Logger.Debug($"{_ship.Name}: Found {componentsFound} behavior components");
            
            if (componentsFound == 0)
            {
                Logger.Warning($"{_ship.Name}: No behavior components found for this ship");
            }
            
            if (_debugMode)
            {
                SetupDebugVisuals();
            }
            
            // Register with debug registry
            DebugRegistry.RegisterObject($"{_ship.Name} Behavior Controller", this, "AI");
        }

        public override void _PhysicsProcess(double delta)
        {
            if (_ship == null || _ship.IsDead)
            {
                return;
            }

            // Update targeting first, as other behaviors may depend on it
            if (_targetingBehavior != null && _targetingBehavior.IsEnabled)
            {
                _targetingBehavior.UpdateBehavior(_ship, delta);
            }

            // Update movement behavior
            if (_movementBehavior != null && _movementBehavior.IsEnabled)
            {
                _movementBehavior.UpdateBehavior(_ship, delta);
            }

            // Update weapon behavior
            if (_weaponBehavior != null && _weaponBehavior.IsEnabled)
            {
                _weaponBehavior.UpdateBehavior(_ship, delta);
            }
            
            // Update debug visuals
            if (_debugMode && _debugVisuals != null)
            {
                UpdateDebugVisuals();
            }
        }

        // Helper methods to get specific behaviors
        public TargetingBehaviorComponent GetTargetingBehavior() => _targetingBehavior;
        public MovementBehaviorComponent GetMovementBehavior() => _movementBehavior;
        public WeaponBehaviorComponent GetWeaponBehavior() => _weaponBehavior;
        
        /// <summary>
        /// Reports the current state of all behavior components for diagnostic purposes
        /// </summary>
        /// <returns>A string containing the diagnostic information</returns>
        public string GetDiagnosticReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine($"Behavior Diagnostic for {_ship?.Name ?? "Unknown Ship"}:");
            
            report.AppendLine($"  Targeting: {(_targetingBehavior != null ? (_targetingBehavior.IsEnabled ? "Active" : "Disabled") : "Not Assigned")}");
            if (_targetingBehavior != null)
            {
                report.AppendLine($"    Type: {_targetingBehavior.GetType().Name}");
                report.AppendLine($"    Current Target: {_targetingBehavior.CurrentTarget?.Name ?? "None"}");
            }
            
            report.AppendLine($"  Movement: {(_movementBehavior != null ? (_movementBehavior.IsEnabled ? "Active" : "Disabled") : "Not Assigned")}");
            if (_movementBehavior != null)
            {
                report.AppendLine($"    Type: {_movementBehavior.GetType().Name}");
            }
            
            report.AppendLine($"  Weapon: {(_weaponBehavior != null ? (_weaponBehavior.IsEnabled ? "Active" : "Disabled") : "Not Assigned")}");
            if (_weaponBehavior != null)
            {
                report.AppendLine($"    Type: {_weaponBehavior.GetType().Name}");
            }
            
            return report.ToString();
        }
        
        private void SetupDebugVisuals()
        {
            // Create debug visuals container
            _debugVisuals = new Node2D();
            _debugVisuals.Name = "DebugVisuals";
            AddChild(_debugVisuals);
            
            // Create status label
            _statusLabel = new Label();
            _statusLabel.Name = "StatusLabel";
            _statusLabel.Text = "";
            _statusLabel.Position = new Vector2(0, -50); // Position above the ship
            _statusLabel.HorizontalAlignment = HorizontalAlignment.Center;
            _statusLabel.VerticalAlignment = VerticalAlignment.Center;
            _statusLabel.ZIndex = 100; // Ensure it's visible above other elements
            _debugVisuals.AddChild(_statusLabel);
        }
        
        private void UpdateDebugVisuals()
        {
            if (_statusLabel != null)
            {
                // Update status text
                string status = "AI: ";
                
                if (_targetingBehavior != null && _targetingBehavior.IsEnabled)
                {                    
                    status += "T";
                    if (_targetingBehavior.CurrentTarget != null)
                    {
                        status += "*"; // Indicate target acquired
                    }
                }
                else
                {
                    status += "-";
                }
                
                if (_movementBehavior != null && _movementBehavior.IsEnabled)
                {
                    status += "M";
                }
                else
                {
                    status += "-";
                }
                
                if (_weaponBehavior != null && _weaponBehavior.IsEnabled)
                {
                    status += "W";
                }
                else
                {
                    status += "-";
                }
                
                _statusLabel.Text = status;
            }
        }
        
        public override void _ExitTree()
        {
            // Unregister from debug registry
            DebugRegistry.UnregisterObject($"{_ship?.Name ?? "Unknown"} Behavior Controller");
        }
    }
}
