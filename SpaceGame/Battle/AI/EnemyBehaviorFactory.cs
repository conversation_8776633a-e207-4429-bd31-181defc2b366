using Godot;
using SpaceGame.Battle.AI.Components;
using SpaceGame.Battle.AI.Components.Movement;
using SpaceGame.Battle.AI.Components.Targeting;
using SpaceGame.Battle.AI.Components.Weapon;
using SpaceGame.Helpers;

namespace SpaceGame.Battle.AI
{
    public static class EnemyBehaviorFactory
    {
        public static ShipBehaviorController CreateBehaviorController(EnemyBehaviorConfiguration config)
        {
            if (config == null)
            {
                Logger.Error("Cannot create behavior controller from null configuration");
                return null;
            }

            var controller = new ShipBehaviorController();

            // Create and configure movement behavior
            var movementBehavior = CreateMovementBehavior(config);
            if (movementBehavior != null)
            {
                controller.AddChild(movementBehavior);
            }

            // Create and configure targeting behavior
            var targetingBehavior = CreateTargetingBehavior(config);
            if (targetingBehavior != null)
            {
                controller.AddChild(targetingBehavior);
            }

            // Create and configure weapon behavior
            var weaponBehavior = CreateWeaponBehavior(config);
            if (weaponBehavior != null)
            {
                controller.AddChild(weaponBehavior);
            }

            return controller;
        }

        private static MovementBehaviorComponent CreateMovementBehavior(EnemyBehaviorConfiguration config)
        {
            MovementBehaviorComponent behavior = null;

            switch (config.MovementBehaviorType)
            {
                case MovementBehaviorType.Intercept:
                    behavior = new InterceptMovementBehavior
                    {
                        ThrustStrength = config.ThrustStrength,
                        RotationSpeed = config.RotationSpeed,
                        OptimalDistance = config.OptimalDistance
                    };
                    break;

                case MovementBehaviorType.Circle:
                    behavior = new CircleMovementBehavior
                    {
                        ThrustStrength = config.ThrustStrength,
                        RotationSpeed = config.RotationSpeed,
                        CircleDistance = config.CircleDistance,
                        CircleSpeed = config.CircleSpeed,
                        ClockwiseCircling = config.ClockwiseCircling
                    };
                    break;

                // Additional movement behavior types can be added here
                default:
                    Logger.Warning($"Unsupported movement behavior type: {config.MovementBehaviorType}");
                    break;
            }

            return behavior;
        }

        private static TargetingBehaviorComponent CreateTargetingBehavior(EnemyBehaviorConfiguration config)
        {
            TargetingBehaviorComponent behavior = null;

            switch (config.TargetingBehaviorType)
            {
                case TargetingBehaviorType.Proximity:
                    behavior = new ProximityTargetingBehavior
                    {
                        DetectionRange = config.DetectionRange,
                        TargetUpdateInterval = config.TargetUpdateInterval
                    };
                    break;

                case TargetingBehaviorType.Priority:
                    behavior = new PriorityTargetingBehavior
                    {
                        DetectionRange = config.DetectionRange,
                        TargetUpdateInterval = config.TargetUpdateInterval,
                        PriorityShipTypes = config.PriorityShipTypes
                    };
                    break;

                // Additional targeting behavior types can be added here
                default:
                    Logger.Warning($"Unsupported targeting behavior type: {config.TargetingBehaviorType}");
                    break;
            }

            return behavior;
        }

        private static WeaponBehaviorComponent CreateWeaponBehavior(EnemyBehaviorConfiguration config)
        {
            WeaponBehaviorComponent behavior = null;

            switch (config.WeaponBehaviorType)
            {
                case WeaponBehaviorType.Opportunistic:
                    behavior = new OpportunisticWeaponBehavior
                    {
                        FiringAngleThreshold = config.FiringAngleThreshold,
                        OptimalFiringDistance = config.OptimalFiringDistance,
                        DistanceTolerance = config.DistanceTolerance
                    };
                    break;

                case WeaponBehaviorType.Strategic:
                    behavior = new StrategicWeaponBehavior
                    {
                        FiringAngleThreshold = config.FiringAngleThreshold,
                        OptimalFiringDistance = config.OptimalFiringDistance,
                        DistanceTolerance = config.DistanceTolerance,
                        BurstDuration = config.BurstDuration,
                        CooldownDuration = config.CooldownDuration
                    };
                    break;

                // Additional weapon behavior types can be added here
                default:
                    Logger.Warning($"Unsupported weapon behavior type: {config.WeaponBehaviorType}");
                    break;
            }

            return behavior;
        }
    }
}
