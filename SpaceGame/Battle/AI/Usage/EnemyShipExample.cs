using Godot;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;

namespace SpaceGame.Battle.AI.Usage
{
    /// <summary>
    /// Example class showing how to use the enemy behavior system with a ship entity.
    /// This would typically be used in a script attached to an enemy ship scene.
    /// </summary>
    public partial class EnemyShipExample : Node
    {
        [Export]
        public string BehaviorResourcePath { get; set; } = "res://SpaceGame/Resources/Battle/AI/InterceptorBehavior.tres";
        
        public override void _Ready()
        {
            // Get the parent ship entity
            var ship = GetParent<ShipEntity>();
            if (ship == null)
            {
                Logger.Error("EnemyShipExample must be a child of a ShipEntity");
                return;
            }
            
            // Attach the behavior controller to the ship
            BattleEntityFactory.Instance.AttachBehaviorControllerFromResource(ship, BehaviorResourcePath);
            
            // The ship now has a behavior controller with the specified behavior
            Logger.Info($"Attached behavior controller to ship {ship.Name} using behavior {BehaviorResourcePath}");
        }
    }
}
