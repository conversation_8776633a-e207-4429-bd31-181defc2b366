using Godot;
using System;
using System.Text.Json;
using SpaceGame.Helpers;
using SpaceGame.Scripts;
using SpaceGame.Utility.Interfaces;

namespace SpaceGame.Battle.AI
{
    [GlobalClass]
    public partial class EnemyBehaviorConfiguration : Resource, IJsonPopulatable
    {
        [Export]
        public string BehaviorName { get; set; } = "Default";
        
        [Export]
        public MovementBehaviorType MovementBehaviorType { get; set; } = MovementBehaviorType.Intercept;
        
        [Export]
        public TargetingBehaviorType TargetingBehaviorType { get; set; } = TargetingBehaviorType.Proximity;
        
        [Export]
        public WeaponBehaviorType WeaponBehaviorType { get; set; } = WeaponBehaviorType.Opportunistic;
        
        // Movement behavior parameters
        [ExportCategory("Movement Behavior Configuration")]
        [Export]
        public float ThrustStrength { get; set; } = 1.0f;
        
        [Export]
        public float RotationSpeed { get; set; } = 2.0f;
        
        [Export]
        public float OptimalDistance { get; set; } = 300f;
        
        [Export]
        public float CircleDistance { get; set; } = 400f;
        
        [Export]
        public float CircleSpeed { get; set; } = 1.0f;
        
        [Export]
        public bool ClockwiseCircling { get; set; } = true;
        
        // Targeting behavior parameters
        [ExportCategory("Targeting Behavior Configuration")]
        [Export]
        public float DetectionRange { get; set; } = 800f;
        
        [Export]
        public float TargetUpdateInterval { get; set; } = 1.0f;
        
        // Using a private field for the array since ShipType[] is not exportable
        private ShipType[] _priorityShipTypes = [0]; // Using 0 as default value
        
        // Property to access the array
        public ShipType[] PriorityShipTypes
        {
            get => _priorityShipTypes;
            set => _priorityShipTypes = value;
        }
        
        // Weapon behavior parameters
        [ExportCategory("Weapon Behavior Configuration")]
        [Export]
        public float FiringAngleThreshold { get; set; } = 0.2f;
        
        [Export]
        public float OptimalFiringDistance { get; set; } = 500f;
        
        [Export]
        public float DistanceTolerance { get; set; } = 200f;
        
        [Export]
        public float BurstDuration { get; set; } = 2.0f;
        
        [Export]
        public float CooldownDuration { get; set; } = 3.0f;
        
        public void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)
        {
            try
            {
                // Handle string properties
                if (jsonOverrides.TryGetProperty("BehaviorName", out JsonElement behaviorNameElement))
                {
                    BehaviorName = behaviorNameElement.GetString();
                }
                
                // Handle enum properties
                if (jsonOverrides.TryGetProperty("MovementBehaviorType", out JsonElement movementTypeElement))
                {
                    if (movementTypeElement.ValueKind == JsonValueKind.String)
                    {
                        if (Enum.TryParse<MovementBehaviorType>(movementTypeElement.GetString(), true, out var movementType))
                        {
                            MovementBehaviorType = movementType;
                        }
                    }
                    else if (movementTypeElement.ValueKind == JsonValueKind.Number)
                    {
                        MovementBehaviorType = (MovementBehaviorType)movementTypeElement.GetInt32();
                    }
                }
                
                if (jsonOverrides.TryGetProperty("TargetingBehaviorType", out JsonElement targetingTypeElement))
                {
                    if (targetingTypeElement.ValueKind == JsonValueKind.String)
                    {
                        if (Enum.TryParse<TargetingBehaviorType>(targetingTypeElement.GetString(), true, out var targetingType))
                        {
                            TargetingBehaviorType = targetingType;
                        }
                    }
                    else if (targetingTypeElement.ValueKind == JsonValueKind.Number)
                    {
                        TargetingBehaviorType = (TargetingBehaviorType)targetingTypeElement.GetInt32();
                    }
                }
                
                if (jsonOverrides.TryGetProperty("WeaponBehaviorType", out JsonElement weaponTypeElement))
                {
                    if (weaponTypeElement.ValueKind == JsonValueKind.String)
                    {
                        if (Enum.TryParse<WeaponBehaviorType>(weaponTypeElement.GetString(), true, out var weaponType))
                        {
                            WeaponBehaviorType = weaponType;
                        }
                    }
                    else if (weaponTypeElement.ValueKind == JsonValueKind.Number)
                    {
                        WeaponBehaviorType = (WeaponBehaviorType)weaponTypeElement.GetInt32();
                    }
                }
                
                // Handle float properties
                if (jsonOverrides.TryGetProperty("ThrustStrength", out JsonElement thrustElement))
                {
                    ThrustStrength = thrustElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("RotationSpeed", out JsonElement rotationElement))
                {
                    RotationSpeed = rotationElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("OptimalDistance", out JsonElement optDistElement))
                {
                    OptimalDistance = optDistElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("CircleDistance", out JsonElement circleDistElement))
                {
                    CircleDistance = circleDistElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("CircleSpeed", out JsonElement circleSpeedElement))
                {
                    CircleSpeed = circleSpeedElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("DetectionRange", out JsonElement detectionElement))
                {
                    DetectionRange = detectionElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("TargetUpdateInterval", out JsonElement updateIntervalElement))
                {
                    TargetUpdateInterval = updateIntervalElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("FiringAngleThreshold", out JsonElement firingAngleElement))
                {
                    FiringAngleThreshold = firingAngleElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("OptimalFiringDistance", out JsonElement optFiringDistElement))
                {
                    OptimalFiringDistance = optFiringDistElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("DistanceTolerance", out JsonElement distToleranceElement))
                {
                    DistanceTolerance = distToleranceElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("BurstDuration", out JsonElement burstElement))
                {
                    BurstDuration = burstElement.GetSingle();
                }
                
                if (jsonOverrides.TryGetProperty("CooldownDuration", out JsonElement cooldownElement))
                {
                    CooldownDuration = cooldownElement.GetSingle();
                }
                
                // Handle boolean properties
                if (jsonOverrides.TryGetProperty("ClockwiseCircling", out JsonElement clockwiseElement))
                {
                    ClockwiseCircling = clockwiseElement.GetBoolean();
                }
                
                // Handle array properties
                if (jsonOverrides.TryGetProperty("PriorityShipTypes", out JsonElement priorityTypesElement) && 
                    priorityTypesElement.ValueKind == JsonValueKind.Array)
                {
                    var shipTypes = new ShipType[priorityTypesElement.GetArrayLength()];
                    for (int i = 0; i < shipTypes.Length; i++)
                    {
                        var element = priorityTypesElement[i];
                        if (element.ValueKind == JsonValueKind.String)
                        {
                            if (Enum.TryParse<ShipType>(element.GetString(), true, out var shipType))
                            {
                                shipTypes[i] = shipType;
                            }
                        }
                        else if (element.ValueKind == JsonValueKind.Number)
                        {
                            shipTypes[i] = (ShipType)element.GetInt32();
                        }
                    }
                    PriorityShipTypes = shipTypes;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error populating EnemyBehaviorConfiguration from JSON: {ex.Message}", ex);
            }
        }
    }
}
