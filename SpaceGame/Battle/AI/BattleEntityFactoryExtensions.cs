using Godot;
using SpaceGame.Battle.Registry;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;

namespace SpaceGame.Battle.AI
{
    public static class BattleEntityFactoryExtensions
    {
        /// <summary>
        /// Attaches an AI behavior controller to an enemy ship based on a behavior configuration.
        /// </summary>
        /// <param name="factory">The BattleEntityFactory instance</param>
        /// <param name="ship">The ship to attach the behavior controller to</param>
        /// <param name="behaviorConfig">The behavior configuration to use</param>
        /// <returns>The created ShipBehaviorController, or null if failed</returns>
        public static ShipBehaviorController AttachBehaviorController(this BattleEntityFactory factory, ShipEntity ship, EnemyBehaviorConfiguration behaviorConfig)
        {
            if (ship == null || behaviorConfig == null)
            {
                Logger.Error("Cannot attach behavior controller. Ship or behavior configuration is null.");
                return null;
            }

            // Check if the ship already has a behavior controller
            var existingController = ship.GetNodeOrNull<ShipBehaviorController>("BehaviorController");
            if (existingController != null)
            {
                Logger.Warning($"Ship {ship.Name} already has a behavior controller. Removing it before attaching a new one.");
                existingController.QueueFree();
            }

            // Create a new behavior controller
            var controller = EnemyBehaviorFactory.CreateBehaviorController(behaviorConfig);
            if (controller == null)
            {
                Logger.Error($"Failed to create behavior controller for ship {ship.Name} using behavior {behaviorConfig.BehaviorName}.");
                return null;
            }

            // Set the name and add it to the ship
            controller.Name = "BehaviorController";
            ship.AddChild(controller);
            
            Logger.Debug($"Attached {behaviorConfig.BehaviorName} behavior controller to ship {ship.Name}.");
            return controller;
        }

        /// <summary>
        /// Attaches an AI behavior controller to an enemy ship based on a behavior configuration resource path.
        /// </summary>
        /// <param name="factory">The BattleEntityFactory instance</param>
        /// <param name="ship">The ship to attach the behavior controller to</param>
        /// <param name="behaviorResourcePath">The resource path to the behavior configuration</param>
        /// <returns>The created ShipBehaviorController, or null if failed</returns>
        public static ShipBehaviorController AttachBehaviorControllerFromResource(this BattleEntityFactory factory, ShipEntity ship, string behaviorResourcePath)
        {
            if (ship == null || string.IsNullOrEmpty(behaviorResourcePath))
            {
                Logger.Error("Cannot attach behavior controller. Ship is null or behavior resource path is empty.");
                return null;
            }

            // Load the behavior configuration resource
            var behaviorConfig = GD.Load<EnemyBehaviorConfiguration>(behaviorResourcePath);
            if (behaviorConfig == null)
            {
                Logger.Error($"Failed to load behavior configuration from path: {behaviorResourcePath}");
                return null;
            }

            return factory.AttachBehaviorController(ship, behaviorConfig);
        }
        
        /// <summary>
        /// Attaches a default behavior controller to an entity based on its type and configuration.
        /// </summary>
        /// <param name="factory">The BattleEntityFactory instance</param>
        /// <param name="entity">The entity to attach the behavior controller to</param>
        /// <returns>The created ShipBehaviorController, or null if failed or not applicable</returns>
        public static ShipBehaviorController AttachDefaultBehavior(this BattleEntityFactory factory, BaseBattleEntity entity)
        {
            // Only apply behaviors to ship entities
            if (entity == null || !(entity is ShipEntity ship))
            {
                return null;
            }
            
            // Try to get behavior from the ship's entity data
            EnemyBehaviorConfiguration behaviorConfig = null;
            
            // Check if the entity has ShipEntityData with a direct DefaultBehavior reference
            if (entity.EntityData is ShipEntityData shipData && shipData.DefaultBehavior != null)
            {
                behaviorConfig = shipData.DefaultBehavior;
                Logger.Debug($"Using direct DefaultBehavior from ShipEntityData for {entity.Name}");
            }
            // If no direct behavior, try to get one from the registry based on entity type
            else if (entity.EntityData != null)
            {
                string entityTypeId = "";
                
                // Determine entity type based on available information
                if (entity.EntityData is ShipEntityData shipEntityData)
                {
                    // Use the ship type as the identifier
                    entityTypeId = shipEntityData.ShipDetailType.ToString().ToLowerInvariant();
                }
                else
                {
                    // Use the base entity type as a fallback
                    entityTypeId = entity.EntityData.BaseType.ToString().ToLowerInvariant();
                }
                
                if (!string.IsNullOrEmpty(entityTypeId))
                {
                    var registry = BattleEntityRegistry.Instance;
                    if (registry != null)
                    {
                        behaviorConfig = registry.GetBehavior(entityTypeId);
                        if (behaviorConfig != null)
                        {
                            Logger.Debug($"Using behavior from registry for entity type {entityTypeId}");
                        }
                    }
                }
            }
            
            // If we found a behavior configuration, attach it
            if (behaviorConfig != null)
            {
                return factory.AttachBehaviorController(ship, behaviorConfig);
            }
            
            string entityType = "Unknown";
            if (entity.EntityData is ShipEntityData shipEntityTypeData)
            {
                entityType = shipEntityTypeData.ShipDetailType.ToString();
            }
            else if (entity.EntityData != null)
            {
                entityType = entity.EntityData.BaseType.ToString();
            }
            
            Logger.Warning($"No default behavior found for entity {entity.Name} (Type: {entityType})");
            return null;
        }
    }
}
