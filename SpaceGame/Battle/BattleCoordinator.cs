using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Events;
using System;
using System.Collections.Generic;

namespace SpaceGame.Battle;

[GlobalClass]
public partial class BattleCoordinator : Node
{
    [Export] private PackedScene? _spaceBattleScenePrefab; // Made nullable
    private BattleEntityFactory _entityFactory => BattleEntityFactory.Instance;

    private SpaceBattleScene? _currentBattleSceneInstance; // Made nullable
    private BattleManager? _currentBattleManagerInstance; // Made nullable
    private bool _isBattleInProgress = false;
    private int _playerShipsSpawned = 0; // Counter for player ship spawning positions

    public override void _Ready()
    {
        if (_entityFactory == null)
        {
            Logger.Error("BattleCoordinator requires a BattleEntityFactory to be assigned in the editor.");
            SetProcess(false); // Disable processing if not configured
            return;
        }
        
        // Subscribe to the StartBattle event
        BattleEvents.StartBattle += HandleStartBattleRequest;
    }

    /// <summary>
    /// Starts a new battle instance.
    /// </summary>
    /// <param name="playerFleet">Information about the player's fleet.</param>
    /// <param name="enemyConfig">Configuration for spawning enemy waves.</param>
    public void StartNewBattle(PlayerFleetInfo playerFleet, EnemySpawnConfig enemyConfig) // Changed from async Task to void
    {
        if (_spaceBattleScenePrefab == null)
        {
            Logger.Error("Space Battle Scene Prefab not set in BattleCoordinator.");
            return;
        }
        if (_entityFactory == null)
        {
            Logger.Error("EntityFactory is not set in BattleCoordinator. Cannot spawn player fleet.");
            return;
        }
        if (_currentBattleSceneInstance != null)
        {
            Logger.Warning("Attempting to start a new battle while one is already active. Cleaning up previous battle first."); // Changed Warn to Warning
            CleanupBattle();
        }

        _playerShipsSpawned = 0; // Reset spawn counter for this battle

        _currentBattleSceneInstance = _spaceBattleScenePrefab.Instantiate<SpaceBattleScene>();
        if (_currentBattleSceneInstance == null)
        {
            Logger.Error("Failed to instantiate Space Battle Scene.");
            return;
        }
        AddChild(_currentBattleSceneInstance);
        Logger.Info("Space Battle Scene instantiated.");

        _currentBattleSceneInstance.ExitRequested += HandleExitRequested;

        _currentBattleManagerInstance = new BattleManager();
        _currentBattleManagerInstance.Name = "BattleManager";
        _currentBattleSceneInstance.AddChild(_currentBattleManagerInstance);
        Logger.Info("Battle Manager instantiated and added to scene.");

        _currentBattleManagerInstance.BattleWon += HandleBattleWon;
        _currentBattleManagerInstance.BattleLost += HandleBattleLost;

        // Determine the flagship spawn position
        Vector2 flagshipSpawnPosition = GetNextPlayerSpawnPosition(); // Use the first position for the flagship
        Logger.Info($"Calculated flagship spawn position: {flagshipSpawnPosition}");

        // Initialize the battle, which will now also handle spawning the player fleet from GameState
        _currentBattleManagerInstance.InitializeBattle(_currentBattleSceneInstance, _entityFactory, enemyConfig, flagshipSpawnPosition);

        Logger.Info("Battle Initialization Sequence Complete.");
    }

    private Vector2 GetNextPlayerSpawnPosition()
    {
        // Simple sequential spawning for now, can be made more sophisticated
        // Example: Spawn in a line or a formation around a central point
        Vector2 basePosition = new Vector2(50, 50); // Arbitrary starting point
        float spacing = 100f; // Spacing between ships
        Vector2 position = basePosition + new Vector2(_playerShipsSpawned * spacing, 0);
        _playerShipsSpawned++;
        return position;
    }

    private void HandleBattleWon()
    {
        Logger.Info("Battle Coordinator: Battle Won!");
        CleanupBattle();
    }

    private void HandleBattleLost()
    {
        Logger.Info("Battle Coordinator: Battle Lost!");
        CleanupBattle();
    }

    private void HandleExitRequested()
    {
        Logger.Info("Exit requested, cleaning up battle.");
        CleanupBattle();
    }

    /// <summary>
    /// Handles the StartBattle event by creating a battle with the specified configuration.
    /// </summary>
    /// <param name="config">The battle configuration to use.</param>
    private void HandleStartBattleRequest(BattleConfig config)
    {
        if (_isBattleInProgress)
        {
            Logger.Warning("Cannot start a new battle while one is already in progress. Clean up the current battle first.");
            return;
        }
        
        if (config == null)
        {
            Logger.Error("Cannot start battle with null configuration");
            return;
        }
        
        Logger.Info($"Starting battle with config: {config.BattleId} (Type: {config.BattleType}, Difficulty: {config.DifficultyLevel})");
        
        // Create a simple player fleet based on the configuration
        var playerFleet = new PlayerFleetInfo
        {
            PlayerShipTypes = new List<string> { config.PlayerShipType }
        };
        
        // Create enemy spawn config based on the battle configuration
        var enemyConfig = CreateEnemySpawnConfig(config);
        
        // Start the battle
        StartNewBattle(playerFleet, enemyConfig);
        
        // Raise the BattleStarted event
        BattleEvents.RaiseBattleStarted(config);
        
        _isBattleInProgress = true;
    }
    
    /// <summary>
    /// Creates an enemy spawn configuration based on the battle configuration.
    /// </summary>
    /// <param name="config">The battle configuration to use.</param>
    /// <returns>An enemy spawn configuration.</returns>
    private EnemySpawnConfig CreateEnemySpawnConfig(BattleConfig config)
    {
        var enemyConfig = new EnemySpawnConfig();
        
        // Create enemy waves based on difficulty level
        int numWaves = Math.Max(1, config.DifficultyLevel / 2);
        int enemiesPerWave = Math.Max(1, config.MaxEnemyShips / numWaves);
        
        // Default enemy types - can be expanded based on game content
        var enemyTypes = new List<string> { "Scout", "Fighter" };
        
        // Create waves
        for (int i = 0; i < numWaves; i++)
        {
            var wave = new EnemyWave
            {
                NumberToSpawn = enemiesPerWave,
                EnemyShipTypes = enemyTypes,
                DelayBeforeSpawnSeconds = i * 5, // 5 second delay between waves
                SpawnIntervalSeconds = 1.5f // 1.5 second between enemy spawns
            };
            
            enemyConfig.Waves.Add(wave);
        }
        
        return enemyConfig;
    }
    
    private void CleanupBattle()
    {
        Logger.Info("Cleaning up battle scene...");
        if (_currentBattleManagerInstance != null)
        {
            _currentBattleManagerInstance.BattleWon -= HandleBattleWon;
            _currentBattleManagerInstance.BattleLost -= HandleBattleLost;
        }

        if (_currentBattleSceneInstance != null)
        {
            if (_currentBattleSceneInstance.IsConnected(SpaceBattleScene.SignalName.ExitRequested, Callable.From(HandleExitRequested)))
            {
                _currentBattleSceneInstance.ExitRequested -= HandleExitRequested;
            }

            if (IsInstanceValid(_currentBattleSceneInstance))
            {
                _currentBattleSceneInstance.QueueFree();
            }
            _currentBattleSceneInstance = null;
            _currentBattleManagerInstance = null;
        }
        
        _isBattleInProgress = false;
        Logger.Info("Battle cleanup complete.");
    }

    public override void _ExitTree()
    {
        CleanupBattle();
        
        // Unsubscribe from the StartBattle event
        BattleEvents.StartBattle -= HandleStartBattleRequest;
    }
}
