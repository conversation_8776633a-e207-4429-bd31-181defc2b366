using Godot;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using SpaceGame.Helpers;
using SpaceGame.Utility.Interfaces;

namespace SpaceGame.Utility
{
    /// <summary>
    /// Provides functionality to apply property overrides to Godot objects using the IJsonPopulatable interface.
    /// This is a more robust and type-safe approach compared to the original PropertyOverrideService.
    /// </summary>
    public static class PropertyOverrideServiceV2
    {
        /// <summary>
        /// Default JSON serializer options used for property overrides.
        /// </summary>
        public static readonly JsonSerializerOptions DefaultSerializerOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            AllowTrailingCommas = true,
            ReadCommentHandling = JsonCommentHandling.Skip,
            Converters = { new JsonStringEnumConverter() }
        };

        /// <summary>
        /// Applies property overrides to a target object, only modifying properties that are explicitly defined in the overrides dictionary.
        /// Uses IJsonPopulatable interface for complex objects when available.
        /// </summary>
        /// <param name="target">The GodotObject to apply overrides to</param>
        /// <param name="overrides">Dictionary of property names and their override values</param>
        /// <param name="serializerOptions">Optional custom JSON serializer options</param>
        public static void ApplyOverrides(GodotObject target, Dictionary<string, object> overrides, JsonSerializerOptions serializerOptions = null)
        {
            if (target == null)
            {
                Logger.Warning("PropertyOverrideServiceV2: Target object is null.");
                return;
            }

            if (overrides == null || overrides.Count == 0)
            {
                return;
            }

            var usedSerializerOptions = serializerOptions ?? DefaultSerializerOptions;
            Type targetType = target.GetType();

            foreach (var entry in overrides)
            {
                string propertyName = entry.Key;
                object propertyValue = entry.Value;

                try
                {
                    // Get property info with case-insensitive search
                    PropertyInfo propInfo = GetPropertyInfo(targetType, propertyName);
                    if (propInfo == null)
                    {
                        Logger.Warning($"PropertyOverrideServiceV2: Property '{propertyName}' not found on type '{targetType.Name}'.");
                        continue;
                    }

                    // Get the current value of the property
                    object currentValue = propInfo.GetValue(target);

                    // Handle IJsonPopulatable objects
                    if (currentValue is IJsonPopulatable jsonPopulatable && propertyValue != null)
                    {
                        JsonElement jsonElement;
                        
                        // Convert propertyValue to JsonElement if needed
                        if (propertyValue is JsonElement element)
                        {
                            jsonElement = element;
                        }
                        else
                        {
                            // Convert dictionary or other objects to JsonElement
                            string jsonString = JsonSerializer.Serialize(propertyValue, usedSerializerOptions);
                            jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonString, usedSerializerOptions);
                        }

                        // Use the IJsonPopulatable interface to populate the object
                        jsonPopulatable.PopulateFromJson(jsonElement, usedSerializerOptions);
                    }
                    // Handle collections
                    else if (IsCollection(propInfo.PropertyType) && propertyValue != null)
                    {
                        HandleCollectionOverrides(target, propInfo, propertyValue, usedSerializerOptions);
                    }
                    // Handle regular properties
                    else
                    {
                        // Convert and set the value directly
                        object convertedValue = ConvertValue(propertyValue, propInfo.PropertyType, usedSerializerOptions);
                        propInfo.SetValue(target, convertedValue);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"PropertyOverrideServiceV2: Error applying override for property '{propertyName}': {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Gets the PropertyInfo for a property with case-insensitive search through the inheritance hierarchy.
        /// </summary>
        private static PropertyInfo GetPropertyInfo(Type targetType, string propertyName)
        {
            PropertyInfo propInfo = null;
            
            try
            {
                // Look only at properties declared in the most derived type
                propInfo = targetType.GetProperty(propertyName, 
                    BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.IgnoreCase);
                
                // If not found in the most derived type, search base types
                if (propInfo == null)
                {
                    // Walk up the inheritance chain
                    Type currentType = targetType.BaseType;
                    while (propInfo == null && currentType != null)
                    {
                        propInfo = currentType.GetProperty(propertyName, 
                            BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.IgnoreCase);
                        currentType = currentType.BaseType;
                    }
                }
            }
            catch (AmbiguousMatchException)
            {
                Logger.Warning($"PropertyOverrideServiceV2: Ambiguous match for property '{propertyName}' on type '{targetType.Name}'. " +
                               $"This typically happens with the 'new' keyword. Using most derived version.");
                
                // Try to get the most specific version of the property
                propInfo = targetType.GetProperty(propertyName, 
                    BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.IgnoreCase);
            }

            return propInfo;
        }

        /// <summary>
        /// Determines if a type is a collection.
        /// </summary>
        private static bool IsCollection(Type type)
        {
            return typeof(ICollection).IsAssignableFrom(type) || 
                   (type.IsGenericType && typeof(ICollection<>).IsAssignableFrom(type.GetGenericTypeDefinition())) ||
                   typeof(Godot.Collections.Array).IsAssignableFrom(type);
        }

        /// <summary>
        /// Handles applying overrides to collection properties.
        /// </summary>
        private static void HandleCollectionOverrides(GodotObject target, PropertyInfo propInfo, object propertyValue, JsonSerializerOptions serializerOptions)
        {
            // Get the existing collection
            var existingCollection = propInfo.GetValue(target) as ICollection;
            if (existingCollection == null)
            {
                Logger.Warning($"PropertyOverrideServiceV2: Collection property '{propInfo.Name}' is null on target object.");
                return;
            }

            // Handle partial collection updates (list of dictionaries)
            if (propertyValue is List<Dictionary<string, object>> partialItemOverrides)
            {
                if (HandlePartialCollectionOverrides(existingCollection, partialItemOverrides, serializerOptions))
                {
                    return;
                }
            }

            // Convert propertyValue to JsonElement if needed
            JsonElement jsonElement;
            if (propertyValue is JsonElement element)
            {
                jsonElement = element;
            }
            else
            {
                try
                {
                    string jsonString = JsonSerializer.Serialize(propertyValue, serializerOptions);
                    jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonString, serializerOptions);
                }
                catch (Exception ex)
                {
                    Logger.Error($"PropertyOverrideServiceV2: Failed to convert collection override to JsonElement: {ex.Message}", ex);
                    return;
                }
            }

            // Only proceed if we have a JSON array
            if (jsonElement.ValueKind != JsonValueKind.Array)
            {
                Logger.Warning($"PropertyOverrideServiceV2: Expected array for collection property '{propInfo.Name}' but got {jsonElement.ValueKind}.");
                return;
            }

            // Handle different collection types
            if (existingCollection is Godot.Collections.Array godotArray)
            {
                HandleGodotArrayOverrides(godotArray, jsonElement, serializerOptions);
            }
            else if (existingCollection is IList list)
            {
                HandleIListOverrides(list, jsonElement, propInfo.PropertyType, serializerOptions);
            }
            else
            {
                Logger.Warning($"PropertyOverrideServiceV2: Unsupported collection type for property '{propInfo.Name}'.");
            }
        }

        /// <summary>
        /// Handles partial updates to collection items, where only some properties of each item are specified.
        /// </summary>
        private static bool HandlePartialCollectionOverrides(ICollection collection, List<Dictionary<string, object>> partialItemOverrides, JsonSerializerOptions serializerOptions)
        {
            // Handle Godot.Collections.Array
            if (collection is Godot.Collections.Array godotArray)
            {
                // Apply partial updates to each item
                for (int i = 0; i < Math.Min(partialItemOverrides.Count, godotArray.Count); i++)
                {
                    var itemOverrides = partialItemOverrides[i];
                    if (itemOverrides != null)
                    {
                        // Get the item from the array and ensure it's a GodotObject
                        var item = godotArray[i];
                        if (item.Obj is GodotObject itemObject)
                        {
                            // Apply partial overrides to this item
                            ApplyOverrides(itemObject, itemOverrides, serializerOptions);
                        }
                    }
                }
                return true;
            }
            
            // Handle IList (including List<T>)
            if (collection is IList list)
            {
                // Apply partial updates to each item
                for (int i = 0; i < Math.Min(partialItemOverrides.Count, list.Count); i++)
                {
                    var itemOverrides = partialItemOverrides[i];
                    if (itemOverrides != null)
                    {
                        // Get the item from the list and ensure it's a GodotObject
                        var item = list[i];
                        if (item is GodotObject itemObject)
                        {
                            // Apply partial overrides to this item
                            ApplyOverrides(itemObject, itemOverrides, serializerOptions);
                        }
                    }
                }
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// Handles overrides for Godot.Collections.Array.
        /// </summary>
        private static void HandleGodotArrayOverrides(Godot.Collections.Array godotArray, JsonElement jsonArray, JsonSerializerOptions serializerOptions)
        {
            int arrayLength = godotArray.Count;
            int jsonArrayLength = 0;
            foreach (var _ in jsonArray.EnumerateArray()) jsonArrayLength++;

            // Process existing items
            int i = 0;
            foreach (var jsonItem in jsonArray.EnumerateArray())
            {
                if (i < arrayLength)
                {
                    // Update existing item
                    var item = godotArray[i];
                    if (item.Obj is GodotObject godotObject && item.Obj is IJsonPopulatable jsonPopulatable)
                    {
                        // Use IJsonPopulatable interface if available
                        jsonPopulatable.PopulateFromJson(jsonItem, serializerOptions);
                    }
                    else if (item.Obj is GodotObject itemObject)
                    {
                        // Convert JsonElement to Dictionary and apply overrides
                        try
                        {
                            var itemOverrides = JsonSerializer.Deserialize<Dictionary<string, object>>(
                                jsonItem.GetRawText(), serializerOptions);
                            if (itemOverrides != null)
                            {
                                ApplyOverrides(itemObject, itemOverrides, serializerOptions);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"PropertyOverrideServiceV2: Error deserializing array item: {ex.Message}", ex);
                        }
                    }
                }
                // TODO: Handle adding new items if needed
                i++;
            }
        }

        /// <summary>
        /// Handles overrides for IList collections.
        /// </summary>
        private static void HandleIListOverrides(IList list, JsonElement jsonArray, Type listType, JsonSerializerOptions serializerOptions)
        {
            int listLength = list.Count;
            int jsonArrayLength = 0;
            foreach (var _ in jsonArray.EnumerateArray()) jsonArrayLength++;

            // Get the item type of the list
            Type itemType = GetListItemType(listType);
            if (itemType == null)
            {
                Logger.Warning("PropertyOverrideServiceV2: Could not determine list item type.");
                return;
            }

            // Process existing items
            int i = 0;
            foreach (var jsonItem in jsonArray.EnumerateArray())
            {
                if (i < listLength)
                {
                    // Update existing item
                    var item = list[i];
                    if (item is IJsonPopulatable jsonPopulatable)
                    {
                        // Use IJsonPopulatable interface if available
                        jsonPopulatable.PopulateFromJson(jsonItem, serializerOptions);
                    }
                    else if (item is GodotObject godotObject)
                    {
                        // Convert JsonElement to Dictionary and apply overrides
                        try
                        {
                            var itemOverrides = JsonSerializer.Deserialize<Dictionary<string, object>>(
                                jsonItem.GetRawText(), serializerOptions);
                            if (itemOverrides != null)
                            {
                                ApplyOverrides(godotObject, itemOverrides, serializerOptions);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"PropertyOverrideServiceV2: Error deserializing list item: {ex.Message}", ex);
                        }
                    }
                    else
                    {
                        // For simple types, try direct deserialization
                        try
                        {
                            object convertedValue = JsonSerializer.Deserialize(jsonItem.GetRawText(), itemType, serializerOptions);
                            list[i] = convertedValue;
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"PropertyOverrideServiceV2: Error deserializing simple list item: {ex.Message}", ex);
                        }
                    }
                }
                // TODO: Handle adding new items if needed
                i++;
            }
        }

        /// <summary>
        /// Gets the item type of a list.
        /// </summary>
        private static Type GetListItemType(Type listType)
        {
            // Handle generic collections like List<T>
            if (listType.IsGenericType)
            {
                return listType.GetGenericArguments()[0];
            }
            
            // Handle arrays
            if (listType.IsArray)
            {
                return listType.GetElementType();
            }
            
            // For non-generic collections, try to find the item type through interfaces
            foreach (Type interfaceType in listType.GetInterfaces())
            {
                if (interfaceType.IsGenericType && 
                    interfaceType.GetGenericTypeDefinition() == typeof(ICollection<>))
                {
                    return interfaceType.GetGenericArguments()[0];
                }
            }
            
            return null;
        }

        /// <summary>
        /// Converts a value to the specified target type.
        /// </summary>
        private static object ConvertValue(object value, Type targetType, JsonSerializerOptions serializerOptions)
        {
            if (value == null)
            {
                return targetType.IsValueType ? Activator.CreateInstance(targetType) : null;
            }

            Type valueType = value.GetType();

            // If types already match, no conversion needed
            if (targetType.IsAssignableFrom(valueType))
            {
                return value;
            }

            // Handle JsonElement conversion
            if (value is JsonElement jsonElement)
            {
                try
                {
                    // Deserialize the JsonElement to the target type
                    return JsonSerializer.Deserialize(jsonElement.GetRawText(), targetType, serializerOptions);
                }
                catch (Exception ex)
                {
                    Logger.Warning($"PropertyOverrideServiceV2: Failed to deserialize JsonElement to {targetType.Name}: {ex.Message}");
                    throw;
                }
            }

            // Handle other conversions
            try
            {
                // Try using a type converter
                var converter = System.ComponentModel.TypeDescriptor.GetConverter(targetType);
                if (converter.CanConvertFrom(valueType))
                {
                    return converter.ConvertFrom(value);
                }

                // Try direct conversion for compatible types
                return Convert.ChangeType(value, targetType);
            }
            catch (Exception ex)
            {
                Logger.Warning($"PropertyOverrideServiceV2: Failed to convert value '{value}' of type '{valueType.Name}' to '{targetType.Name}': {ex.Message}");
                throw;
            }
        }
    }
}
