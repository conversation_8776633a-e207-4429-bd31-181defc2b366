using Godot;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using SpaceGame.Helpers;

namespace SpaceGame.Utility
{
    public static class PropertyOverrideService
    {
        // Create a reusable JsonSerializerOptions with enum string converter
        private static readonly JsonSerializerOptions DefaultSerializerOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            AllowTrailingCommas = true,
            ReadCommentHandling = JsonCommentHandling.Skip,
            Converters = { new JsonStringEnumConverter() }
        };
        
        /// <summary>
        /// Applies property overrides to a target object, only modifying properties that are explicitly defined in the overrides dictionary.
        /// All other properties remain untouched, including collections.
        /// </summary>
        /// <param name="target">The GodotObject to apply overrides to</param>
        /// <param name="overrides">Dictionary of property names and their override values</param>
        
        public static void ApplyOverrides(GodotObject target, Dictionary<string, object> overrides)
        {
            if (target == null)
            {
                Logger.Warning("PropertyOverrideService: Target object is null.");
                return;
            }

            if (overrides == null || overrides.Count == 0)
            {
                // Logger.Warning("PropertyOverrideService: Overrides dictionary is null or empty, no overrides applied."); // Optional: Too verbose if an entity simply has no overrides.
                return;
            }

            Type targetType = target.GetType();

            foreach (var entry in overrides)
            {
                string propertyName = entry.Key;
                object propertyValue = entry.Value;

                // Try to get the property directly from the most derived class first
                PropertyInfo propInfo = null;
                try
                {
                    // Look only at properties declared in the most derived type
                    propInfo = targetType.GetProperty(propertyName, 
                        BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.IgnoreCase);
                    
                    // If not found in the most derived type, search base types
                    if (propInfo == null)
                    {
                        // Walk up the inheritance chain
                        Type currentType = targetType.BaseType;
                        while (propInfo == null && currentType != null)
                        {
                            propInfo = currentType.GetProperty(propertyName, 
                                BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.IgnoreCase);
                            currentType = currentType.BaseType;
                        }
                    }
                }
                catch (AmbiguousMatchException)
                {
                    Logger.Warning($"PropertyOverrideService: Ambiguous match for property '{propertyName}' on type '{targetType.Name}'. " +
                                   $"This typically happens with the 'new' keyword. Using most derived version.");
                    
                    // Try to get the most specific version of the property
                    propInfo = targetType.GetProperty(propertyName, 
                        BindingFlags.Instance | BindingFlags.Public | BindingFlags.DeclaredOnly | BindingFlags.IgnoreCase);
                }

                if (propInfo == null)
                {
                    Logger.Warning($"PropertyOverrideService: Property '{propertyName}' not found on type '{targetType.Name}'.");
                    continue;
                }

                if (propertyValue is Dictionary<string, object> nestedOverrides)
                {
                    // Handle nested overrides
                    if (!propInfo.CanRead)
                    {
                        Logger.Warning($"PropertyOverrideService: Property '{propertyName}' on type '{targetType.Name}' is not readable (needed to get sub-object for nested overrides).");
                        continue;
                    }

                    object subObject = propInfo.GetValue(target);
                    if (subObject == null)
                    {
                        // Attempt to create an instance if it's a Resource and null
                        if (typeof(Resource).IsAssignableFrom(propInfo.PropertyType) && propInfo.CanWrite)
                        {
                            try
                            {
                                subObject = Activator.CreateInstance(propInfo.PropertyType);
                                propInfo.SetValue(target, subObject);
                                Logger.Debug($"PropertyOverrideService: Created and assigned new instance of {propInfo.PropertyType.Name} for '{propertyName}'.");
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"PropertyOverrideService: Failed to create instance of {propInfo.PropertyType.Name} for '{propertyName}'. {ex.Message}");
                                continue;
                            }
                        }
                        else
                        {
                            Logger.Warning($"PropertyOverrideService: Sub-object '{propertyName}' on type '{targetType.Name}' is null and cannot be auto-instantiated. Cannot apply nested overrides.");
                            continue;
                        }
                    }

                    if (subObject is GodotObject godotSubObject)
                    {
                        // Only apply overrides to properties explicitly defined in the nested overrides
                        ApplyOverrides(godotSubObject, nestedOverrides); // Recursive call
                    }
                    else
                    {
                        // This case might occur if a property is a plain C# class not deriving from GodotObject.
                        // For now, we primarily expect GodotObject-derived types (like Resource) for nesting.
                        Logger.Warning($"PropertyOverrideService: Sub-object '{propertyName}' of type '{subObject.GetType().Name}' is not a GodotObject. Nested overrides might not apply as expected unless custom handling is added.");
                    }
                }
                else
                {
                    // Handle direct property setting
                    if (!propInfo.CanWrite)
                    {
                        Logger.Warning($"PropertyOverrideService: Property '{propertyName}' on type '{targetType.Name}' is not writable.");
                        continue;
                    }

                    try
                    {
                        // Special handling for collections to support partial item overrides
                        if (typeof(ICollection).IsAssignableFrom(propInfo.PropertyType))
                        {
                            // Handle collections with dictionaries for partial updates
                            bool handledPartialUpdates = false;
                            
                            // Handle List<Dictionary<string, object>>
                            if (propertyValue is List<Dictionary<string, object>> listPartialOverrides)
                            {
                                handledPartialUpdates = HandlePartialCollectionOverrides(target, propInfo, listPartialOverrides);
                            }
                            // Handle Godot.Collections.Array
                            else if (propertyValue is Godot.Collections.Array godotArrayOverrides)
                            {
                                // Convert Godot.Collections.Array to a list of dictionaries if possible
                                var dictList = new List<Dictionary<string, object>>();
                                foreach (var item in godotArrayOverrides)
                                {
                                    // Handle Godot.Collections.Dictionary
                                    if (item.Obj is Godot.Collections.Dictionary godotDict)
                                    {
                                        // Convert Godot.Collections.Dictionary to Dictionary<string, object>
                                        var dict = new Dictionary<string, object>();
                                        foreach (var key in godotDict.Keys)
                                        {
                                            // Convert key to string if possible
                                            string strKey = key.ToString();
                                            if (!string.IsNullOrEmpty(strKey))
                                            {
                                                dict[strKey] = godotDict[key];
                                            }
                                        }
                                        dictList.Add(dict);
                                    }
                                }
                                
                                if (dictList.Count > 0)
                                {
                                    handledPartialUpdates = HandlePartialCollectionOverrides(target, propInfo, dictList);
                                }
                            }
                            
                            // If we've already handled the partial updates, skip to the next property
                            if (handledPartialUpdates)
                            {
                                continue;
                            }
                            
                            // For regular collections or when partial updates aren't specified,
                            // replace the entire collection
                            object valueToSet = ConvertValue(propertyValue, propInfo.PropertyType);
                            propInfo.SetValue(target, valueToSet);
                        }
                        else
                        {
                            // For non-collections, just set the value directly
                            object valueToSet = ConvertValue(propertyValue, propInfo.PropertyType);
                            propInfo.SetValue(target, valueToSet);
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"PropertyOverrideService: Error setting property '{propertyName}' on type '{targetType.Name}': {ex.Message}");
                    }
                }
            }
        }

        private static object ConvertValue(object value, Type targetType)
        {
            if (value == null) 
            {
                if (targetType.IsClass || Nullable.GetUnderlyingType(targetType) != null)
                    return null;
                throw new ArgumentNullException(nameof(value), $"Cannot assign null to non-nullable type {targetType.Name}");
            }

            Type valueType = value.GetType();

            if (targetType.IsAssignableFrom(valueType))
            {
                return value;
            }

            // Special handling for enum types early in the process
            if (targetType.IsEnum)
            {
                if (value is string stringValue)
                {
                    try
                    {
                        return Enum.Parse(targetType, stringValue, true);
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"PropertyOverrideService: Failed to parse enum value '{stringValue}' for type {targetType.Name}. Error: {ex.Message}");
                        // Continue to other conversion methods
                    }
                }
                else if (valueType.IsNumeric())
                {
                    try
                    {
                        return Enum.ToObject(targetType, Convert.ChangeType(value, typeof(int), CultureInfo.InvariantCulture));
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"PropertyOverrideService: Failed to convert numeric value to enum type {targetType.Name}. Error: {ex.Message}");
                        // Continue to other conversion methods
                    }
                }
            }

            // Handle JsonElement specifically, as it's a common type from System.Text.Json parsing
            if (value is JsonElement jsonElement)
            {
                // For complex objects, try direct serialization to the target type first
                if (jsonElement.ValueKind == JsonValueKind.Object && !targetType.IsPrimitive && targetType != typeof(string))
                {
                    try
                    {
                        object result = JsonSerializer.Deserialize(jsonElement.GetRawText(), targetType, DefaultSerializerOptions);
                        if (result != null)
                        {
                            return result;
                        }
                    }
                    catch (JsonException ex)
                    {
                        Logger.Warning($"PropertyOverrideService: JsonException during complex object JsonElement.Deserialize for type {targetType.Name}. Error: {ex.Message}. Will try specific type conversions.");
                    }
                }

                // Godot-specific type handling
                if (targetType == typeof(Color) && jsonElement.ValueKind == JsonValueKind.String)
                {
                    try
                    {
                        return new Color(jsonElement.GetString());
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"PropertyOverrideService: Could not parse Color from JsonElement string. Value: {jsonElement.GetString()}. Error: {ex.Message}");
                    }
                }
                
                // Handle Vector2 and Vector3 special cases
                if (targetType == typeof(Vector2))
                {
                    // From object: { "x": val, "y": val }
                    if (jsonElement.ValueKind == JsonValueKind.Object &&
                        jsonElement.TryGetProperty("x", out var xProp) && jsonElement.TryGetProperty("y", out var yProp) &&
                        xProp.TryGetSingle(out float xObj) && yProp.TryGetSingle(out float yObj))
                    {
                        return new Vector2(xObj, yObj);
                    }
                    // From array: [ val, val ]
                    else if (jsonElement.ValueKind == JsonValueKind.Array && jsonElement.GetArrayLength() == 2)
                    {
                        try
                        {
                            float[] arr = jsonElement.Deserialize<float[]>(DefaultSerializerOptions);
                            if (arr != null)
                                return new Vector2(arr[0], arr[1]);
                        }
                        catch (Exception ex)
                        {
                            Logger.Warning($"PropertyOverrideService: Failed to deserialize array to Vector2. Error: {ex.Message}");
                        }
                    }
                }
                else if (targetType == typeof(Vector3))
                {
                    // From object: { "x": val, "y": val, "z": val }
                    if (jsonElement.ValueKind == JsonValueKind.Object &&
                        jsonElement.TryGetProperty("x", out var xProp) && jsonElement.TryGetProperty("y", out var yProp) && jsonElement.TryGetProperty("z", out var zProp) &&
                        xProp.TryGetSingle(out float xObj) && yProp.TryGetSingle(out float yObj) && zProp.TryGetSingle(out float zObj))
                    {
                        return new Vector3(xObj, yObj, zObj);
                    }
                    // From array: [ val, val, val ]
                    else if (jsonElement.ValueKind == JsonValueKind.Array && jsonElement.GetArrayLength() == 3)
                    {
                        try
                        {
                            float[] arr = jsonElement.Deserialize<float[]>(DefaultSerializerOptions);
                            if (arr != null)
                                return new Vector3(arr[0], arr[1], arr[2]);
                        }
                        catch (Exception ex)
                        {
                            Logger.Warning($"PropertyOverrideService: Failed to deserialize array to Vector3. Error: {ex.Message}");
                        }
                    }
                }

                // Handle primitive types based on JsonElement's ValueKind
                if (jsonElement.ValueKind == JsonValueKind.String) 
                {
                    string stringValue = jsonElement.GetString();
                    
                    // Check for enum type again (double-checking here in case the earlier check was bypassed)
                    if (targetType.IsEnum)
                    {
                        try
                        {
                            return Enum.Parse(targetType, stringValue, true);
                        }
                        catch (Exception) { /* Already logged above, continue to other conversions */ }
                    }
                    
                    // Convert string to various numeric types
                    try
                    {
                        if (targetType == typeof(float)) return float.Parse(stringValue, CultureInfo.InvariantCulture);
                        if (targetType == typeof(double)) return double.Parse(stringValue, CultureInfo.InvariantCulture);
                        if (targetType == typeof(int)) return int.Parse(stringValue, CultureInfo.InvariantCulture);
                        if (targetType == typeof(long)) return long.Parse(stringValue, CultureInfo.InvariantCulture);
                        if (targetType == typeof(bool)) return bool.Parse(stringValue);
                        if (targetType == typeof(decimal)) return decimal.Parse(stringValue, CultureInfo.InvariantCulture);
                    }
                    catch (Exception) { /* Continue to other conversion attempts */ }
                    
                    return stringValue; // Return as string if no conversion succeeded
                }
                else if (jsonElement.ValueKind == JsonValueKind.Number)
                {
                    if (targetType == typeof(int)) return jsonElement.GetInt32();
                    if (targetType == typeof(long)) return jsonElement.GetInt64();
                    if (targetType == typeof(float)) return jsonElement.GetSingle();
                    if (targetType == typeof(double)) return jsonElement.GetDouble();
                    if (targetType == typeof(decimal)) return jsonElement.GetDecimal();
                    
                    // If the target type is an enum, try converting from numeric value
                    if (targetType.IsEnum)
                    {
                        try
                        {
                            return Enum.ToObject(targetType, jsonElement.GetInt32());
                        }
                        catch (Exception) { /* Already logged above, continue to other conversions */ }
                    }
                }
                else if (jsonElement.ValueKind == JsonValueKind.True) return true;
                else if (jsonElement.ValueKind == JsonValueKind.False) return false;
            }
            
            // Handle plain string values
            if (value is string sVal)
            {
                try
                {
                    // Check for enum type first
                    if (targetType.IsEnum)
                    {
                        return Enum.Parse(targetType, sVal, true);
                    }

                    if (targetType == typeof(float)) return float.Parse(sVal, CultureInfo.InvariantCulture);
                    if (targetType == typeof(double)) return double.Parse(sVal, CultureInfo.InvariantCulture);
                    if (targetType == typeof(int)) return int.Parse(sVal, CultureInfo.InvariantCulture);
                    if (targetType == typeof(long)) return long.Parse(sVal, CultureInfo.InvariantCulture);
                    if (targetType == typeof(bool)) return bool.Parse(sVal);
                    if (targetType == typeof(decimal)) return decimal.Parse(sVal, CultureInfo.InvariantCulture);
                }
                catch (FormatException fe)
                {
                    Logger.Warning($"PropertyOverrideService: FormatException converting string '{sVal}' to {targetType.Name}. Error: {fe.Message}");
                }
            }

            // Try using TypeConverter if available
            TypeConverter converter = TypeDescriptor.GetConverter(targetType);
            if (converter != null && converter.CanConvertFrom(valueType))
            {
                try 
                {
                    return converter.ConvertFrom(null, CultureInfo.InvariantCulture, value);
                }
                catch (Exception ex)
                {
                    Logger.Warning($"PropertyOverrideService: TypeConverter failed to convert {valueType.Name} to {targetType.Name} for value '{value}'. Error: {ex.Message}. Falling back to System.Convert.");
                }
            }

            // Last resort: try direct System.Convert
            try
            {
                 return Convert.ChangeType(value, targetType, CultureInfo.InvariantCulture);
            }
            catch (InvalidCastException ice)
            {
                Logger.Warning($"PropertyOverrideService: InvalidCastException during Convert.ChangeType from {valueType.Name} to {targetType.Name} for value '{value}'. Error: {ice.Message}");
                 throw;
            }
            catch (Exception ex)
            {
                Logger.Warning($"PropertyOverrideService: Failed to convert value '{value}' of type '{valueType.Name}' to '{targetType.Name}'. Exception: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Handles partial updates to collection items, where only some properties of each item are specified.
        /// </summary>
        /// <param name="target">The target object containing the collection property</param>
        /// <param name="propInfo">PropertyInfo for the collection property</param>
        /// <param name="partialItemOverrides">List of dictionaries containing partial overrides for each item</param>
        /// <returns>True if partial updates were handled, false otherwise</returns>
        private static bool HandlePartialCollectionOverrides(GodotObject target, PropertyInfo propInfo, List<Dictionary<string, object>> partialItemOverrides)
        {
            // Get the existing collection
            var existingCollection = propInfo.GetValue(target) as ICollection;
            if (existingCollection == null)
            {
                return false;
            }
            
            // Handle Godot.Collections.Array
            if (existingCollection is Godot.Collections.Array godotArray)
            {
                // Apply partial updates to each item
                for (int i = 0; i < Math.Min(partialItemOverrides.Count, godotArray.Count); i++)
                {
                    var itemOverrides = partialItemOverrides[i];
                    if (itemOverrides != null)
                    {
                        // Get the item from the array and ensure it's a GodotObject
                        var item = godotArray[i];
                        if (item.Obj is GodotObject itemObject)
                        {
                            // Apply partial overrides to this item
                            ApplyOverrides(itemObject, itemOverrides);
                        }
                    }
                }
                return true;
            }
            
            // Handle IList (including List<T>)
            if (existingCollection is System.Collections.IList list)
            {
                // Apply partial updates to each item
                for (int i = 0; i < Math.Min(partialItemOverrides.Count, list.Count); i++)
                {
                    var itemOverrides = partialItemOverrides[i];
                    if (itemOverrides != null)
                    {
                        // Get the item from the list and ensure it's a GodotObject
                        var item = list[i];
                        if (item is GodotObject itemObject)
                        {
                            // Apply partial overrides to this item
                            ApplyOverrides(itemObject, itemOverrides);
                        }
                    }
                }
                return true;
            }
            
            return false;
        }
    }
    
    // Helper extension method for numeric type checking
    internal static class TypeExtensions
    {
        public static bool IsNumeric(this Type type)
        {
            if (type == null) return false;
            
            switch (Type.GetTypeCode(type))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    return true;
                default:
                    return false;
            }
        }
    }
}
