using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SpaceGame.Utility
{
    /// <summary>
    /// Custom JsonSerializer to handle Dictionary<string, object> where 'object'
    /// can be various primitive types, Godot types (like Vector2, Color represented as strings or objects),
    /// or nested structures that System.Text.Json might parse into JsonElement.
    /// This converter attempts to deserialize into common C#/.NET types and leaves others as JsonElement
    /// for downstream converters or manual handling (like in PropertyOverrideService).
    /// </summary>
    public class GodotDictionaryConverter : JsonConverter<Dictionary<string, object>>
    {
        public override Dictionary<string, object> Read(
            ref Utf8JsonReader reader, 
            Type typeToConvert,
            JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartObject)
            {
                throw new JsonException("Expected StartObject token");
            }

            var dictionary = new Dictionary<string, object>();

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return dictionary;
                }

                if (reader.TokenType != JsonTokenType.PropertyName)
                {
                    throw new JsonException("Expected PropertyName token");
                }

                string propertyName = reader.GetString();
                reader.Read(); // Move to the value token

                dictionary[propertyName] = DeserializeValue(ref reader, options);
            }

            throw new JsonException("Expected EndObject token");
        }

        private object DeserializeValue(ref Utf8JsonReader reader, JsonSerializerOptions options)
        {
            switch (reader.TokenType)
            {
                case JsonTokenType.String:
                    return reader.GetString();
                case JsonTokenType.Number:
                    if (reader.TryGetInt64(out long l)) return l;
                    if (reader.TryGetDouble(out double d)) return d; 
                    throw new JsonException("Cannot parse number."); 
                case JsonTokenType.True:
                    return true;
                case JsonTokenType.False:
                    return false;
                case JsonTokenType.Null:
                    return null;
                case JsonTokenType.StartObject:
                    return Read(ref reader, typeof(Dictionary<string, object>), options); 
                case JsonTokenType.StartArray:
                    return JsonSerializer.Deserialize<List<object>>(ref reader, options);
                default:
                    throw new JsonException($"Unexpected token type: {reader.TokenType}");
            }
        }

        public override void Write(Utf8JsonWriter writer, Dictionary<string, object> value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();
            foreach (KeyValuePair<string, object> kvp in value)
            {
                writer.WritePropertyName(kvp.Key);
                JsonSerializer.Serialize(writer, kvp.Value, kvp.Value?.GetType() ?? typeof(object), options);
            }
            writer.WriteEndObject();
        }
    }
}
