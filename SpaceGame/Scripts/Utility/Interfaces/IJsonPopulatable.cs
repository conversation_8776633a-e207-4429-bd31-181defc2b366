using System.Text.Json;

namespace SpaceGame.Utility.Interfaces
{
    /// <summary>
    /// Interface for objects that can be populated from JSON data.
    /// Implementing this interface allows an object to handle its own JSON deserialization
    /// for property overrides, reducing the need for reflection.
    /// </summary>
    public interface IJsonPopulatable
    {
        /// <summary>
        /// Populates the object's properties from JSON data.
        /// </summary>
        /// <param name="jsonOverrides">The JSON element containing override values</param>
        /// <param name="serializerOptions">Options for JSON serialization/deserialization</param>
        void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions);
    }
}