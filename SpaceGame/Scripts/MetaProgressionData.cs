using System.Collections.Generic;

namespace SpaceGame.Scripts;

using Godot;
using Godot.Collections;

public class MetaProgressionData
{
    public int TechPoints { get; set; } = 0; // Example currency

    // --- Unlocked Global Stat Modifiers from Meta Progression ---
    public List<StatModifierData> GlobalModifiers { get; set; } = new();

    // Optional: Track specific researched techs, unlocked commanders, etc.
    // [Export] public Godot.Collections.Array<string> UnlockedTechIDs { get; set; } = new Array<string>();
}