namespace SpaceGame.Scripts;

using Godot;
using System;
using System.Collections.Generic; // For List in ToString

// Container for stats related to non-weapon systems/equipment.
// Not a Resource itself, but a data container used by SystemData and FleetShipInstance.
[GlobalClass]
public partial class SystemStatsBlueprint : Resource
{
    // --- Cloaking ---
    [Export] public float CloakEnergyDrain { get; set; } = 0f;
    [Export] public float CloakStealthValue { get; set; } = 0f; // 0 = No cloak effect
    [Export] public float CloakSpeedModifier { get; set; } = 1.0f; // 1.0 = No speed change

    // --- Shield Booster ---
    [Export] public float ShieldBoostAmount { get; set; } = 0f; // Amount healed
    [Export] public float ShieldBoostCost { get; set; } = 0f; // Energy cost
    [Export] public float ShieldBoostCooldown { get; set; } = 0f; // Cooldown in seconds

    // --- Sensor Array ---
    [Export] public float SensorBoostRange { get; set; } = 0f; // Added range
    [Export] public float SensorBoostCost { get; set; } = 0f; // Energy cost

    // --- Repair Drones ---
    [Export] public float RepairRate { get; set; } = 0f; // HP/sec
    [Export] public float RepairEnergyCost { get; set; } = 0f; // Energy cost/sec

    // --- Tractor Beam ---
    [Export] public float TractorStrength { get; set; } = 0f;
    [Export] public float TractorRange { get; set; } = 0f;
    [Export] public float TractorCost { get; set; } = 0f;

    // Add other system stats fields here...

    public SystemStatsBlueprint() { } // Needed for initialization
}