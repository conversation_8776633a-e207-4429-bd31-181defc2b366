using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;
using SpaceGame.State;
using SpaceGame.Universe.Model;

namespace SpaceGame.Scripts.Managers;

public partial class GameStateManager
{
    private Dictionary<FactionId, FactionState> _factions => _gameState.Factions;
    
    /// <summary>
    /// Gets all factions in the game.
    /// </summary>
    /// <returns>An enumerable of all factions.</returns>
    public IEnumerable<FactionState> GetAllFactions()
    {
        return _factions.Values;
    }
    
    /// <summary>
    /// Gets a faction by its ID.
    /// </summary>
    /// <param name="id">The ID of the faction to get.</param>
    /// <returns>The faction with the specified ID, or null if not found.</returns>
    public FactionState? GetFactionById(FactionId id)
    {
        return _factions.GetValueOrDefault(id);
    }

    /// <summary>
    /// Gets the player faction.
    /// </summary>
    /// <returns>The player faction, or null if not found.</returns>
    public FactionState? GetPlayerFaction()
    {
        // Find the faction marked as the player faction
        return _factions.Values.FirstOrDefault(f => f.IsPlayerFaction);
    }

    /// <summary>
    /// Adds or updates a faction in the game state.
    /// </summary>
    /// <param name="faction">The faction to add or update.</param>
    public void AddOrUpdateFaction(FactionState faction)
    {
        _factions[faction.Id] = faction;
    }
    
    /// <summary>
    /// Sets the specified faction as the player faction, and ensures no other faction is marked as the player faction.
    /// </summary>
    /// <param name="factionId">The ID of the faction to set as the player faction.</param>
    /// <returns>True if successful, false if the faction wasn't found.</returns>
    public bool SetPlayerFaction(FactionId factionId)
    {
        var playerFaction = GetPlayerFaction();
        if (playerFaction?.Id == factionId)
        {
            Logger.Info($"Faction {factionId} is already the player faction");
            return true;
        }

        playerFaction?.SetPlayerFaction(false);

        var faction = GetFactionById(factionId);
        if (faction == null)
        {
            Logger.Warning($"Could not find faction with ID {factionId} to set as player faction.");
            return false;
        }

        faction.SetPlayerFaction(true);
        return true;
    }
    
    
    /// <summary>
    /// Checks if a faction is the player's faction.
    /// </summary>
    /// <param name="factionId">The ID of the faction to check.</param>
    /// <returns>True if the faction is the player's faction, false otherwise.</returns>
    public bool IsPlayerFaction(string factionId)
    {
        var faction = GetFactionById(factionId);
        return faction is { IsPlayerFaction: true };
    }
    
    /// <summary>
    /// Creates a new faction with the specified properties.
    /// </summary>
    /// <param name="displayName">The display name of the faction.</param>
    /// <param name="type">The faction type.</param>
    /// <param name="playerRelationship">The diplomatic relationship level with the player faction.</param>
    /// <param name="color">The color used to represent this faction in the UI.</param>
    /// <param name="description">A description of this faction.</param>
    /// <param name="isPlayerFaction">Whether this faction is controlled by the player.</param>
    /// <returns>The newly created faction.</returns>
    public FactionState CreateFaction(string displayName, FactionType type, RelationshipLevel playerRelationship,
        Color color, string description = "", bool isPlayerFaction = false)
    {
        var faction = new Faction(displayName, type, playerRelationship, color, description, isPlayerFaction);

        // If this is a player faction, clear the player flag from all other factions
        if (isPlayerFaction)
        {
            var playerFaction = GetPlayerFaction();
            playerFaction?.SetPlayerFaction(false);
        }

        var newFaction = new FactionState(faction, _gameState.CurrentUniverse);
        
        AddOrUpdateFaction(newFaction);
        Logger.Info($"Created and added new faction: {displayName} ({faction.Id})");

        return newFaction;
    }
    
    /// <summary>
    /// Deletes a faction from the game state.
    /// </summary>
    /// <param name="id">The ID of the faction to delete.</param>
    /// <returns>True if the faction was deleted, false if it wasn't found.</returns>
    public bool DeleteFaction(FactionId id)
    {
        var faction = GetFactionById(id);
        if (faction == null)
        {
            Logger.Warning($"Could not find faction with ID {id} to delete.");
            return false;
        }
        
        // Remove the faction
        _factions.Remove(id);
            
        Logger.Info($"Deleted faction: {faction.Name} ({faction.Id})");
            
        // Publish faction deleted event
        StaticFactionEvents.RaiseFactionDeleted(faction);
            
        return true;
    }
    
    /// <summary>
    /// Gets the celestial bodies controlled by a faction.
    /// </summary>
    /// <param name="factionId">The ID of the faction to get controlled celestial bodies for.</param>
    /// <returns>A list of celestial bodies controlled by the specified faction, or an empty list if the faction wasn't found.</returns>
    public IReadOnlyList<CelestialBodyData> GetFactionPlanets(FactionId factionId)
    {
        var faction = GetFactionById(factionId);
        if (faction != null)
        {
            return faction.ControlledPlanets;
        } else {
            Logger.Warning($"Could not find faction with ID {factionId} to get controlled planets.");
            return new List<CelestialBodyData>();
        }
    }
    
    /// <summary>
    /// Removes a planet from a faction's control.
    /// </summary>
    /// <param name="factionId">The ID of the faction to remove the planet from.</param>
    /// <param name="planetId">The ID of the planet to remove.</param>
    /// <returns>True if the planet was removed successfully, false otherwise.</returns>
    public bool RemovePlanetFromFaction(FactionId factionId, CelestialBodyId planetId)
    {
        FactionState? faction = GetFactionById(factionId);
        if (faction == null)
        {
            Logger.Warning($"Could not find faction with ID {factionId} to remove planet from.");
            return false;
        }
        
        var controlledPlanet = faction.ControlledPlanetsById.GetValueOrDefault(planetId);
        if (controlledPlanet == null)
        {
            Logger.Warning($"Planet {planetId} is not controlled by faction {faction.Name}.");
            return false;
        }

        if (faction.RemoveControlledPlanet(planetId))
        {
            StaticFactionEvents.RaisePlanetControlChanged(controlledPlanet, factionId, null);
            Logger.Info($"Removed planet {planetId} from faction {faction.Name}'s control");
        }
        
        Logger.Debug($"Published PlanetControlChangedEvent for planet {planetId} from faction {factionId} to null");
        return true;
    }

    public bool AssignPlanetToFaction(FactionId factionId, CelestialBodyData celestialBody)
    {
        FactionState? faction = GetFactionById(factionId);
        if (faction == null)
        {
            Logger.Warning($"Could not find faction with ID {factionId} to assign planet to.");
            return false;
        }

        var previousFactionId = celestialBody.FactionId;
        
        if (previousFactionId != null)
        {
            var previousFactionObject = GetFactionById(previousFactionId);
            previousFactionObject?.RemoveControlledPlanet(celestialBody.Id);
        }

        faction.AddControlledPlanet(celestialBody);
        // AddOrUpdateFaction(faction);
        
        StaticFactionEvents.RaisePlanetControlChanged(celestialBody, previousFactionId, factionId);
        Logger.Debug($"Published PlanetControlChangedEvent for planet {celestialBody.Id} from {previousFactionId} to {factionId} ({faction.Type})");
        
        return true;
    }
    
    
    /// <summary>
    /// Clears all factions from the game state.
    /// </summary>
    public void ClearAllFactions()
    {
        var factionIds = _factions.Keys.ToList();
        foreach (var id in factionIds)
        {
            DeleteFaction(id);
        }
        Logger.Info("All factions cleared.");
    }
    
    /// <summary>
    /// Resets the FactionManager state for a new game or when loading a game.
    /// </summary>
    public void ResetFactions()
    {
        Logger.Info("Resetting FactionManager...");
            
        // Clear existing factions
        _factions.Clear();
            
        Logger.Info("FactionManager reset complete.");
    }

    public FactionState? GetFactionByType(FactionType factionType)
    {
        return _factions.Values.FirstOrDefault(faction => faction.Type == factionType);
    }
}