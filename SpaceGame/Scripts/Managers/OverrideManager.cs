using Godot;
using SpaceGame.Utility;
using SpaceGame.Utility.Interfaces;
using System;
using System.Collections.Generic;
using System.Text.Json;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Managers
{
    public class OverrideManager
    {
        public static OverrideManager Instance { get; private set; }

        private Dictionary<string, Dictionary<string, object>> _globalOverrides = new();

        // Static constructor to create the singleton instance
        static OverrideManager()
        {
            Instance = new OverrideManager();
        }

        // Private constructor to prevent external instantiation
        private OverrideManager() { }

        public static void InitializeOverrides()
        {
            Instance.LoadAllOverrides();
        }

        private void LoadAllOverrides()
        {
            _globalOverrides.Clear();
            Logger.Info("OverrideManager: Initializing and loading overrides...");

            // Define paths
            string[] overridePaths = {
                "res://Data/Overrides/",
                "user://overrides/"
            };

            foreach (string basePath in overridePaths)
            {
                LoadOverridesFromDirectory(basePath);
            }
            Logger.Info($"OverrideManager: Finished loading all overrides. {_globalOverrides.Count} entity types have overrides.");
        }

        private void LoadOverridesFromDirectory(string directoryPath)
        {
            if (!DirAccess.DirExistsAbsolute(directoryPath))
            {
                Logger.Info($"OverrideManager: Directory not found, skipping: {directoryPath}");
                return;
            }

            using var dir = DirAccess.Open(directoryPath);
            if (dir == null)
            {
                Logger.Error($"OverrideManager: Failed to open directory: {directoryPath}. Error: {DirAccess.GetOpenError()}");
                return;
            }

            dir.ListDirBegin();
            string fileName = dir.GetNext();
            while (!string.IsNullOrEmpty(fileName))
            {
                if (!dir.CurrentIsDir() && fileName.EndsWith(".json"))
                {
                    string filePath = directoryPath.PathJoin(fileName);
                    LoadAndMergeOverridesFromFile(filePath);
                }
                fileName = dir.GetNext();
            }
            dir.ListDirEnd();
        }

        private void LoadAndMergeOverridesFromFile(string filePath)
        {
            try
            {
                using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    Logger.Error($"OverrideManager: Failed to open file {filePath}. Error: {Godot.FileAccess.GetOpenError()}");
                    return;
                }
                string content = file.GetAsText();
                
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters = { new GodotDictionaryConverter() } // Required for Dictionary<string, object>
                };

                var fileOverrides = JsonSerializer.Deserialize<JsonOverrideFileFormat>(content, options);

                if (fileOverrides?.EntityOverrides != null)
                {
                    foreach (var entityTypeEntry in fileOverrides.EntityOverrides)
                    {
                        string entityTypeId = entityTypeEntry.Key.ToLowerInvariant(); // Convert to lowercase
                        Dictionary<string, object> propertiesToOverride = entityTypeEntry.Value;

                        if (!_globalOverrides.ContainsKey(entityTypeId))
                        {
                            _globalOverrides[entityTypeId] = new Dictionary<string, object>();
                        }

                        // Merge properties, new file can overwrite existing ones from previous files (within the same path type res/user)
                        // User path files will be processed after res path, naturally overwriting.
                        foreach (var propEntry in propertiesToOverride)
                        {
                            _globalOverrides[entityTypeId][propEntry.Key] = propEntry.Value;
                            Logger.Debug($"OverrideManager: Loaded override for {entityTypeEntry.Key} (as {entityTypeId}): {propEntry.Key} = {propEntry.Value}"); // Log original and lowercase ID
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"OverrideManager: Failed to load or parse override file '{filePath}'. Exception: {ex.Message}");
            }
        }

        public void ApplyGlobalOverrides(GodotObject targetEntity, string entityTypeId)
        {
            if (targetEntity == null)
            {
                Logger.Error("OverrideManager.ApplyGlobalOverrides: targetEntity is null.");
                return;
            }

            if (string.IsNullOrEmpty(entityTypeId))
            {
                Logger.Error("OverrideManager.ApplyGlobalOverrides: entityTypeId is null or empty.");
                return;
            }

            string lowerEntityTypeId = entityTypeId.ToLowerInvariant(); // Convert to lowercase

            if (_globalOverrides.TryGetValue(lowerEntityTypeId, out var overridesForEntityType)) // Use lowercase ID for lookup
            {
                Logger.Info($"OverrideManager: Applying {overridesForEntityType.Count} global overrides to entity instance of type '{entityTypeId}' (as '{lowerEntityTypeId}')."); // Log original and lowercase ID
                
                // Check if the target entity implements IJsonPopulatable for more efficient override application
                if (targetEntity is IJsonPopulatable jsonPopulatable)
                {
                    try
                    {
                        // Convert Dictionary to JsonElement
                        string jsonString = JsonSerializer.Serialize(overridesForEntityType);
                        var jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonString);
                        
                        // Use IJsonPopulatable interface
                        jsonPopulatable.PopulateFromJson(jsonElement, PropertyOverrideServiceV2.DefaultSerializerOptions);
                        Logger.Debug($"OverrideManager: Applied overrides to '{entityTypeId}' using IJsonPopulatable interface.");
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"OverrideManager: Error applying overrides using IJsonPopulatable: {ex.Message}", ex);
                        // Fall back to PropertyOverrideServiceV2
                        PropertyOverrideServiceV2.ApplyOverrides(targetEntity, overridesForEntityType);
                    }
                }
                else
                {
                    // Use PropertyOverrideServiceV2 for non-IJsonPopulatable objects
                    PropertyOverrideServiceV2.ApplyOverrides(targetEntity, overridesForEntityType);
                }
            }
            else
            {
                Logger.Info($"OverrideManager: No global overrides found for entity type '{entityTypeId}' (as '{lowerEntityTypeId}')."); // Log original and lowercase ID
            }
        }

        public static void Reset()
        {
            Instance._globalOverrides.Clear();
            Logger.Info("OverrideManager: All global overrides have been cleared.");
            // Optionally, re-initialize if that's the desired reset behavior
            // InitializeOverrides(); 
        }
    }

    // Helper class for JSON structure
    // Assumes the JSON structure from the plan: 
    // {
    //   "entity_overrides": {
    //     "FighterShip": { "MaxHealth": 120, "MovementSpeed": 350.0 },
    //     "BomberShip": { "MaxHealth": 250 }
    //   }
    // }
    public class JsonOverrideFileFormat
    {
        // The JsonPropertyName attribute ensures mapping from "entity_overrides" in JSON
        // to this C# property EntityOverrides.
        [System.Text.Json.Serialization.JsonPropertyName("entity_overrides")]
        public Dictionary<string, Dictionary<string, object>> EntityOverrides { get; set; } = new(); // Initialize to new dictionary
    }
}
