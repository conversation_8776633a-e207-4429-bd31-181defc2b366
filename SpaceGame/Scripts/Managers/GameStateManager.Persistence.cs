using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;
using SpaceGame.State;

namespace SpaceGame.Scripts.Managers;

public partial class GameStateManager
{
    private SaveLoadManager _saveLoadManager => SaveLoadManager.Instance;

    // Track if the game state has been modified and needs saving
    private bool _isGameStateDirty = false;
    
    
    public void LoadGameStateFromFileOrInitialize()
    {
        Logger.Info("Initializing game state...");
        var loadedState = _saveLoadManager.LoadGame();

        if (loadedState == null)
        {
            InitializeGameState();
        }
        else
        {
            Logger.Info("Successfully loaded existing game state.");
            _gameState = loadedState;
        }

        _isGameStateDirty = false;
        Logger.Info("Game state initialization complete.");
    }

    public void InitializeGameState()
    {
        Logger.Info("No existing game state found or load failed. Creating initial state.");
        _gameState = _saveLoadManager.CreateInitialGameState();
        _isGameStateDirty = false;
        SaveGameState();
    }
    
    /// <summary>
    /// Saves the current game state.
    /// </summary>
    /// <param name="saveName">Optional save name (uses "Autosave" if null).</param>
    /// <param name="description">Optional save description.</param>
    /// <returns>True if the save was successful, false otherwise.</returns>
    public bool SaveGameState(string? saveName = null, string description = "")
    {
        if (_gameState == null)
        {
            Logger.Error("Cannot save: No current game state exists.");
            return false;
        }

        Error result = _saveLoadManager.SaveGame(_gameState, saveName, description);
        if (result == Error.Ok)
        {
            _isGameStateDirty = false;
            Logger.Info($"Game state saved successfully with name: {saveName ?? "Autosave"}");

            // Raise a game saved event
            StateEvents.RaiseGameStateChanged(GameStateChangeType.GameSaved);

            return true;
        }
        else
        {
            Logger.Error($"Failed to save game state: {result}");
            return false;
        }
    }
    
    /// <summary>
    /// Loads a game from the specified save file.
    /// </summary>
    /// <param name="saveFileName">The save file to load</param>
    /// <returns>True if the game was loaded successfully</returns>
    public bool LoadGame(string? saveFileName = null)
    {
        Logger.Info($"Loading game from {saveFileName}...");

        var loadedState = _saveLoadManager.LoadGame(saveFileName);

        if (loadedState == null)
        {
            Logger.Error($"Failed to load game from {saveFileName}");
            return false;
        }
            
        ApplyGameState(loadedState);

        Logger.Info($"Game loaded successfully from {saveFileName}");

        // Raise a game loaded event
        StateEvents.RaiseGameStateChanged(GameStateChangeType.GameLoaded);

        return true;
    }
    
    
    /// <summary>
    /// Applies a game state to the current game.
    /// </summary>
    /// <param name="gameState">The game state to apply.</param>
    public void ApplyGameState(GameState gameState)
    {
        Logger.Info("Applying game state...");

        // Store the game state
        _gameState = gameState;

        Logger.Info("Game state applied successfully");

        // Reset dirty flag
        _isGameStateDirty = false;

        // Raise a game state applied event
        StateEvents.RaiseGameStateChanged(GameStateChangeType.GameStateApplied);
    }
    
    /// <summary>
    /// Marks the game state as dirty, indicating it needs to be saved.
    /// </summary>
    public void MarkGameStateDirty()
    {
        _isGameStateDirty = true;
        Logger.Debug("Game state marked as dirty");
    }
    
    /// <summary>
    /// Checks if the game state is dirty (needs saving).
    /// </summary>
    /// <returns>True if the game state needs saving, false otherwise.</returns>
    public bool IsGameStateDirty()
    {
        return _isGameStateDirty;
    }

    
    /// <summary>
    /// Starts a new game, resetting all state.
    /// </summary>
    public void NewGame()
    {
        Logger.Info("Starting new game...");

        _gameState = _saveLoadManager.CreateInitialGameState();
        _isGameStateDirty = false;
        Logger.Info("New game started successfully.");

        // Raise a new game started event
        StateEvents.RaiseGameStateChanged(GameStateChangeType.NewGame);
    }
    
            
    /// <summary>
    /// Lists all available save files.
    /// </summary>
    /// <returns>Array of save file names.</returns>
    public string[] ListSaveFiles()
    {
        return _saveLoadManager.ListSaveFiles();
    }

    /// <summary>
    /// Deletes a save file.
    /// </summary>
    /// <param name="saveName">The name of the save to delete.</param>
    /// <returns>True if the delete was successful, false otherwise.</returns>
    public bool DeleteSaveFile(string saveName)
    {
        return _saveLoadManager.DeleteSave(saveName);
    }
}