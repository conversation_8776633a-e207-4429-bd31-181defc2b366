using R3;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Events;
using SpaceGame.State;
using SpaceGame.Universe.Model;

namespace SpaceGame.Scripts.Managers
{
    /// <summary>
    /// Provides a simplified facade over the various manager classes for game code to access.
    /// This class centralizes access to common operations that would otherwise require direct interaction with multiple managers.
    /// </summary>
    public partial class GameStateManager
    {
        private static GameStateManager? _instance;
        
        /// <summary>
        /// Gets the singleton instance of the GameManagerFacade.
        /// </summary>
        public static GameStateManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    Logger.Info("GameManagerFacade: Creating singleton instance");
                    _instance = new GameStateManager();
                }
                return _instance;
            }
        }
        
        private GameState _gameState;

        private ReactiveProperty<GameViewState> _currentViewState { get; } = new(GameViewState.UNIVERSE_VIEW);

        /// <summary>
        /// Reactive property representing the current top-level view state.
        /// Subscribe to this property to react to view changes.
        /// Usage:
        ///   gameState.CurrentViewState.Subscribe(newState => { /* handle state change */ });
        ///   gameState.CurrentViewState.Value = GameViewState.FLEET_MANAGEMENT_SCREEN;
        /// </summary>
        public ReadOnlyReactiveProperty<GameViewState> CurrentViewState => _currentViewState;

        
        // Using static events from event classes instead of EventBus
        
        // Private constructor for singleton pattern
        private GameStateManager()
        {
            Logger.Info("GameStateManager initializing...");
            LoadGameStateFromFileOrInitialize();
            Logger.Info("GameManagerFacade initialization complete");
            BattleEvents.BattleStarted += HandleBattleStarted;
            BattleEvents.BattleCompleted += HandleBattleCompleted;
        }

        private void HandleBattleCompleted(bool playerVictory)
        {
            Logger.Info($"Battle completed. Player victory: {playerVictory}");
            TransitionToViewState(GameViewState.UNIVERSE_VIEW);
        }

        private void HandleBattleStarted(BattleConfig config)
        {
            Logger.Info($"Battle started with config: {config}");
            TransitionToViewState(GameViewState.SPACE_BATTLE_VIEW);
        }

        /// <summary>
        /// Gets the current game state.
        /// </summary>
        public GameState GetGameState() => _gameState;
        
        /// <summary>
        /// Completely resets the game state, clearing all data and returning to a clean state.
        /// This should be called before generating a new universe.
        /// </summary>
        public void ResetState()
        {
            Logger.Info("Completely resetting GameState");
            
            // Create a new empty GameState
            _gameState = new GameState
            {
                Id = System.Guid.NewGuid().ToString(),
                DataVersion = _gameState.DataVersion // Preserve the data version
            };
            
            TransitionToViewState(GameViewState.UNIVERSE_VIEW);
            
            Logger.Info("GameState reset complete");
        }
        
        /// <summary>
        /// Initializes the GameState with data from a newly generated UniverseModel.
        /// This should be called after generating a new universe and ResetState().
        /// </summary>
        /// <param name="model">The newly generated universe model</param>
        public void UpdateUniverseModel(UniverseModel model)
        {
            Logger.Info("Initializing GameState from UniverseModel");
            
            // Set the universe model
            _gameState.CurrentUniverse = model;
            
            Logger.Info("GameState initialized from UniverseModel");
        }
        
        public void TransitionToViewState(GameViewState viewState)
        {
            _currentViewState.Value = viewState;
        }
    }
} 