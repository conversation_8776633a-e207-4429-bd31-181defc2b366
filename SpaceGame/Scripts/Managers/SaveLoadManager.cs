using System;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Helpers.Serialization;
using SpaceGame.Scripts.Events;
using SpaceGame.State;
using FileAccess = Godot.FileAccess;

namespace SpaceGame.Scripts.Managers
{
    /// <summary>
    /// Manager for saving and loading game state.
    /// </summary>
    public class SaveLoadManager
    {
        private static SaveLoadManager? _instance;
        
        /// <summary>
        /// Gets the singleton instance of the SaveLoadManager.
        /// </summary>
        public static SaveLoadManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new SaveLoadManager();
                    Logger.Info("SaveLoadManager singleton created");
                }
                return _instance;
            }
        }
        
        // Legacy path for backward compatibility
        public static readonly string LegacySaveFilePath = "user://GameState.tres";
        
        // The serialization strategy to use
        private readonly ISerializationStrategy _serializationStrategy;
        
        // Using static events instead of EventBus
        
        /// <summary>
        /// Private constructor for singleton pattern.
        /// </summary>
        private SaveLoadManager()
        {
            // Private constructor for singleton pattern
            Logger.Info("SaveLoadManager initializing...");
            
            _serializationStrategy = new JsonSerializationStrategy(
                SerializationConfig.CreateGameStateSerializationOptions()
            );
            
            // Ensure the saves directory exists
            SerializationConfig.EnsureSavesDirectoryExists();
            
            // Subscribe to events
            SubscribeToEvents();
        }
        
        /// <summary>
        /// Subscribes to game-related events.
        /// </summary>
        private void SubscribeToEvents()
        {
            Logger.Info("Subscribing to save/load-related events");
            
            // Subscribe to game state changed events
            StateEvents.GameStateChanged += OnGameStateChanged;
            
            Logger.Info("Save/load event subscriptions complete");
        }
        
        /// <summary>
        /// Handles game state changed events.
        /// </summary>
        private void OnGameStateChanged(GameStateChangeType stateChangeType)
        {
            Logger.Debug($"Handling game state change: {stateChangeType}");
            
            // Auto-save when significant events occur
            if (stateChangeType == GameStateChangeType.GameStateApplied || 
                stateChangeType == GameStateChangeType.FleetCreated ||
                stateChangeType == GameStateChangeType.FleetDeleted ||
                stateChangeType == GameStateChangeType.FactionCreated ||
                stateChangeType == GameStateChangeType.FactionDeleted)
            {
                // Don't auto-save for now, let the GameManager control saving
                // We could add auto-saving here if desired in the future
                Logger.Debug("Significant game state change detected - GameManager will handle saving if needed");
            }
        }
        
        /// <summary>
        /// Checks if a save file exists at the specified path.
        /// </summary>
        /// <param name="saveName">Optional save name (uses autosave if not specified)</param>
        /// <returns>True if the save file exists, false otherwise</returns>
        public bool HasSaveFile(string? saveName = null)
        {
            string path = GetSaveFilePath(saveName);
            return FileAccess.FileExists(path);
        }
        
        /// <summary>
        /// Gets the full path to a save file.
        /// </summary>
        /// <param name="saveName">Optional save name (uses autosave if not specified)</param>
        /// <returns>The full path to the save file</returns>
        private string GetSaveFilePath(string? saveName = null)
        {
            return string.IsNullOrEmpty(saveName)
                ? SerializationConfig.GetAutosaveFilePath()
                : SerializationConfig.GetSaveFilePath(saveName);
        }
        
        /// <summary>
        /// Lists all available save files.
        /// </summary>
        /// <returns>Array of save file names</returns>
        public string[] ListSaveFiles()
        {
            if (!SerializationConfig.EnsureSavesDirectoryExists())
            {
                Logger.Error("Failed to access saves directory");
                return Array.Empty<string>();
            }
            
            var dir = DirAccess.Open(SerializationConfig.SavesDirectory);
            if (dir == null)
            {
                Logger.Error($"Failed to open saves directory: {FileAccess.GetOpenError()}");
                return Array.Empty<string>();
            }
            
            var saveFiles = new System.Collections.Generic.List<string>();
            dir.ListDirBegin();
            
            string fileName = dir.GetNext();
            while (!string.IsNullOrEmpty(fileName))
            {
                if (!dir.CurrentIsDir() && fileName.EndsWith(_serializationStrategy.GetFileExtension()))
                {
                    saveFiles.Add(fileName);
                }
                fileName = dir.GetNext();
            }
            
            dir.ListDirEnd();
            return saveFiles.ToArray();
        }
        
        /// <summary>
        /// Saves the game state to a file.
        /// </summary>
        /// <param name="gameState">The game state to save</param>
        /// <param name="saveName">Optional save name (uses autosave if not specified)</param>
        /// <param name="description">Optional save description</param>
        /// <returns>Error.Ok if successful, otherwise an error code</returns>
        public Error SaveGame(GameState gameState, string? saveName = null, string description = "")
        {
            try
            {
                Logger.Info($"Attempting to save game state with name: {saveName ?? "Autosave"}");
                
                // Determine save file path
                string saveFilePath = GetSaveFilePath(saveName);
                
                // Create a backup of the existing file
                SerializationHelper.CreateBackup(saveFilePath);
                
                // Create save metadata
                var metadata = SerializationConfig.CreateSaveMetadata(
                    saveName ?? "Autosave", 
                    description);
                
                // Convert GameState to SaveState
                var saveState = gameState.ToSaveState();
                
                // Create save data container
                var saveData = new SaveData(saveState, metadata);
                
                // Save to file
                bool success = _serializationStrategy.SaveToFile(saveFilePath, saveData);
                
                if (!success)
                {
                    Logger.Error($"Failed to save game state to {saveFilePath}");
                    return Error.Failed;
                }
                
                Logger.Info($"Game state saved successfully to {saveFilePath}");
                
                Logger.Info($"Game saved successfully to {saveFilePath}");
                StateEvents.RaiseGameStateChanged(GameStateChangeType.SaveCompleted);
                StateEvents.RaiseSaveCompleted(saveName ?? "autosave", true);
                
                return Error.Ok;
            }
            catch (Exception ex)
            {
                Logger.Error($"Exception while saving game state: {ex.Message}");
                return Error.Failed;
            }
        }
        
        /// <summary>
        /// Loads a game state from a file.
        /// </summary>
        /// <param name="saveName">Optional save name (uses autosave if not specified)</param>
        /// <returns>The loaded game state, or null if loading failed</returns>
        public GameState? LoadGame(string? saveName = null)
        {
            try
            {
                string path = GetSaveFilePath(saveName);
                Logger.Info($"Attempting to load game state from {path}");
                
                if (!FileAccess.FileExists(path))
                {
                    Logger.Warning($"No save file found at {path}");
                    return null;
                }
                
                // Signal that we're about to load a game
                StateEvents.RaiseGameStateChanged(GameStateChangeType.PreLoad);
                
                // Load the save data
                var saveData = LoadSaveData(path);
                if (saveData == null)
                {
                    Logger.Error($"Failed to load save data from {path}");
                    return null;
                }
                
                // Get GameState from SaveData (handles both SaveState and legacy GameState)
                var gameState = saveData.GetGameState();
                
                if (gameState != null)
                {
                    Logger.Info($"Successfully loaded game state from {path}");
                    
                    // Raise a post-load event to notify systems that a load has completed
                    // The GameManager will raise the more specific game_loaded event after applying the state
                    StateEvents.RaiseGameStateChanged(GameStateChangeType.PostLoad);
                    StateEvents.RaiseLoadCompleted(gameState, true);
                    
                    return gameState;
                }
                else
                {
                    Logger.Error($"Save data at {path} contains no game state data");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Exception while loading game state: {ex.Message}", ex);
                return null;
            }
        }
        
        /// <summary>
        /// Deletes a save file.
        /// </summary>
        /// <param name="saveName">The name of the save to delete</param>
        /// <returns>True if the save was deleted, false otherwise</returns>
        public bool DeleteSave(string saveName)
        {
            try
            {
                string path = GetSaveFilePath(saveName);
                
                if (!FileAccess.FileExists(path))
                {
                    Logger.Warning($"Cannot delete save {saveName}: file not found at {path}");
                    return false;
                }
                
                Error result = DirAccess.RemoveAbsolute(path);
                if (result != Error.Ok)
                {
                    Logger.Error($"Failed to delete save {saveName} at {path}: {result}");
                    return false;
                }
                
                Logger.Info($"Successfully deleted save {saveName} at {path}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Exception while deleting save {saveName}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Creates a new, empty game state.
        /// </summary>
        /// <returns>A new game state instance</returns>
        public GameState CreateInitialGameState()
        {
            var newGameState = new GameState();
            
            // Initialize with a unique ID
            newGameState.Id = Guid.NewGuid().ToString();
            
            // Initialize meta progression data
            newGameState.MetaProgress = new MetaProgressionData();
            
            Logger.Info("Initial game state created");
            StateEvents.RaiseGameStateChanged(GameStateChangeType.NewGameStateCreated);
            
            Logger.Info("Created new initial game state");
            return newGameState;
        }
        
        /// <summary>
        /// Loads a save data file from a path.
        /// </summary>
        /// <param name="path">The path to the save file</param>
        /// <returns>The loaded save data, or null if loading failed</returns>
        public SaveData? LoadSaveData(string path)
        {
            try
            {
                if (!FileAccess.FileExists(path))
                {
                    Logger.Warning($"Save file does not exist at path: {path}");
                    return null;
                }
                
                using var file = FileAccess.Open(path, FileAccess.ModeFlags.Read);
                var jsonContent = file.GetAsText();
                
                var saveData = _serializationStrategy.Deserialize<SaveData>(jsonContent);
                if (saveData == null)
                {
                    Logger.Error($"Failed to deserialize save data from {path}");
                    return null;
                }
                
                // Apply migrations if needed
                saveData = SerializationConfig.MigrateSaveData(saveData);
                
                return saveData;
            }
            catch (Exception ex)
            {
                Logger.Error($"Failed to load save data: {ex.Message}", ex);
                return null;
            }
        }
        
        /// <summary>
        /// Loads a legacy save file using Godot's ResourceLoader.
        /// </summary>
        /// <param name="savePath">The path to the legacy save file</param>
        /// <returns>The loaded game state, or null if loading failed</returns>
        public GameState? LoadLegacySaveFile(string savePath)
        {
            try
            {
                Logger.Info($"Loading legacy save file from {savePath}");
                
                if (!FileAccess.FileExists(savePath))
                {
                    Logger.Warning($"Legacy save file does not exist at path: {savePath}");
                    return null;
                }
                
                var gameState = ResourceLoader.Load<GameState>(savePath, cacheMode: ResourceLoader.CacheMode.IgnoreDeep);
                if (gameState == null)
                {
                    Logger.Error($"Failed to load legacy save file from {savePath}");
                    return null;
                }
                
                Logger.Info($"Successfully loaded legacy game state from {savePath}");
                
                // Save in new format after loading for future compatibility
                SaveGame(gameState, "ConvertedFromLegacy", "Automatically converted from legacy format");
                
                return gameState;
            }
            catch (Exception ex)
            {
                Logger.Error($"Exception while loading legacy save file: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Resets the SaveLoadManager to its initial state.
        /// </summary>
        public void Reset()
        {
            Logger.Info("Resetting SaveLoadManager...");
            
            // Unsubscribe from events
            StateEvents.GameStateChanged -= OnGameStateChanged;
            
            // Re-subscribe to events
            SubscribeToEvents();
            
            Logger.Info("SaveLoadManager reset complete");
        }
    }
}