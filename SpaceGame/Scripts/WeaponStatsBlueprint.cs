namespace SpaceGame.Scripts;

using Godot;
using System;

// Defines stats for a single weapon.
// Not a Resource itself, but a data container used by WeaponData and FleetShipInstance.
[GlobalClass]
public partial class WeaponStatsBlueprint : Resource
{
    // Base Stats
    [Export] public float DamagePerShot { get; set; } = 10f;
    [Export] public float FireRate { get; set; } = 1.0f; // Shots per second
    [Export] public float Range { get; set; } = 800f;
    [Export] public float ProjectileSpeed { get; set; } = 1000f;
    [Export] public float Accuracy { get; set; } = 1.0f; // 1.0 = perfect

    public WeaponStatsBlueprint() { } // Needed for initialization

    // Copy constructor (can be useful even for blueprints)
    public WeaponStatsBlueprint(WeaponStatsBlueprint other)
    {
        if (other == null) return; // Safety check

        DamagePerShot = other.DamagePerShot;
        FireRate = other.FireRate;
        Range = other.Range;
        ProjectileSpeed = other.ProjectileSpeed;
        Accuracy = other.Accuracy;
    }
}