using System;
using System.Collections.Generic;
using Godot;
using ImGuiNET;
using R3;
using SpaceGame.Battle;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Managers;
using SpaceGame.State;
using SpaceGame.UI.MiniMap;
using SpaceGame.Universe;
using SpaceGame.Universe.Cosmic;
using SpaceGame.Universe.Generation;
using SpaceGame.Universe.Generation.Factions;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;
using SpaceGame.Universe.Selection;

[GlobalClass]
public partial class UniverseRoot : Node2D
{
    private Camera2D _camera;
    public Camera2D Camera => _camera;
    
    private HexGridManager _hexGridManager;
    public HexGridManager HexGridManager => _hexGridManager;
    
    private bool _isInputPaused = false; // Tracks if input should be paused due to view state

    private SelectionManager _selectionManager;
    public SelectionManager SelectionManager => _selectionManager;
    // Using computed property to access HexGrid singleton instead of storing an instance
    private HexGrid _hexGrid => HexGrid.GetInstance();
    // Using computed property to access CelestialBodyRegistry singleton
    private CelestialBodyRegistry _celestialRegistry => CelestialBodyRegistry.GetInstance();

    private const float CAMERA_SPEED = 500.0f;
    private const float CAMERA_SPEED_MULTIPLIER = 2.5f; // Multiplier when shift is pressed
    private const float CAMERA_ZOOM_SPEED = 0.05f;
    private const float MOUSE_ZOOM_SPEED = 0.05f;
    private const float MIN_ZOOM = 0.1f;
    private const float MAX_ZOOM = 5.0f;
    private const float CAMERA_LERP_SPEED = 5.0f;
    
    private Vector2? _targetCameraPosition;

    // For universe regeneration
    private string _currentSeed = "spaceGame";
    public string CurrentSeed => _currentSeed;
    
    // Reference to the battle button
    private Button _startBattleButton;

    // Reference to the minimap toggle button
    private Button _showMinimapButton;
    
    // Reference to the minimap
    private MiniMap _miniMap;
    
    private DisposableBag _disposables;
    
    // Added BattleCoordinator export
    [Export]
    private BattleCoordinator _battleCoordinator;

    // Access to the unified manager facade
    private GameStateManager _gameStateManager => GameStateManager.Instance;
    private GameState _gameState => _gameStateManager.GetGameState();
    private UniverseModel _currentUniverseModel => _gameState.CurrentUniverse;

    private ReadOnlyReactiveProperty<GameViewState> _viewState => _gameStateManager.CurrentViewState;

    public override void _Ready()
    {
        Logger.Info("UniverseRoot._Ready() called");
        // Get references to our nodes
        _camera = GetNode<Camera2D>("Camera");
        _hexGridManager = GetNode<HexGridManager>("%HexGridManager");
        _selectionManager = GetNode<SelectionManager>("%SelectionManager");
        
        // Get reference to the minimap toggle button
        _showMinimapButton = GetNode<Button>("%ShowMinimapButton");
        
        // Get reference to the minimap
        _miniMap = GetNode<MiniMap>("%MiniMap");
        _miniMap.Initialize(this, _camera);
        
        // Connect signals for Minimap toggle button
        if (_showMinimapButton != null && _miniMap != null)
        {
            _showMinimapButton.Pressed += OnShowMinimapButtonPressed;
            
            // Initially, the minimap should be visible and the button should show "Hide Map"
            _miniMap.Visible = true;
            _showMinimapButton.Text = "Hide Map";
        }
        else
        {
            Logger.Error("Failed to find ShowMinimapButton or MiniMap nodes. Check paths/unique names in UniverseScene.tscn");
        }

        _startBattleButton = GetNodeOrNull<Button>("%StartBattleButton");
        _startBattleButton.Pressed += OnStartBattleButtonPressed;

        // Ensure BattleCoordinator is assigned
        if (_battleCoordinator == null)
        {
            Logger.Error("BattleCoordinator is not assigned in UniverseRoot. Please assign it in the editor.");
        }

        _viewState.Subscribe(HandleViewStateChange)
            .AddTo(ref _disposables);
    }
    
    private void HandleViewStateChange(GameViewState viewState)
    {
        Logger.Debug($"Handling view state change: {viewState}");
        _isInputPaused = viewState != GameViewState.UNIVERSE_VIEW;
    }
    
    private void OnStartBattleButtonPressed()
    {
        Logger.Info("Start Battle button pressed");

        if (_battleCoordinator != null)
        {
            // Create placeholder battle configuration
            var playerFleet = new PlayerFleetInfo
            {
                PlayerShipTypes = new List<string> { "Fighter" } // Example: Start with one fighter
            };
            var enemyConfig = new EnemySpawnConfig
            {
                Waves = new List<EnemyWave>
                {
                    new EnemyWave { EnemyShipTypes = new List<string> { "Scout" }, NumberToSpawn = 2, DelayBeforeSpawnSeconds = 1f, SpawnIntervalSeconds = 1.5f },
                    new EnemyWave { EnemyShipTypes = new List<string> { "Fighter" }, NumberToSpawn = 1, DelayBeforeSpawnSeconds = 5f }
                }
            };

            // Start the battle using the coordinator
            _battleCoordinator.StartNewBattle(playerFleet, enemyConfig);
        }
        else
        {
            Logger.Error("Cannot start battle: BattleCoordinator is not assigned.");
        }
    }

    public void GenerateOrDisplayUniverse()
    {
        var loadedUniverse = _gameState.CurrentUniverse;
        if (loadedUniverse != null && loadedUniverse.GetStats().CelestialBodyCount > 0)
        {
            DisplayGeneratedUniverse(loadedUniverse);
        }
        else
        {
            GenerateUniverse(_currentSeed);
        }
    }
    
    private void GenerateUniverse(string seed)
    {
        _currentSeed = seed;

        Logger.Info($"Generating universe using multi-pass generator with seed '{seed}'");
        
        string universeName = $"Universe {DateTime.Now.ToString("yyyyMMdd-HHmmss")}";
        
        // Generate the universe model using optimized generator (medium size)
        var generator = new MultiPassUniverseGenerator(universeName, seed, 100); // Medium size (100)
        var universeModel = generator.Generate();
        
        DisplayGeneratedUniverse(universeModel);
        Logger.Info($"Generated universe using multi-pass generator with seed: {seed}");
    }

    public override void _Process(double delta)
    {
        HandleCameraMovement(delta);
        UpdateCameraPosition(delta);
    }
    
    public override void _UnhandledInput(InputEvent @event)
    {
        // If input is paused due to view state, do not handle any input
        if (_isInputPaused) return;
        
        // Only handle inputs when mouse is over the universe viewport
        var universeViewport = GetViewport();
        bool mouseInUniverseViewport = universeViewport.GetVisibleRect().HasPoint(universeViewport.GetMousePosition());
        
        if (!mouseInUniverseViewport)
            return;
            
        if (@event is InputEventMouseButton mouseButton)
        {
            if (mouseButton.Pressed)
            {
                // Handle zoom
                Vector2 zoomChange = Vector2.Zero;
                
                switch (mouseButton.ButtonIndex)
                {
                    case MouseButton.WheelDown:
                        zoomChange = Vector2.One * MOUSE_ZOOM_SPEED;
                        break;
                    case MouseButton.WheelUp:
                        zoomChange = Vector2.One * -MOUSE_ZOOM_SPEED;
                        break;
                }
                
                if (zoomChange != Vector2.Zero)
                {
                    // Get mouse position in viewport coordinates
                    Vector2 mousePos = GetViewport().GetMousePosition();
                    
                    // Get the mouse position in world coordinates before zoom
                    Vector2 worldPos = _camera.GetScreenCenterPosition() + (mousePos - universeViewport.GetVisibleRect().Size / 2) * _camera.Zoom;
                    
                    // Apply zoom
                    _camera.Zoom = new Vector2(
                        Mathf.Clamp(_camera.Zoom.X + zoomChange.X, MIN_ZOOM, MAX_ZOOM),
                        Mathf.Clamp(_camera.Zoom.Y + zoomChange.Y, MIN_ZOOM, MAX_ZOOM)
                    );
                    
                    // Calculate new camera position to keep mouse position stable
                    Vector2 newWorldPos = _camera.GetScreenCenterPosition() + (mousePos - universeViewport.GetVisibleRect().Size / 2) * _camera.Zoom;
                    _camera.Position += worldPos - newWorldPos;
                    
                    _miniMap?.UpdateViewportIndicator();
                    
                    GetViewport().SetInputAsHandled();
                }
            }
        }
    }

    public void UpdateGeneratorSeed(string seed)
    {
        Logger.Debug($"UpdateGeneratorSeed {seed}");
        _currentSeed = seed;
        GenerateUniverseFromSeed(seed);
    }

    public void GenerateUniverseFromSeed(string seed, int universeSize = 100)
    {
        Logger.Info($"Generating universe with seed: {seed}");
        
        // 1. Clear Selection & Visuals
        _selectionManager.ClearSelection();
        _celestialRegistry.Clear(); // Removes bodies from registry and queues them for deletion
        _hexGrid.Clear();           // Clears the logical grid state
        _hexGridManager.ClearHexagons(); // Removes hexagon nodes from the scene
        
        // 2. Reset GameState FULLY
        _gameStateManager.ResetState(); // Call the new complete reset method
        
        // 3. Generate New UniverseModel (includes factions)
        string universeName = $"Universe {DateTime.Now.ToString("yyyyMMdd-HHmmss")}";
        var generator = new MultiPassUniverseGenerator(universeName, seed, universeSize);
        UniverseModel newUniverseModel = generator.Generate();
        
        // 4. Initialize GameState from the New Model
        _gameStateManager.UpdateUniverseModel(newUniverseModel);
        var factionGenerator = new FactionGenerator(seed);
        var factionResult = factionGenerator.GenerateFactions(newUniverseModel);
        _gameStateManager.AddOrUpdateFaction(factionResult.PlayerFaction);
        _gameStateManager.AddOrUpdateFaction(factionResult.EnemyFaction);
        
        // 5. Display the New State (reads from the updated GameState)
        DisplayGeneratedUniverse(_gameState.CurrentUniverse);
        
        Logger.Info("Universe generation complete.");
    }
    
    private void RegenerateUniverse()
    {
        Logger.Debug("RegenerateUniverse called");
        GenerateUniverseFromSeed(_currentSeed);
    }

    private void HandleCameraMovement(double delta)
    {
        if (ImGui.GetIO().WantCaptureKeyboard)
            return;
        
        // If input is paused due to view state, do not handle any input
        if (_isInputPaused) return;
            
        Vector2 movement = Vector2.Zero;
        
        // Movement using input actions
        if (Input.IsActionPressed(InputNames.UNIVERSE_MOVE_UP)) movement.Y -= 1;
        if (Input.IsActionPressed(InputNames.UNIVERSE_MOVE_DOWN)) movement.Y += 1;
        if (Input.IsActionPressed(InputNames.UNIVERSE_MOVE_LEFT)) movement.X -= 1;
        if (Input.IsActionPressed(InputNames.UNIVERSE_MOVE_RIGHT)) movement.X += 1;
        
        // Apply speed boost when shift is pressed
        float speedMultiplier = 1.0f;
        if (Input.IsKeyPressed(Key.Shift))
        {
            speedMultiplier = CAMERA_SPEED_MULTIPLIER;
        }
        
        var needsIndicatorUpdate = movement.Length() > 0.0f;
        
        movement = movement.Normalized() * CAMERA_SPEED * speedMultiplier * (float)delta;
        _camera.Position += movement;
        
        // Zoom using input actions - Scale speed by current zoom for consistent feel
        float zoomFactor = CAMERA_ZOOM_SPEED; // Use the existing speed constant as a factor
        
        if (Input.IsActionPressed(InputNames.UNIVERSE_ZOOM_OUT))
        {
            _camera.Zoom = new Vector2(
                Mathf.Clamp(_camera.Zoom.X * (1.0f - zoomFactor), MIN_ZOOM, MAX_ZOOM),
                Mathf.Clamp(_camera.Zoom.Y * (1.0f - zoomFactor), MIN_ZOOM, MAX_ZOOM)
            );
            
            // Update minimap when zoom changes
            needsIndicatorUpdate = true;
        }
        if (Input.IsActionPressed(InputNames.UNIVERSE_ZOOM_IN))
        {
            _camera.Zoom = new Vector2(
                Mathf.Clamp(_camera.Zoom.X * (1.0f + zoomFactor), MIN_ZOOM, MAX_ZOOM),
                Mathf.Clamp(_camera.Zoom.Y * (1.0f + zoomFactor), MIN_ZOOM, MAX_ZOOM)
            );
            
            // Update minimap when zoom changes
            needsIndicatorUpdate = true;
        }
        
        if (needsIndicatorUpdate) _miniMap?.UpdateViewportIndicator();
    }
    
    private void UpdateCameraPosition(double delta)
    {
        if (_targetCameraPosition.HasValue)
        {
            // Smoothly move camera to target position
            _camera.Position = _camera.Position.Lerp(
                _targetCameraPosition.Value,
                (float)delta * CAMERA_LERP_SPEED
            );
            
            // Update minimap when camera moves
            _miniMap?.UpdateViewportIndicator();
            
            // If we're close enough to the target, consider it reached
            if (_camera.Position.DistanceTo(_targetCameraPosition.Value) < 1.0f)
            {
                _targetCameraPosition = null;
            }
        }
    }
    
    private void OnShowMinimapButtonPressed()
    {
        if (_miniMap != null)
        {
            _miniMap.Visible = !_miniMap.Visible;
            _showMinimapButton.Text = _miniMap.Visible ? "Hide Map" : "Show Map";
        }
    }
    
    /// <summary>
    /// Iterates through all existing Hexagon nodes and updates their visual state.
    /// Used when global display settings like ShowUnrevealedHexesDebug change.
    /// Also ensures visual Hexagon nodes exist for all cells in the model.
    /// </summary>
    public void UpdateAllHexVisuals()
    {
        Logger.Info($"Updating all Hex Visuals");
        if (_currentUniverseModel == null)
        {
            Logger.Debug($"_currentUniverseModel is null, skipping visual update.");
            return;
        }
        
        // Iterate through all cells in the model, ensure a visual node exists, and update its state
        foreach (var cell in _currentUniverseModel.GetAllCells())
        {
            // Ensure the visual Hexagon node exists for this cell coordinate using GetOrCreateHexagon
            var hexNode = _hexGridManager.GetOrCreateHexagon(cell.Coordinates);
            
            // Update its visual state based on the model
            hexNode?.UpdateVisualState(_currentUniverseModel);
        }
        
        // Update minimap to reflect visual changes
        _miniMap?.OnUniverseChanged();
    }
    
        
    /// <summary>
    /// Updates the visual state of hexagons neighboring a given coordinate.
    /// This is called when a hex is manually revealed or un-revealed via debug controls.
    /// </summary>
    public void UpdateNeighboringHexVisuals(HexCoordinates centerCoords)
    {
        if (_currentUniverseModel == null) return;

        List<HexCoordinates> neighbors = HexUtils.GetRing(centerCoords, 1);
        
        foreach (var neighborCoords in neighbors)
        {
            var neighborNode = _hexGridManager.GetHexagon(neighborCoords);
            neighborNode?.UpdateVisualState(_currentUniverseModel);
        }
        
        // Update minimap to reflect visual changes to neighboring hexes
        _miniMap?.OnUniverseChanged();
    }
    
    private void DisplayGeneratedUniverse(UniverseModel universeModel)
    {
        // Initialize Fog of War state *before* creating visuals
        InitializeFogOfWar(universeModel);
        
        // Create celestial bodies from the model
        CreateCelestialBodiesFromModel(universeModel);
        
        // Ensure all hex visuals (including empty ones) are updated based on the final model state
        UpdateAllHexVisuals();
        
        // Update minimap with new universe data
        _miniMap?.OnUniverseChanged();
    }
    
        private void CreateCelestialBodiesFromModel(UniverseModel universeModel)
    {
        foreach (var bodyData in universeModel.GetAllCelestialBodies())
        {
            if (bodyData.Position == null)
                continue;
                
            // Get the corresponding HexCell data from the model
            var hexCellData = universeModel.GetCell(bodyData.Position);
            if (hexCellData == null)
            {
                Logger.Warning($"Hexagon node created for {bodyData.Position}, but no corresponding HexCell data found in model.");
                continue; // Skip if no data for this cell
            }

            // Convert model hex coordinates to our HexPosition
            var hexPosition = new HexCoordinates(bodyData.Position.Q, bodyData.Position.R);
            
            // Ensure the visual Hexagon node exists for this coordinate first
            Hexagon hex = _hexGridManager.GetOrCreateHexagon(hexPosition);

            // Set initial visibility and visual state based on Fog of War state from the model
            hex.UpdateVisualState(universeModel);

            // Try to create the appropriate celestial body scene node based on type
            CelestialBody? celestialBody = CreateCelestialBodyFromData(bodyData); // Returns null for EmptySpace
            
            // If a scene node was created (i.e., not EmptySpace)
            if (celestialBody != null)
            {
                // Associate the body node with the hexagon node
                hex.SetContainedBody(celestialBody);
                
                // Register the body in the registry
                _celestialRegistry.RegisterBody(hexPosition, celestialBody);
                
                // Mark the hexagon as occupied in the logical hex grid
                _hexGrid.MarkOccupied(hexPosition);
            }
            // If celestialBody is null (EmptySpace), we still have the 'hex' node created above,
            // but we don't associate a body with it or mark it occupied in the logical grid.
            // Its visibility is still controlled by hex.SetRevealed() above.
        }
    }
    
    private CelestialBody? CreateCelestialBodyFromData(CelestialBodyData data)
    {
        CelestialBody? body = null;
        
        // Create the appropriate body type based on the CelestialBodyType
        switch (data.BodyType)
        {
            case CelestialBodyType.EmptySpace:
                // Don't create a scene node for empty space
                body = null;
                break;
                
            case CelestialBodyType.Star:
                var starScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Sun.tscn");
                var star = starScene.Instantiate<Sun>();
                star.Initialize(data);
                body = star;
                break;
                
            case CelestialBodyType.TerranPlanet:
            case CelestialBodyType.DesertPlanet:
            case CelestialBodyType.ForestPlanet:
            case CelestialBodyType.GasGiant:
            case CelestialBodyType.OceanPlanet:
            case CelestialBodyType.TundraPlanet:
            case CelestialBodyType.IcePlanet:
            case CelestialBodyType.LavaPlanet:
            case CelestialBodyType.RockyPlanet:
            case CelestialBodyType.TechPlanet:
            case CelestialBodyType.Asteroid:
                var planetScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Planet.tscn");
                var planet = planetScene.Instantiate<Planet>();
                planet.Initialize(data);
                body = planet;
                break;
                
            case CelestialBodyType.Nebula:
                var nebulaScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Cosmic/Nebula.tscn");
                var nebula = nebulaScene.Instantiate<Nebula>();
                nebula.Initialize(data);
                body = nebula;
                break;
                
            case CelestialBodyType.Quasar:
                var quasarScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Cosmic/Quasar.tscn");
                var quasar = quasarScene.Instantiate<Quasar>();
                quasar.Initialize(data);
                body = quasar;
                break;
                
            case CelestialBodyType.Supernova:
                var supernovaScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Cosmic/Supernova.tscn");
                var supernova = supernovaScene.Instantiate<Supernova>();
                supernova.Initialize(data);
                body = supernova;
                break;
                
            case CelestialBodyType.BlackHole:
                var blackHoleScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Cosmic/BlackHole.tscn");
                var blackHole = blackHoleScene.Instantiate<BlackHole>();
                blackHole.Initialize(data);
                body = blackHole;
                break;
                
            case CelestialBodyType.AsteroidBelt:
                var asteroidBeltScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Cosmic/AsteroidBelt.tscn");
                var asteroidBelt = asteroidBeltScene.Instantiate<AsteroidBelt>();
                asteroidBelt.Initialize(data);
                body = asteroidBelt;
                break;
                
            case CelestialBodyType.Comet:
                var cometScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Scenes/Cosmic/Comet.tscn");
                var comet = cometScene.Instantiate<Comet>();
                comet.Initialize(data);
                body = comet;
                break;
                
            default:
                Logger.Warning($"Unknown celestial body type: {data.BodyType}");
                break;
        }
        
        return body;
    }
    
    /// <summary>
    /// Sets the camera target position, causing smooth movement to that location
    /// </summary>
    public void SetCameraTarget(Vector2 worldPos)
    {
        _targetCameraPosition = worldPos;
    }
    
    /// <summary>
    /// Sets the initial revealed state for the fog of war in the model.
    /// Reveals the starting hex (0,0) and its immediate neighbors.
    /// This modifies the UniverseModel *before* visuals are created.
    /// </summary>
    private void InitializeFogOfWar(UniverseModel universe)
    {
        HexCoordinates startCoords = HexCoordinates.Zero;
        List<HexCoordinates> revealCoords = HexUtils.GetSpiral(startCoords, 1); // Radius 1 includes center + neighbors

        foreach (var coords in revealCoords)
        {
            var cell = universe.GetCell(coords);
            if (cell != null)
            {
                cell.IsRevealed = true;
            }
        }
        Logger.Info($"Fog of War initialized in model. Revealed {revealCoords.Count} hexes around {startCoords}.");
    }
    
    /// <summary>
    /// Clears the current universe in preparation for a new one
    /// </summary>
    private void ClearUniverse()
    {
        // Clear selection
        _selectionManager.ClearSelection();
        
        // Clear existing universe objects
        _celestialRegistry.Clear();
        _hexGrid.Clear();
        _hexGridManager.ClearHexagons();
    }
}
