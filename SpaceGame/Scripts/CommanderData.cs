using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Scripts.Serialization.Blueprint;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts;

/// <summary>
/// Serializable data class for Commanders.
/// This is a plain C# class (not a Resource) that can be serialized to/from JSON.
/// </summary>
public class CommanderData : GameDataBase
{
    /// <summary>
    /// Gets or sets the description of this commander.
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the ID of the fleet this commander is assigned to, if any.
    /// </summary>
    public string? FleetId { get; set; }

    /// <summary>
    /// Modifiers this commander provides to the fleet.
    /// </summary>
    public List<StatModifierData> Modifiers { get; set; } = new();

    /// <summary>
    /// Default constructor required for serialization.
    /// </summary>
    public CommanderData()
    {
    }

    public CommanderData(CommanderBlueprint blueprint)
        : base(blueprint.BlueprintId, blueprint.CommanderName)
    {
        Description = blueprint.Description;
        Modifiers = blueprint.Modifiers.Select(bpMod => (StatModifierData)bpMod.CreateInstance()).ToList();
    }

    /// <summary>
    /// Returns a string representation of this commander data.
    /// </summary>
    /// <returns>A string representation of this commander data.</returns>
    public override string ToString()
    {
        return $"CommanderData[{Id}]: {DisplayName} (BlueprintId: {BlueprintId})";
    }
}