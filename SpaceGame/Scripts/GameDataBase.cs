using System;

namespace SpaceGame.CodeGeneration;

/// <summary>
/// Base class for all serializable game data objects.
/// Game data objects are plain C# objects (not Resources) that can be serialized to/from JSON.
/// They are created from blueprints and contain runtime state.
/// </summary>
public abstract class GameDataBase
{
    /// <summary>
    /// Gets or sets the unique identifier for this game data instance.
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// Gets or sets the blueprint reference ID.
    /// This is used to link back to the blueprint that created this data object.
    /// </summary>
    public string BlueprintId { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the display name of this game data instance.
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;
    
    /// <summary>
    /// Default constructor that generates a new unique identifier.
    /// </summary>
    protected GameDataBase()
    {
        // Id is initialized in the property declaration
    }
    
    /// <summary>
    /// Constructor that initializes the object with a blueprint ID and display name.
    /// </summary>
    /// <param name="blueprintId">The ID of the blueprint that created this object.</param>
    /// <param name="displayName">The display name for this object.</param>
    protected GameDataBase(string? blueprintId, string? displayName)
    {
        BlueprintId = blueprintId ?? throw new ArgumentNullException(nameof(blueprintId));
        DisplayName = displayName ?? string.Empty;
    }
    
    /// <summary>
    /// Returns a string representation of this game data object.
    /// </summary>
    /// <returns>A string containing the object's ID and display name.</returns>
    public override string ToString()
    {
        return $"{GetType().Name} [Id={Id}, DisplayName={DisplayName}]";
    }
    
    /// <summary>
    /// Generates a new unique ID for this data object.
    /// Useful when creating copies or duplicating data objects.
    /// </summary>
    public void RegenerateId()
    {
        Id = Guid.NewGuid().ToString();
    }
} 