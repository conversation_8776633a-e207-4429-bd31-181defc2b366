using Godot;
using SpaceGame.Scripts.Serialization.Blueprint;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts;

/// <summary>
/// Blueprint for ship stats.
/// This is a Resource-based definition that defines the base properties of ship stats.
/// </summary>
public partial class ShipStatsBlueprint : Resource, IBlueprint
{
    /// <summary>
    /// Gets or sets the unique identifier for this blueprint.
    /// </summary>
    [Export] public string BlueprintId { get; set; } = string.Empty;

    /// <summary>
    /// Gets the display name of this blueprint.
    /// </summary>
    [Export] public string DisplayName { get; set; } = "Ship Stats Blueprint";

    // Core Stats
    [Export] public float MaxHealth { get; set; } = 100f;
    [Export] public float Armor { get; set; } = 5f;
    [Export] public float Shield { get; set; } = 50f; // Base shield value from ship's generator
    [Export] public float ShieldRegen { get; set; } = 2f; // Base shield regen
    [Export] public float Speed { get; set; } = 100f;
    [Export] public float TurnRate { get; set; } = 0.5f;
    [Export] public float MaxEnergy { get; set; } = 100f;
    [Export] public float EnergyRegen { get; set; } = 5f;
    [Export] public float SensorRange { get; set; } = 1000f; // Base detection range
    [Export] public float CargoCapacity { get; set; } = 0f;

    /// <summary>
    /// Default constructor.
    /// </summary>
    public ShipStatsBlueprint() { }

    /// <summary>
    /// Creates a new ShipStatsData instance from this blueprint.
    /// </summary>
    /// <returns>A new ShipStatsData instance with values copied from this blueprint.</returns>
    public ShipStatsData CreateInstanceFromBlueprint()
    {
        ShipStatsData instance = new ShipStatsData
        {
            BlueprintId = this.BlueprintId,
            DisplayName = this.DisplayName,
            
            // Copy core stats
            MaxHealth = this.MaxHealth,
            Armor = this.Armor,
            Shield = this.Shield,
            ShieldRegen = this.ShieldRegen,
            Speed = this.Speed,
            TurnRate = this.TurnRate,
            MaxEnergy = this.MaxEnergy,
            EnergyRegen = this.EnergyRegen,
            SensorRange = this.SensorRange,
            CargoCapacity = this.CargoCapacity
        };

        return instance;
    }

    /// <summary>
    /// Implements the IBlueprint interface.
    /// </summary>
    /// <returns>A new instance of IGameData created from this blueprint.</returns>
    public GameDataBase CreateInstance()
    {
        ShipStatsData data = CreateInstanceFromBlueprint();
        return (GameDataBase)data;
    }

    /// <summary>
    /// String representation for debugging.
    /// </summary>
    public override string ToString()
    {
        return $"ShipStatsBlueprint[{BlueprintId}]: HP:{MaxHealth:F0}, Armor:{Armor:F1}, Shield:{Shield:F0}/{ShieldRegen:F1}, Spd:{Speed:F0}, Turn:{TurnRate:F2}, Energy:{MaxEnergy:F0}/{EnergyRegen:F1}, Sensor:{SensorRange:F0}";
    }
} 