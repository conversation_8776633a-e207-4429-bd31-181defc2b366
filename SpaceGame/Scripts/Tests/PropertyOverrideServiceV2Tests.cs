using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Ships.Data;
using SpaceGame.Utility;
using SpaceGame.Utility.Interfaces;

namespace SpaceGame.Scripts.Tests
{
    /// <summary>
    /// Tests for the PropertyOverrideServiceV2 to ensure it correctly applies overrides
    /// using the IJsonPopulatable interface and handles nested objects properly.
    /// </summary>
    [GlobalClass]
    public partial class PropertyOverrideServiceV2Tests : Node
    {
        public override void _Ready()
        {
            RunTests();
        }

        /// <summary>
        /// Run all tests for the PropertyOverrideServiceV2.
        /// </summary>
        public void RunTests()
        {
            GD.Print("Running PropertyOverrideServiceV2 Tests...");
            
            TestBasicPropertyOverrides();
            TestIJsonPopulatableInterface();
            TestNestedObjectOverrides();
            
            GD.Print("PropertyOverrideServiceV2 Tests completed.");
        }
        
        /// <summary>
        /// Tests that basic property overrides work correctly.
        /// </summary>
        private void TestBasicPropertyOverrides()
        {
            GD.Print("  Testing basic property overrides...");
            
            // Create a test ship entity
            var shipData = new ShipEntityData
            {
                Name = "TestShip",
                MaxSpeed = 100.0f,
                RotationSpeed = 180.0f
            };
            
            // Create overrides for only one property
            var overrides = new Dictionary<string, object>
            {
                { "MaxSpeed", 200.0f }
            };
            
            // Apply the overrides
            PropertyOverrideServiceV2.ApplyOverrides(shipData, overrides);
            
            // Verify that only the specified property was changed
            if (shipData.MaxSpeed != 200.0f)
            {
                GD.PrintErr("    FAILED: MaxSpeed was not overridden correctly");
            }
            
            // Verify that non-specified properties remain unchanged
            if (shipData.RotationSpeed != 180.0f)
            {
                GD.PrintErr("    FAILED: RotationSpeed was modified when it shouldn't have been");
            }
            
            if (shipData.Name != "TestShip")
            {
                GD.PrintErr("    FAILED: Name was modified when it shouldn't have been");
            }
            
            GD.Print("  Basic property overrides test completed.");
        }
        
        /// <summary>
        /// Tests that the IJsonPopulatable interface is used correctly.
        /// </summary>
        private void TestIJsonPopulatableInterface()
        {
            GD.Print("  Testing IJsonPopulatable interface...");
            
            // Create a test weapon data object (which implements IJsonPopulatable)
            var weaponData = new SpaceGame.Scripts.Battle.WeaponData
            {
                Id = "TestWeapon",
                WeaponName = "Original Name",
                Damage = 10.0f,
                FireRate = 1.0f
            };
            
            // Create JSON string with overrides
            string jsonOverrides = @"{
                ""WeaponName"": ""New Weapon Name"",
                ""Damage"": 25.0,
                ""FireRate"": 2.5
            }";
            
            // Convert JSON string to JsonElement
            JsonElement jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonOverrides);
            
            // Use PopulateFromJson directly (as would happen in OverrideManager)
            weaponData.PopulateFromJson(jsonElement, PropertyOverrideServiceV2.DefaultSerializerOptions);
            
            // Verify properties were updated
            if (weaponData.WeaponName != "New Weapon Name")
            {
                GD.PrintErr($"    FAILED: WeaponName was not updated. Expected 'New Weapon Name' but got '{weaponData.WeaponName}'");
            }
            
            if (Math.Abs(weaponData.Damage - 25.0f) > 0.001f)
            {
                GD.PrintErr($"    FAILED: Damage was not updated. Expected 25.0 but got {weaponData.Damage}");
            }
            
            if (Math.Abs(weaponData.FireRate - 2.5f) > 0.001f)
            {
                GD.PrintErr($"    FAILED: FireRate was not updated. Expected 2.5 but got {weaponData.FireRate}");
            }
            
            // Verify that non-specified properties remain unchanged
            if (weaponData.Id != "TestWeapon")
            {
                GD.PrintErr($"    FAILED: Id was modified when it shouldn't have been. Expected 'TestWeapon' but got '{weaponData.Id}'");
            }
            
            GD.Print("  IJsonPopulatable interface test completed.");
        }
        
        /// <summary>
        /// Tests that nested object overrides work correctly.
        /// </summary>
        private void TestNestedObjectOverrides()
        {
            GD.Print("  Testing nested object overrides...");
            
            // Create a test ship entity with weapon slots
            var shipData = new ShipEntityData
            {
                Name = "TestShip",
                MaxSpeed = 100.0f
            };
            
            // Add weapon slots
            var slot1 = new WeaponSlotData
            {
                MountPointId = "Mount1",
                WeaponId = "Weapon1",
                DisplayVisuals = true
            };
            
            var slot2 = new WeaponSlotData
            {
                MountPointId = "Mount2",
                WeaponId = "Weapon2",
                DisplayVisuals = true
            };
            
            shipData.WeaponSlots.Add(slot1);
            shipData.WeaponSlots.Add(slot2);
            
            // Create JSON string with nested overrides
            string jsonOverrides = @"{
                ""MaxSpeed"": 200.0,
                ""WeaponSlots"": [
                    {
                        ""WeaponId"": ""Weapon3"",
                        ""DisplayVisuals"": false
                    }
                ]
            }";
            
            // Convert JSON string to JsonElement
            JsonElement jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonOverrides);
            
            // Use PopulateFromJson directly (as would happen in OverrideManager)
            shipData.PopulateFromJson(jsonElement, PropertyOverrideServiceV2.DefaultSerializerOptions);
            
            // Verify that MaxSpeed was changed
            if (Math.Abs(shipData.MaxSpeed - 200.0f) > 0.001f)
            {
                GD.PrintErr($"    FAILED: MaxSpeed was not updated. Expected 200.0 but got {shipData.MaxSpeed}");
            }
            
            // Verify that WeaponSlots collection still has the correct count
            if (shipData.WeaponSlots.Count != 2)
            {
                GD.PrintErr($"    FAILED: WeaponSlots collection count changed. Expected 2 slots, but got {shipData.WeaponSlots.Count}");
            }
            else
            {
                // Verify that MountPointId was preserved (not in the override)
                if (shipData.WeaponSlots[0].MountPointId != "Mount1")
                {
                    GD.PrintErr($"    FAILED: MountPointId was not preserved. Expected 'Mount1' but got '{shipData.WeaponSlots[0].MountPointId}'");
                }
                
                // Verify that WeaponId was changed (in the override)
                if (shipData.WeaponSlots[0].WeaponId != "Weapon3")
                {
                    GD.PrintErr($"    FAILED: WeaponId was not updated. Expected 'Weapon3' but got '{shipData.WeaponSlots[0].WeaponId}'");
                }
                
                // Verify that DisplayVisuals was changed (in the override)
                if (shipData.WeaponSlots[0].DisplayVisuals != false)
                {
                    GD.PrintErr($"    FAILED: DisplayVisuals was not updated. Expected 'false' but got '{shipData.WeaponSlots[0].DisplayVisuals}'");
                }
                
                // Verify that the second slot was completely untouched
                if (shipData.WeaponSlots[1].MountPointId != "Mount2" || 
                    shipData.WeaponSlots[1].WeaponId != "Weapon2" || 
                    shipData.WeaponSlots[1].DisplayVisuals != true)
                {
                    GD.PrintErr("    FAILED: Second weapon slot was modified when it shouldn't have been");
                }
            }
            
            GD.Print("  Nested object overrides test completed.");
        }
    }
}
