using Godot;
using System;
using System.Collections.Generic;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Ships.Data;
using SpaceGame.Utility;

namespace SpaceGame.Scripts.Tests
{
    /// <summary>
    /// Tests for the PropertyOverrideService to ensure it only modifies properties
    /// that are explicitly defined in the overrides dictionary.
    /// </summary>
    [GlobalClass]
    public partial class PropertyOverrideServiceTests : Node
    {
        public override void _Ready()
        {
            RunTests();
        }

        /// <summary>
        /// Run all tests for the PropertyOverrideService.
        /// </summary>
        public void RunTests()
        {
            GD.Print("Running PropertyOverrideService Tests...");
            
            TestBasicPropertyOverrides();
            TestCollectionPreservation();
            TestNestedObjectOverrides();
            
            GD.Print("PropertyOverrideService Tests completed.");
        }
        
        /// <summary>
        /// Tests that basic property overrides work correctly.
        /// </summary>
        private void TestBasicPropertyOverrides()
        {
            GD.Print("  Testing basic property overrides...");
            
            // Create a test ship entity
            var shipData = new ShipEntityData
            {
                Name = "TestShip",
                MaxSpeed = 100.0f,
                RotationSpeed = 180.0f
            };
            
            // Create overrides for only one property
            var overrides = new Dictionary<string, object>
            {
                { "MaxSpeed", 200.0f }
            };
            
            // Apply the overrides
            PropertyOverrideService.ApplyOverrides(shipData, overrides);
            
            // Verify that only the specified property was changed
            if (shipData.MaxSpeed != 200.0f)
            {
                GD.PrintErr("    FAILED: MaxSpeed was not overridden correctly");
            }
            
            // Verify that non-specified properties remain unchanged
            if (shipData.RotationSpeed != 180.0f)
            {
                GD.PrintErr("    FAILED: RotationSpeed was modified when it shouldn't have been");
            }
            
            if (shipData.Name != "TestShip")
            {
                GD.PrintErr("    FAILED: Name was modified when it shouldn't have been");
            }
            
            GD.Print("  Basic property overrides test completed.");
        }
        
        /// <summary>
        /// Tests that collections are preserved when not explicitly overridden.
        /// </summary>
        private void TestCollectionPreservation()
        {
            GD.Print("  Testing collection preservation...");
            
            // Create a test ship entity with weapon slots
            var shipData = new ShipEntityData
            {
                Name = "TestShip",
                MaxSpeed = 100.0f
            };
            
            // Add weapon slots
            var slot1 = new WeaponSlotData
            {
                MountPointId = "Mount1",
                WeaponId = "Weapon1"
            };
            
            var slot2 = new WeaponSlotData
            {
                MountPointId = "Mount2",
                WeaponId = "Weapon2"
            };
            
            shipData.WeaponSlots.Add(slot1);
            shipData.WeaponSlots.Add(slot2);
            
            // Create overrides for a property other than WeaponSlots
            var overrides = new Dictionary<string, object>
            {
                { "MaxSpeed", 200.0f }
            };
            
            // Apply the overrides
            PropertyOverrideService.ApplyOverrides(shipData, overrides);
            
            if (shipData.Name != "TestShip")
            {
                GD.PrintErr("    FAILED: Name was modified when it shouldn't have been");
            }
            
            // Verify that MaxSpeed was changed
            if (shipData.MaxSpeed != 200.0f)
            {
                GD.PrintErr("    FAILED: MaxSpeed was not overridden correctly");
            }
            
            // Verify that WeaponSlots collection was preserved
            if (shipData.WeaponSlots.Count != 2)
            {
                GD.PrintErr($"    FAILED: WeaponSlots collection was modified. Expected 2 slots, but got {shipData.WeaponSlots.Count}");
            }
            else
            {
                // Check that the slots are still correct
                if (shipData.WeaponSlots[0].MountPointId != "Mount1" || shipData.WeaponSlots[0].WeaponId != "Weapon1")
                {
                    GD.PrintErr("    FAILED: First weapon slot was modified");
                }
                
                if (shipData.WeaponSlots[1].MountPointId != "Mount2" || shipData.WeaponSlots[1].WeaponId != "Weapon2")
                {
                    GD.PrintErr("    FAILED: Second weapon slot was modified");
                }
            }
            
            GD.Print("  Collection preservation test completed.");
        }
        
        /// <summary>
        /// Tests that partial overrides of collection items work correctly.
        /// </summary>
        private void TestNestedObjectOverrides()
        {
            GD.Print("  Testing partial collection item overrides...");
            
            // Create a test ship entity with weapon slots
            var shipData = new ShipEntityData
            {
                Name = "TestShip",
                MaxSpeed = 100.0f
            };
            
            // Add weapon slots
            var slot1 = new WeaponSlotData
            {
                MountPointId = "Mount1",
                WeaponId = "Weapon1",
                DisplayVisuals = true
            };
            
            var slot2 = new WeaponSlotData
            {
                MountPointId = "Mount2",
                WeaponId = "Weapon2",
                DisplayVisuals = true
            };
            
            shipData.WeaponSlots.Add(slot1);
            shipData.WeaponSlots.Add(slot2);
            
            // Print the initial state for debugging
            GD.Print($"    Initial WeaponSlots[0]: MountPointId={shipData.WeaponSlots[0].MountPointId}, WeaponId={shipData.WeaponSlots[0].WeaponId}, DisplayVisuals={shipData.WeaponSlots[0].DisplayVisuals}");
            
            // Create a partial override for the first weapon slot
            // We only specify the properties we want to change
            var partialOverride = new Dictionary<string, object>
            {
                { "WeaponSlots", new List<Dictionary<string, object>> 
                    {
                        new Dictionary<string, object>
                        {
                            { "WeaponId", "Weapon3" },
                            { "DisplayVisuals", false }
                        }
                    }
                }
            };
            
            // Apply the overrides
            PropertyOverrideService.ApplyOverrides(shipData, partialOverride);
            
            // Print the state after applying overrides for debugging
            GD.Print($"    After override WeaponSlots[0]: MountPointId={shipData.WeaponSlots[0].MountPointId}, WeaponId={shipData.WeaponSlots[0].WeaponId}, DisplayVisuals={shipData.WeaponSlots[0].DisplayVisuals}");
            
            // Verify that WeaponSlots collection still has the correct count
            if (shipData.WeaponSlots.Count != 2)
            {
                GD.PrintErr($"    FAILED: WeaponSlots collection count changed. Expected 2 slots, but got {shipData.WeaponSlots.Count}");
            }
            else
            {
                // Verify that MountPointId was preserved (not in the override)
                if (shipData.WeaponSlots[0].MountPointId != "Mount1")
                {
                    GD.PrintErr($"    FAILED: MountPointId was not preserved. Expected 'Mount1' but got '{shipData.WeaponSlots[0].MountPointId}'");
                }
                
                // Verify that WeaponId was changed (in the override)
                if (shipData.WeaponSlots[0].WeaponId != "Weapon3")
                {
                    GD.PrintErr($"    FAILED: WeaponId was not updated. Expected 'Weapon3' but got '{shipData.WeaponSlots[0].WeaponId}'");
                }
                
                // Verify that DisplayVisuals was changed (in the override)
                if (shipData.WeaponSlots[0].DisplayVisuals != false)
                {
                    GD.PrintErr($"    FAILED: DisplayVisuals was not updated. Expected 'false' but got '{shipData.WeaponSlots[0].DisplayVisuals}'");
                }
                
                // Verify that the second slot was completely untouched
                if (shipData.WeaponSlots[1].MountPointId != "Mount2" || 
                    shipData.WeaponSlots[1].WeaponId != "Weapon2" || 
                    shipData.WeaponSlots[1].DisplayVisuals != true)
                {
                    GD.PrintErr("    FAILED: Second weapon slot was modified when it shouldn't have been");
                }
            }
            
            GD.Print("  Partial collection item overrides test completed.");
        }
    }
}
