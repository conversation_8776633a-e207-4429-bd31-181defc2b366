using Godot;

namespace SpaceGame.Scripts.Debug
{
    public partial class DebugGrid : Node2D
    {
        [Export] public bool IsEnabled { get; set; } = true;
        [Export] public int GridCellSize { get; set; } = 100;
        [Export] public Color GridColor { get; set; } = new Color(0.3f, 0.3f, 0.3f, 0.5f); // Semi-transparent dark grey

        public override void _Ready()
        {
            // Ensure it redraws if IsEnabled changes when not in tree, then added
            if (IsEnabled)
            {
                QueueRedraw();
            }
        }

        public override void _Process(double delta) // Using _Process for potential dynamic changes
        {
            // Request redraw if enabled state might have changed by external script
            // Or if camera moves and we want a non-infinite grid (though current approach is camera-culled infinite)
            if (IsEnabled) 
            { 
                QueueRedraw();
            }
            else if (Visible) // If not enabled but was visible, request redraw to clear
            {
                 QueueRedraw(); // This will result in _Draw doing nothing if !IsEnabled
            }
        }

        public override void _Draw()
        {
            if (!IsEnabled || GridCellSize <= 0)
            {
                return;
            }

            var viewport = GetViewport();
            if (viewport == null) return;

            Rect2 visibleRect = viewport.GetVisibleRect();
            Transform2D canvasTransform = viewport.CanvasTransform;
            Transform2D globalCanvasTransform = GetGlobalTransform() * canvasTransform;
            
            // Transform the visible rectangle to local coordinates of this Node2D
            // This handles cases where the DebugGrid node itself might be offset or scaled, 
            // though typically it would be at (0,0) with no rotation/scale under a canvas layer or main scene root.
            Rect2 localVisibleRect = globalCanvasTransform.AffineInverse() * visibleRect;

            float startX = Mathf.Floor(localVisibleRect.Position.X / GridCellSize) * GridCellSize;
            float startY = Mathf.Floor(localVisibleRect.Position.Y / GridCellSize) * GridCellSize;
            float endX = Mathf.Ceil((localVisibleRect.Position.X + localVisibleRect.Size.X) / GridCellSize) * GridCellSize;
            float endY = Mathf.Ceil((localVisibleRect.Position.Y + localVisibleRect.Size.Y) / GridCellSize) * GridCellSize;

            // Draw vertical lines
            for (float x = startX; x <= endX; x += GridCellSize)
            {
                DrawLine(new Vector2(x, localVisibleRect.Position.Y),
                         new Vector2(x, localVisibleRect.Position.Y + localVisibleRect.Size.Y),
                         GridColor, 1.0f);
            }

            // Draw horizontal lines
            for (float y = startY; y <= endY; y += GridCellSize)
            {
                DrawLine(new Vector2(localVisibleRect.Position.X, y),
                         new Vector2(localVisibleRect.Position.X + localVisibleRect.Size.X, y),
                         GridColor, 1.0f);
            }
        }
    }
}
