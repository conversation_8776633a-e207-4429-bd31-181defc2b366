using Godot;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Battle.Movement;

namespace SpaceGame.Scripts.Debug
{
    public partial class VelocityDebugVisualizer : Node2D
    {
        [Export]
        public bool IsEnabled { get; set; } = true;

        private ShipEntity _battleEntity;
        private BattleEntityMovementController _movementController;

        private const float MAX_LINE_LENGTH = 100.0f; // Max pixels for the visualization line
        private const float SPEED_FOR_MAX_LENGTH = 500.0f; // Speed at which line reaches max length

        public VelocityDebugVisualizer() { }

        public override void _Ready()
        {
            _battleEntity = GetParent<ShipEntity>();
            if (_battleEntity == null)
            {
                Logger.Error($"VelocityDebugVisualizer on {Name} could not find parent BattleEntity. Disabling visualizer.");
                SetProcess(false);
                SetPhysicsProcess(false);
                return;
            }

            _movementController = _battleEntity.MovementController;
            if (_movementController == null)
            {
                Logger.Error($"VelocityDebugVisualizer on {_battleEntity.Name} could not find MovementController. Disabling visualizer.");
                SetProcess(false);
                SetPhysicsProcess(false);
                return;
            }
        }

        public override void _PhysicsProcess(double delta)
        {
            if (!IsEnabled || _movementController == null)
            {
                QueueRedraw(); // Request a redraw to clear the visual if it was previously enabled
                return;
            }

            // Request a redraw to update the visualization
            QueueRedraw();
        }

        public override void _Draw()
        {
            if (!IsEnabled || _movementController == null || _battleEntity == null)
            {
                return;
            }

            // Get velocity in world space
            Vector2 velocity = _movementController.GetCurrentVelocity();
            float speed = velocity.Length();

            if (speed < 0.1f) // Don't draw if not moving
            {
                return;
            }

            // Calculate the end point in world space
            Vector2 startPos = _battleEntity.GlobalPosition;
            Vector2 endPos = startPos + velocity.Normalized() * Mathf.Min(MAX_LINE_LENGTH, (speed / SPEED_FOR_MAX_LENGTH) * MAX_LINE_LENGTH);

            // Convert world space positions to local space for drawing
            Vector2 localStart = ToLocal(startPos);
            Vector2 localEnd = ToLocal(endPos);

            // Determine color based on speed (e.g., green to red)
            float speedRatio = Mathf.Clamp(speed / SPEED_FOR_MAX_LENGTH, 0.0f, 1.0f);
            Color lineColor = Colors.Green.Lerp(Colors.Red, speedRatio);
            
            // Draw the line in local space
            DrawLine(localStart, localEnd, lineColor, 2.0f);

            // Draw arrowhead if line is long enough
            if ((localEnd - localStart).Length() > 5.0f)
            {
                Vector2 arrowDir = (localEnd - localStart).Normalized();
                Vector2 arrowSide1 = localEnd + arrowDir.Rotated(Mathf.DegToRad(150)) * 8.0f;
                Vector2 arrowSide2 = localEnd + arrowDir.Rotated(Mathf.DegToRad(-150)) * 8.0f;
                DrawLine(localEnd, arrowSide1, lineColor, 2.0f);
                DrawLine(localEnd, arrowSide2, lineColor, 2.0f);
            }
        }
    }
}
