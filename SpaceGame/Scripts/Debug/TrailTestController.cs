using Godot;
using SpaceGame.Scripts.Battle.Components;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.Debug
{
    /// <summary>
    /// Simple controller for testing ship trail effects.
    /// Allows manual control of a ship to see how the trails look.
    /// </summary>
    public partial class TrailTestController : Node
    {
        /// <summary>
        /// The ship entity to control for testing.
        /// </summary>
        [Export]
        public ShipEntity TestShip { get; set; }
        
        /// <summary>
        /// Movement speed for testing.
        /// </summary>
        [Export]
        public float TestSpeed { get; set; } = 300.0f;
        
        /// <summary>
        /// Rotation speed for testing.
        /// </summary>
        [Export]
        public float TestRotationSpeed { get; set; } = 180.0f;
        
        private ShipTrailComponent _trailComponent;
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            if (TestShip == null)
            {
                Logger.Warning("TrailTestController: No test ship assigned.");
                return;
            }
            
            // Get the trail component
            _trailComponent = TestShip.TrailComponent as ShipTrailComponent;
            if (_trailComponent == null)
            {
                Logger.Warning("TrailTestController: Test ship has no trail component.");
            }
            
            Logger.Info("TrailTestController: Use WASD to move the ship and test trails.");
            Logger.Info("TrailTestController: Use Q/E to rotate the ship.");
            Logger.Info("TrailTestController: Use T to toggle trails on/off.");
            Logger.Info("TrailTestController: Use R to clear trails.");
        }
        
        /// <summary>
        /// Called every frame to handle input and update the test ship.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        public override void _Process(double delta)
        {
            base._Process(delta);
            
            if (TestShip == null)
                return;
                
            HandleInput((float)delta);
        }
        
        /// <summary>
        /// Handles input for controlling the test ship.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        private void HandleInput(float delta)
        {
            Vector2 inputVector = Vector2.Zero;
            float rotationInput = 0.0f;
            
            // Movement input
            if (Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W))
                inputVector.Y -= 1.0f;
            if (Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S))
                inputVector.Y += 1.0f;
            if (Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A))
                inputVector.X -= 1.0f;
            if (Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D))
                inputVector.X += 1.0f;
                
            // Rotation input
            if (Input.IsKeyPressed(Key.Q))
                rotationInput -= 1.0f;
            if (Input.IsKeyPressed(Key.E))
                rotationInput += 1.0f;
                
            // Trail control input
            if (Input.IsActionJustPressed("ui_accept") || Input.IsKeyPressed(Key.T))
            {
                if (_trailComponent != null)
                {
                    _trailComponent.SetTrailEnabled(!_trailComponent.TrailEnabled);
                    Logger.Info($"TrailTestController: Trails {(_trailComponent.TrailEnabled ? "enabled" : "disabled")}");
                }
            }
            
            if (Input.IsKeyPressed(Key.R))
            {
                if (_trailComponent != null)
                {
                    _trailComponent.ClearTrail();
                    Logger.Info("TrailTestController: Trails cleared");
                }
            }
            
            // Apply movement
            if (inputVector.LengthSquared() > 0.0f)
            {
                inputVector = inputVector.Normalized();
                Vector2 movement = inputVector * TestSpeed * delta;
                TestShip.GlobalPosition += movement;
                TestShip.Velocity = inputVector * TestSpeed;
                
                // Update thruster intensity based on movement
                if (TestShip.Thruster != null)
                {
                    TestShip.Thruster.SetIntensity(inputVector.Length());
                }
            }
            else
            {
                TestShip.Velocity = Vector2.Zero;
                if (TestShip.Thruster != null)
                {
                    TestShip.Thruster.SetIntensity(0.0f);
                }
            }
            
            // Apply rotation
            if (Mathf.Abs(rotationInput) > 0.0f)
            {
                TestShip.GlobalRotationDegrees += rotationInput * TestRotationSpeed * delta;
            }
        }
    }
}
