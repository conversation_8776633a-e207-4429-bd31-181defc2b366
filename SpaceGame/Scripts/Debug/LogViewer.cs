using System;
using System.Collections.Generic;
using System.Linq;
using ImGuiNET;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Debug;

/// <summary>
/// A debug UI window that displays and filters log messages.
/// </summary>
public class LogViewer
{
    private readonly List<Logger.LogEventArgs> _logEntries = new();
    private readonly int _maxLogEntries = 5000; // Limit to prevent memory issues
    private bool _autoScroll = true;
    private string _typeFilter = "";
    private string _methodFilter = "";
    private string _messageFilter = "";
    private Logger.LogLevelEnum _minLogLevel = Logger.LogLevelEnum.Debug;
    private readonly Dictionary<Logger.LogLevelEnum, bool> _logLevelFilters = new()
    {
        { Logger.LogLevelEnum.Trace, true },
        { Logger.LogLevelEnum.Debug, true },
        { Logger.LogLevelEnum.Info, true },
        { Logger.LogLevelEnum.Warning, true },
        { Logger.LogLevelEnum.Error, true }
    };

    public LogViewer()
    {
        // Subscribe to log events
        Logger.LogEvent += OnLogEvent;
    }

    ~LogViewer()
    {
        // Unsubscribe to prevent memory leaks
        Logger.LogEvent -= OnLogEvent;
    }

    private void OnLogEvent(object? sender, Logger.LogEventArgs e)
    {
        // Add the log entry to our collection
        _logEntries.Add(e);
        
        // Trim the collection if it gets too large
        if (_logEntries.Count > _maxLogEntries)
        {
            _logEntries.RemoveAt(0);
        }
    }

    /// <summary>
    /// Draws the log viewer window using ImGui.
    /// </summary>
    /// <param name="show">Reference to a boolean that controls whether the window is shown.</param>
    public void Draw(ref bool show)
    {
        if (!show) return;

        if (ImGui.Begin("Log Viewer", ref show))
        {
            // Filter controls
            DrawFilterControls();
            
            ImGui.Separator();
            
            // Log entries table
            DrawLogTable();
        }
        ImGui.End();
    }

    private void DrawFilterControls()
    {
        // Log level filter
        if (ImGui.BeginCombo("Min Log Level", _minLogLevel.ToString()))
        {
            foreach (Logger.LogLevelEnum level in Enum.GetValues(typeof(Logger.LogLevelEnum)))
            {
                if (level == Logger.LogLevelEnum.Off) continue; // Skip "Off" level
                
                bool isSelected = level == _minLogLevel;
                if (ImGui.Selectable(level.ToString(), isSelected))
                {
                    _minLogLevel = level;
                }
                
                if (isSelected)
                {
                    ImGui.SetItemDefaultFocus();
                }
            }
            ImGui.EndCombo();
        }
        
        // Log level checkboxes for individual toggling
        ImGui.Text("Log Levels:");
        ImGui.SameLine();
        
        foreach (var level in _logLevelFilters.Keys.ToList())
        {
            bool isChecked = _logLevelFilters[level];
            if (ImGui.Checkbox(level.ToString(), ref isChecked))
            {
                _logLevelFilters[level] = isChecked;
            }
            ImGui.SameLine();
        }
        ImGui.NewLine();
        
        // Type filter
        ImGui.AlignTextToFramePadding();
        ImGui.Text("Type:");
        ImGui.SameLine();
        ImGui.SetNextItemWidth(200);
        ImGui.InputText("##TypeFilter", ref _typeFilter, 100);
        
        ImGui.SameLine();
        
        // Method filter
        ImGui.AlignTextToFramePadding();
        ImGui.Text("Method:");
        ImGui.SameLine();
        ImGui.SetNextItemWidth(200);
        ImGui.InputText("##MethodFilter", ref _methodFilter, 100);
        
        // Message filter
        ImGui.AlignTextToFramePadding();
        ImGui.Text("Message:");
        ImGui.SameLine();
        ImGui.SetNextItemWidth(300);
        ImGui.InputText("##MessageFilter", ref _messageFilter, 100);
        
        ImGui.SameLine();
        
        // Clear filters button
        if (ImGui.Button("Clear Filters"))
        {
            _typeFilter = "";
            _methodFilter = "";
            _messageFilter = "";
        }
        
        ImGui.SameLine();
        
        // Clear logs button
        if (ImGui.Button("Clear Logs"))
        {
            _logEntries.Clear();
        }
        
        // Auto-scroll checkbox
        ImGui.SameLine();
        ImGui.Checkbox("Auto-scroll", ref _autoScroll);
    }

    private void DrawLogTable()
    {
        // Calculate the filtered logs
        var filteredLogs = _logEntries
            .Where(log => log.LogLevel >= _minLogLevel && _logLevelFilters[log.LogLevel])
            .Where(log => string.IsNullOrEmpty(_typeFilter) || log.CallerInfo.TypeName.Contains(_typeFilter, StringComparison.OrdinalIgnoreCase))
            .Where(log => string.IsNullOrEmpty(_methodFilter) || log.CallerInfo.MethodName.Contains(_methodFilter, StringComparison.OrdinalIgnoreCase))
            .Where(log => string.IsNullOrEmpty(_messageFilter) || log.Message.Contains(_messageFilter, StringComparison.OrdinalIgnoreCase))
            .ToList();
        
        // Display count of filtered logs
        ImGui.Text($"Showing {filteredLogs.Count} of {_logEntries.Count} logs");
        
        if (ImGui.BeginTable("LogTable", 5, ImGuiTableFlags.Borders | ImGuiTableFlags.Resizable | ImGuiTableFlags.ScrollY))
        {
            ImGui.TableSetupColumn("Time", ImGuiTableColumnFlags.WidthFixed, 160);
            ImGui.TableSetupColumn("Level", ImGuiTableColumnFlags.WidthFixed, 70);
            ImGui.TableSetupColumn("Type", ImGuiTableColumnFlags.WidthFixed, 150);
            ImGui.TableSetupColumn("Method", ImGuiTableColumnFlags.WidthFixed, 150);
            ImGui.TableSetupColumn("Message", ImGuiTableColumnFlags.WidthStretch);
            ImGui.TableHeadersRow();
            
            foreach (var log in filteredLogs)
            {
                ImGui.TableNextRow();
                
                // Time column
                ImGui.TableSetColumnIndex(0);
                ImGui.Text(log.Timestamp.ToString("HH:mm:ss.fff"));
                
                // Level column with color
                ImGui.TableSetColumnIndex(1);
                System.Numerics.Vector4 levelColor = GetColorForLogLevel(log.LogLevel);
                ImGui.TextColored(levelColor, log.LogLevel.ToString());
                
                // Type column
                ImGui.TableSetColumnIndex(2);
                ImGui.Text(log.CallerInfo.TypeName);
                
                // Method column
                ImGui.TableSetColumnIndex(3);
                ImGui.Text(log.CallerInfo.MethodName);
                
                // Message column
                ImGui.TableSetColumnIndex(4);
                ImGui.Text(log.Message);
            }
            
            // Auto-scroll to bottom if enabled
            if (_autoScroll && filteredLogs.Count > 0)
            {
                ImGui.SetScrollHereY(1.0f);
            }
            
            ImGui.EndTable();
        }
    }

    private System.Numerics.Vector4 GetColorForLogLevel(Logger.LogLevelEnum logLevel)
    {
        return logLevel switch
        {
            Logger.LogLevelEnum.Trace => new System.Numerics.Vector4(0.5f, 0.5f, 0.5f, 1.0f), // Gray
            Logger.LogLevelEnum.Debug => new System.Numerics.Vector4(0.0f, 0.8f, 0.0f, 1.0f), // Green
            Logger.LogLevelEnum.Info => new System.Numerics.Vector4(1.0f, 1.0f, 1.0f, 1.0f),  // White
            Logger.LogLevelEnum.Warning => new System.Numerics.Vector4(1.0f, 0.8f, 0.0f, 1.0f), // Yellow
            Logger.LogLevelEnum.Error => new System.Numerics.Vector4(1.0f, 0.0f, 0.0f, 1.0f),  // Red
            _ => new System.Numerics.Vector4(1.0f, 1.0f, 1.0f, 1.0f)  // Default white
        };
    }
}
