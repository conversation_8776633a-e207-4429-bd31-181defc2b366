using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Helpers;

namespace SpaceGame.Debug;

/// <summary>
/// Static registry for debug objects that can be inspected at runtime via ImGui.
/// </summary>
public static class DebugRegistry
{
    private static readonly Dictionary<string, (object obj, string category)> _registeredObjects = new();
    
    /// <summary>
    /// Event raised when the registry is modified (object added or removed).
    /// </summary>
    public static event Action? OnRegistryChanged;
    
    /// <summary>
    /// Registers an object for debug inspection.
    /// </summary>
    /// <param name="key">Unique identifier for the object</param>
    /// <param name="obj">The object to register</param>
    /// <param name="category">Optional category for grouping objects (defaults to "Default")</param>
    public static void RegisterObject(string key, object obj, string category = "Default")
    {
        if (string.IsNullOrEmpty(key))
        {
            Logger.Warning("Cannot register object with null or empty key");
            return;
        }
        
        if (obj == null)
        {
            Logger.Warning($"Cannot register null object with key '{key}'");
            return;
        }
        
        _registeredObjects[key] = (obj, category);
        Logger.Debug($"Registered debug object '{key}' in category '{category}'");
        OnRegistryChanged?.Invoke();
    }
    
    /// <summary>
    /// Unregisters an object from the debug registry.
    /// </summary>
    /// <param name="key">The key of the object to unregister</param>
    /// <returns>True if the object was found and removed, false otherwise</returns>
    public static bool UnregisterObject(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            Logger.Warning("Cannot unregister object with null or empty key");
            return false;
        }
        
        bool removed = _registeredObjects.Remove(key);
        if (removed)
        {
            Logger.Debug($"Unregistered debug object '{key}'");
            OnRegistryChanged?.Invoke();
        }
        else
        {
            Logger.Debug($"Failed to unregister debug object '{key}': not found");
        }
        
        return removed;
    }
    
    /// <summary>
    /// Gets an object from the registry by its key.
    /// </summary>
    /// <param name="key">The key of the object to retrieve</param>
    /// <returns>The registered object, or null if not found</returns>
    public static object? GetObject(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            Logger.Warning("Cannot get object with null or empty key");
            return null;
        }
        
        if (_registeredObjects.TryGetValue(key, out var entry))
        {
            return entry.obj;
        }
        
        Logger.Debug($"Debug object '{key}' not found in registry");
        return null;
    }
    
    /// <summary>
    /// Gets all registered objects with their keys and categories.
    /// </summary>
    /// <returns>Enumerable of tuples containing key, object, and category</returns>
    public static IEnumerable<(string key, object obj, string category)> GetAllRegisteredObjects()
    {
        return _registeredObjects.Select(kvp => (kvp.Key, kvp.Value.obj, kvp.Value.category));
    }
    
    /// <summary>
    /// Gets all unique categories of registered objects.
    /// </summary>
    /// <returns>Enumerable of category names</returns>
    public static IEnumerable<string> GetCategories()
    {
        return _registeredObjects.Values.Select(v => v.category).Distinct();
    }
    
    /// <summary>
    /// Gets all objects in a specific category.
    /// </summary>
    /// <param name="category">The category to filter by</param>
    /// <returns>Enumerable of tuples containing key and object</returns>
    public static IEnumerable<(string key, object obj)> GetObjectsByCategory(string category)
    {
        if (string.IsNullOrEmpty(category))
        {
            Logger.Warning("Cannot get objects with null or empty category");
            return Enumerable.Empty<(string, object)>();
        }
        
        return _registeredObjects
            .Where(kvp => kvp.Value.category == category)
            .Select(kvp => (kvp.Key, kvp.Value.obj));
    }
}
