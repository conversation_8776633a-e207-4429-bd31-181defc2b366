namespace SpaceGame.Scripts;

using Godot;
using System;

// Represents CORE ship stats (hull, base power), not equipment stats.
// Not a Resource itself, but a data container used by others.
public partial class ShipStats : Resource
{
    // Core Stats
    public float MaxHealth { get; set; } = 100f;
    public float Armor { get; set; } = 5f;
    public float Shield { get; set; } = 50f; // Base shield value from ship's generator
    public float ShieldRegen { get; set; } = 2f; // Base shield regen
    public float Speed { get; set; } = 100f;
    public float TurnRate { get; set; } = 0.5f;
    public float MaxEnergy { get; set; } = 100f;
    public float EnergyRegen { get; set; } = 5f;
    public float SensorRange { get; set; } = 1000f; // Base detection range
    public float CargoCapacity { get; set; } = 0f;
    // Add other relevant CORE stats...

    public ShipStats() { } // Needed for initialization

    // Copy constructor for easy duplication
    public ShipStats(ShipStats other)
    {
        if (other == null) return; // Safety check

        MaxHealth = other.MaxHealth;
        Armor = other.Armor;
        Shield = other.Shield;
        ShieldRegen = other.ShieldRegen;
        Speed = other.Speed;
        TurnRate = other.TurnRate;
        MaxEnergy = other.MaxEnergy;
        EnergyRegen = other.EnergyRegen;
        SensorRange = other.SensorRange;
        CargoCapacity = other.CargoCapacity;
        // Copy other CORE stats...
    }

    // String representation for debugging
    public override string ToString()
    {
        return $"HP:{MaxHealth:F0}, Armor:{Armor:F1}, Shield:{Shield:F0}/{ShieldRegen:F1}, Spd:{Speed:F0}, Turn:{TurnRate:F2}, Energy:{MaxEnergy:F0}/{EnergyRegen:F1}, Sensor:{SensorRange:F0}";
    }
}