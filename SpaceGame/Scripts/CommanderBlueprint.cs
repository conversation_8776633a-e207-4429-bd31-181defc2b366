using System;
using System.Collections.Generic;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts;

using Godot;
using Godot.Collections; // Use Godot Collections for Array export
using SpaceGame.Helpers;
using SpaceGame.Scripts.Serialization.Blueprint;

// Resource blueprint for a fleet commander (hero unit).
[GlobalClass]
public partial class CommanderBlueprint : Resource, IBlueprint
{
    [Export] public string CommanderId { get; set; } // Unique identifier for this commander
    
    [Export] public string CommanderName { get; set; } = "Unnamed Commander";
    
    [Export] public Texture2D Portrait { get; set; }
    
    [Export(PropertyHint.MultilineText)] public string Description { get; set; } = "";

    // Modifiers this commander provides to the fleet
    [Export] public Array<StatModifierBlueprint> Modifiers { get; set; } = new();

    // IBlueprint implementation
    public string BlueprintId
    {
        get => CommanderId;
        set => CommanderId = value;
    }

    public string DisplayName => CommanderName;

    public CommanderBlueprint() {
        // Generate a unique ID if one isn't already set
        if (string.IsNullOrEmpty(CommanderId))
        {
            CommanderId = Guid.NewGuid().ToString();
        }
    } // Required for Godot Resources
    
    /// <summary>
    /// Creates a new data instance from this blueprint.
    /// </summary>
    /// <returns>A new CommanderData instance.</returns>
    public GameDataBase CreateInstance()
    {
        CommanderData data = CreateCommanderData();
        return (GameDataBase)data;
    }
    
    /// <summary>
    /// Creates a new instance of CommanderData from this blueprint.
    /// </summary>
    /// <returns>A new CommanderData instance.</returns>
    public CommanderData CreateCommanderData()
    {
        var data = new CommanderData(this);
        Logger.Debug($"Created new commander data instance '{data.DisplayName}' with ID {data.Id} from blueprint {CommanderId}");
        return data;
    }
}