using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using SpaceGame.Universe.Model;

namespace SpaceGame.Scripts;

/// <summary>
/// Represents a faction in the game world.
/// </summary>
public class Faction
{
    /// <summary>
    /// Gets or sets the unique identifier for this faction.
    /// </summary>
    public FactionId Id { get; set; }
    
    /// <summary>
    /// Gets or sets the display name of this faction.
    /// </summary>
    public string DisplayName { get; set; }
    
    /// <summary>
    /// Gets or sets the faction type.
    /// </summary>
    public FactionType Type { get; set; }
    
    /// <summary>
    /// Gets or sets the diplomatic relationship level with the player faction.
    /// </summary>
    public RelationshipLevel PlayerRelationship { get; set; }
    
    /// <summary>
    /// Gets or sets the color used to represent this faction in the UI.
    /// </summary>
    public Color Color { get; set; }
    
    /// <summary>
    /// Gets or sets a description of this faction.
    /// </summary>
    public string Description { get; set; }
    
    /// <summary>
    /// Gets or sets whether this faction is controlled by the player.
    /// </summary>
    public bool IsPlayerFaction { get; set; } = false;
    
    /// <summary>
    /// Gets or sets the IDs of celestial bodies controlled by this faction.
    /// </summary>
    public HashSet<CelestialBodyId> ControlledPlanetIds { get; set; } = new HashSet<string>();
    
    /// <summary>
    /// Gets or sets the commanders available to this faction.
    /// Direct reference to commander objects, providing easier access than using IDs.
    /// </summary>
    public List<CommanderData> Commanders { get; set; } = new List<CommanderData>();
    
    /// <summary>
    /// Creates a new faction with default values.
    /// </summary>
    public Faction()
    {
        Id = Guid.NewGuid().ToString();
        DisplayName = "New Faction";
        Type = FactionType.None;
        PlayerRelationship = RelationshipLevel.Neutral;
        Color = Colors.White;
        Description = "";
        IsPlayerFaction = false;
        ControlledPlanetIds = new HashSet<string>();
        Commanders = new List<CommanderData>();
    }
    
    /// <summary>
    /// Creates a new faction with the specified values.
    /// </summary>
    /// <param name="displayName">The display name of the faction.</param>
    /// <param name="type">The faction type.</param>
    /// <param name="playerRelationship">The diplomatic relationship level with the player faction.</param>
    /// <param name="color">The color used to represent this faction in the UI.</param>
    /// <param name="description">A description of this faction.</param>
    /// <param name="isPlayerFaction">Whether this faction is controlled by the player.</param>
    public Faction(string displayName, FactionType type, RelationshipLevel playerRelationship, Color color, string description = "", bool isPlayerFaction = false)
    {
        Id = Guid.NewGuid().ToString();
        DisplayName = displayName;
        Type = type;
        PlayerRelationship = playerRelationship;
        Color = color;
        Description = description;
        IsPlayerFaction = isPlayerFaction;
        ControlledPlanetIds = new HashSet<string>();
        Commanders = new List<CommanderData>();
    }
    
    /// <summary>
    /// Adds a commander to this faction.
    /// </summary>
    /// <param name="commander">The commander to add.</param>
    /// <returns>True if the commander was added successfully, false if it was already in the faction.</returns>
    public bool AddCommander(CommanderData commander)
    {
        if (commander == null || Commanders.Any(c => c.Id == commander.Id))
        {
            return false;
        }
        
        Commanders.Add(commander);
        return true;
    }
    
    /// <summary>
    /// Removes a commander from this faction.
    /// </summary>
    /// <param name="commanderId">The ID of the commander to remove.</param>
    /// <returns>True if the commander was removed successfully, false if it wasn't in the faction.</returns>
    public bool RemoveCommander(CommanderId commanderId)
    {
        return Commanders.RemoveAll(c => c.Id == commanderId) > 0;
    }
    
    /// <summary>
    /// Gets a commander by its ID.
    /// </summary>
    /// <param name="commanderId">The ID of the commander to get.</param>
    /// <returns>The commander with the specified ID, or null if not found.</returns>
    public CommanderData? GetCommanderById(CommanderId commanderId)
    {
        return Commanders.FirstOrDefault(c => c.Id == commanderId);
    }
    
    /// <summary>
    /// Adds a planet to this faction's controlled planets using its ID.
    /// </summary>
    /// <param name="planetId">The ID of the planet to add.</param>
    /// <returns>True if the planet was added successfully, false if it was already controlled by this faction.</returns>
    public bool AddControlledPlanet(string planetId)
    {
        if (planetId == null) return false;
        return ControlledPlanetIds.Add(planetId);
    }
    
    /// <summary>
    /// Removes a planet from this faction's controlled planets using its ID.
    /// </summary>
    /// <param name="planetId">The ID of the planet to remove.</param>
    /// <returns>True if the planet was removed successfully, false if it wasn't controlled by this faction.</returns>
    public bool RemoveControlledPlanet(CelestialBodyId planetId)
    {
        return ControlledPlanetIds.Remove(planetId);
    }
    
    /// <summary>
    /// Checks if a planet is controlled by this faction.
    /// </summary>
    /// <param name="planetId">The ID of the planet to check.</param>
    /// <returns>True if the planet is controlled by this faction, false otherwise.</returns>
    public bool ControlsPlanet(string planetId)
    {
        return ControlledPlanetIds.Contains(planetId);
    }
    
    /// <summary>
    /// Returns a string representation of this faction.
    /// </summary>
    /// <returns>A string representation of this faction.</returns>
    public override string ToString()
    {
        string playerIndicator = IsPlayerFaction ? " [PLAYER]" : "";
        return $"Faction[{Id}]: {DisplayName}{playerIndicator} ({Type}) - Commanders: {Commanders.Count}, Planets: {ControlledPlanetIds.Count}";
    }
    
    /// <summary>
    /// Sets whether this faction is controlled by the player.
    /// </summary>
    /// <param name="isPlayerFaction">Whether this faction is controlled by the player.</param>
    public void SetPlayerFaction(bool isPlayerFaction)
    {
        IsPlayerFaction = isPlayerFaction;
    }
}