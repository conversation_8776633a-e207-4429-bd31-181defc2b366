using SpaceGame.Helpers;
using SpaceGame.Universe;
using SpaceGame.Universe.Hexagon;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Static class containing universe-related events.
    /// </summary>
    public static class UniverseEvents
    {
        /// <summary>
        /// Event triggered when a celestial body is selected.
        /// </summary>
        public delegate void CelestialBodySelectedEventHandler(CelestialBody? body);
        public static event CelestialBodySelectedEventHandler CelestialBodySelected = delegate { };

        /// <summary>
        /// Event triggered when a hexagon is clicked.
        /// </summary>
        public delegate void HexagonClickedEventHandler(Hexagon? hexagon);
        public static event HexagonClickedEventHandler HexagonClicked = delegate { };

        /// <summary>
        /// Event triggered when a hexagon is selected.
        /// </summary>
        public delegate void HexagonSelectedEventHandler(Hexagon? hexagon);
        public static event HexagonSelectedEventHandler HexagonSelected = delegate { };

        /// <summary>
        /// Raises the CelestialBodySelected event.
        /// </summary>
        /// <param name="body">The selected celestial body, or null if deselected.</param>
        public static void RaiseCelestialBodySelected(CelestialBody? body)
        {
            Logger.Debug($"Celestial body selected: {body}");
            CelestialBodySelected(body);
        }

        /// <summary>
        /// Raises the HexagonClicked event.
        /// </summary>
        /// <param name="hexagon">The clicked hexagon, or null if no hexagon was clicked.</param>
        public static void RaiseHexagonClicked(Hexagon? hexagon)
        {
            Logger.Debug($"Hexagon clicked: {hexagon}");
            HexagonClicked(hexagon);
        }

        /// <summary>
        /// Raises the HexagonSelected event.
        /// </summary>
        /// <param name="hexagon">The selected hexagon, or null if deselected.</param>
        public static void RaiseHexagonSelected(Hexagon? hexagon)
        {
            Logger.Debug($"Hexagon selected: {hexagon}");
            HexagonSelected(hexagon);
        }
    }
}
