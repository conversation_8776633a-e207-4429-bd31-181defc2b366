using SpaceGame.Helpers;
using SpaceGame.State;
using SpaceGame.Universe.Model;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Static class containing faction-related events.
    /// </summary>
    public static class StaticFactionEvents
    {
        /// <summary>
        /// Event triggered when a faction is created.
        /// </summary>
        public delegate void FactionCreatedEventHandler(Faction faction);
        public static event FactionCreatedEventHandler FactionCreated = delegate { };

        /// <summary>
        /// Event triggered when a faction is deleted.
        /// </summary>
        public delegate void FactionDeletedEventHandler(FactionState faction);
        public static event FactionDeletedEventHandler FactionDeleted = delegate { };
        
        /// <summary>
        /// Event triggered when a faction is updated.
        /// </summary>
        public delegate void FactionUpdatedEventHandler(Faction faction);
        public static event FactionUpdatedEventHandler FactionUpdated = delegate { };
        
        /// <summary>
        /// Event triggered when a planet's controlling faction changes.
        /// </summary>
        public delegate void PlanetControlChangedEventHandler(CelestialBodyData bodyData, FactionId? previousFactionId, FactionId? newFactionId);
        public static event PlanetControlChangedEventHandler PlanetControlChanged = delegate { };

        /// <summary>
        /// Raises the FactionCreated event.
        /// </summary>
        /// <param name="faction">The created faction.</param>
        public static void RaiseFactionCreated(Faction faction)
        {
            Logger.Debug($"Faction created: {faction.DisplayName} ({faction.Id})");
            FactionCreated(faction);
        }

        /// <summary>
        /// Raises the FactionDeleted event.
        /// </summary>
        /// <param name="faction">The deleted faction.</param>
        public static void RaiseFactionDeleted(FactionState faction)
        {
            Logger.Debug($"Faction deleted: {faction.Name} ({faction.Id})");
            FactionDeleted(faction);
        }

        /// <summary>
        /// Raises the FactionUpdated event.
        /// </summary>
        /// <param name="faction">The updated faction.</param>
        public static void RaiseFactionUpdated(Faction faction)
        {
            Logger.Debug($"Faction updated: {faction.DisplayName} ({faction.Id})");
            FactionUpdated(faction);
        }

        /// <summary>
        /// Raises the PlanetControlChanged event.
        /// </summary>
        /// <param name="bodyData">The celestial body whose control changed.</param>
        /// <param name="previousFactionId">The previous controlling faction ID, if any.</param>
        /// <param name="newFactionId">The new controlling faction ID, if any.</param>
        public static void RaisePlanetControlChanged(CelestialBodyData bodyData, FactionId? previousFactionId, FactionId? newFactionId)
        {
            Logger.Debug($"Planet control changed: {bodyData.Name} ({bodyData.Id}), from {previousFactionId} to {newFactionId}");
            PlanetControlChanged(bodyData, previousFactionId, newFactionId);
        }
    }
}
