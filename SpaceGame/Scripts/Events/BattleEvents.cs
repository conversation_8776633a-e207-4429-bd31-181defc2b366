using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;
using System;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Static class containing battle-related events.
    /// </summary>
    public static class BattleEvents
    {
        /// <summary>
        /// Event triggered when a battle is started.
        /// </summary>
        public delegate void BattleStartedEventHandler(BattleConfig config);
        public static event BattleStartedEventHandler BattleStarted = delegate { };

        /// <summary>
        /// Event triggered when a battle is completed.
        /// </summary>
        public delegate void BattleCompletedEventHandler(bool playerVictory);
        public static event BattleCompletedEventHandler BattleCompleted = delegate { };

        /// <summary>
        /// Event triggered when the player ship is destroyed.
        /// </summary>
        public static event EventHandler PlayerShipDestroyed = delegate { };

        /// <summary>
        /// Event triggered when an enemy ship is destroyed.
        /// </summary>
        public delegate void EnemyShipDestroyedEventHandler(ShipEntity entity);
        public static event EnemyShipDestroyedEventHandler EnemyShipDestroyed = delegate { };
        
        /// <summary>
        /// Event triggered when player control is given to an entity.
        /// </summary>
        public delegate void PlayerControlGivenEventHandler(ShipEntity entity);
        public static event PlayerControlGivenEventHandler PlayerControlGiven = delegate { };
        
        /// <summary>
        /// Event triggered when player control is removed from an entity.
        /// </summary>
        public delegate void PlayerControlRemovedEventHandler(ShipEntity entity);
        public static event PlayerControlRemovedEventHandler PlayerControlRemoved = delegate { };
        
        /// <summary>
        /// Event triggered when player control is switched between entities.
        /// </summary>
        public delegate void PlayerControlSwitchedEventHandler(ShipEntity previousEntity, ShipEntity currentEntity);
        public static event PlayerControlSwitchedEventHandler PlayerControlSwitched = delegate { };

        /// <summary>
        /// Event raised when a ship takes damage.
        /// </summary>
        public static event EventHandler<ShipDamagedEventArgs> ShipDamaged;
        
        /// <summary>
        /// Event raised when a ship is destroyed.
        /// </summary>
        public static event EventHandler<ShipDestroyedEventArgs> ShipDestroyed;
        
        /// <summary>
        /// Fires the ShipDamaged event.
        /// </summary>
        /// <param name="ship">The ship that was damaged.</param>
        /// <param name="damageInfo">Information about the damage.</param>
        public static void FireShipDamaged(ShipEntity ship, DamageInfo damageInfo)
        {
            ShipDamaged?.Invoke(null, new ShipDamagedEventArgs(ship, damageInfo));
        }
        
        /// <summary>
        /// Fires the ShipDestroyed event.
        /// </summary>
        /// <param name="ship">The ship that was destroyed.</param>
        /// <param name="damageInfo">Information about the final damage that destroyed the ship.</param>
        public static void FireShipDestroyed(ShipEntity ship, DamageInfo damageInfo)
        {
            ShipDestroyed?.Invoke(null, new ShipDestroyedEventArgs(ship, damageInfo));
        }

        
        /// <summary>
        /// Raises the BattleStarted event.
        /// </summary>
        /// <param name="config">The battle configuration.</param>
        public static void RaiseBattleStarted(BattleConfig config)
        {
            Logger.Debug("Battle started");
            BattleStarted(config);
        }

        /// <summary>
        /// Raises the BattleCompleted event.
        /// </summary>
        /// <param name="playerVictory">Whether the player won the battle.</param>
        public static void RaiseBattleCompleted(bool playerVictory)
        {
            Logger.Debug($"Battle completed. Player victory: {playerVictory}");
            BattleCompleted(playerVictory);
        }

        /// <summary>
        /// Raises the PlayerShipDestroyed event.
        /// </summary>
        public static void RaisePlayerShipDestroyed()
        {
            Logger.Debug("Player ship destroyed");
            PlayerShipDestroyed(null, EventArgs.Empty);;
        }

        /// <summary>
        /// Raises the EnemyShipDestroyed event.
        /// </summary>
        /// <param name="entity">The destroyed enemy entity.</param>
        public static void RaiseEnemyShipDestroyed(ShipEntity entity)
        {
            Logger.Debug($"Enemy ship destroyed: {entity.Name}");
            EnemyShipDestroyed(entity);
        }
        
        /// <summary>
        /// Raises the PlayerControlGiven event.
        /// </summary>
        /// <param name="entity">The entity that received player control.</param>
        public static void RaisePlayerControlGiven(ShipEntity entity)
        {
            Logger.Debug($"Player control given to: {entity.Name}");
            PlayerControlGiven(entity);
        }
        
        /// <summary>
        /// Raises the PlayerControlRemoved event.
        /// </summary>
        /// <param name="entity">The entity that lost player control.</param>
        public static void RaisePlayerControlRemoved(ShipEntity entity)
        {
            Logger.Debug($"Player control removed from: {entity.Name}");
            PlayerControlRemoved(entity);
        }
        
        /// <summary>
        /// Raises the PlayerControlSwitched event.
        /// </summary>
        /// <param name="previousEntity">The entity that previously had player control.</param>
        /// <param name="currentEntity">The entity that now has player control.</param>
        public static void RaisePlayerControlSwitched(ShipEntity previousEntity, ShipEntity currentEntity)
        {
            Logger.Debug($"Player control switched from {previousEntity?.Name ?? "none"} to {currentEntity.Name}");
            PlayerControlSwitched(previousEntity, currentEntity);
        }
        
        /// <summary>
        /// Event triggered when a battle should be started with a specific configuration.
        /// </summary>
        public delegate void StartBattleEventHandler(BattleConfig config);
        public static event StartBattleEventHandler StartBattle = delegate { };
        
        /// <summary>
        /// Raises the StartBattle event to request starting a battle with the specified configuration.
        /// </summary>
        /// <param name="config">The battle configuration to use.</param>
        /// <returns>True if the battle was successfully started, false otherwise.</returns>
        public static bool RaiseStartBattle(BattleConfig config)
        {
            if (config == null)
            {
                Logger.Error("Cannot start battle with null configuration");
                return false;
            }
            
            Logger.Info($"Requesting to start battle with config: {config.BattleId} (Type: {config.BattleType}, Difficulty: {config.DifficultyLevel})");
            StartBattle(config);
            return true;
        }
    }
    
    /// <summary>
    /// Event arguments for when a ship takes damage.
    /// </summary>
    public class ShipDamagedEventArgs : EventArgs
    {
        /// <summary>
        /// The ship that was damaged.
        /// </summary>
        public ShipEntity Ship { get; }
        
        /// <summary>
        /// Information about the damage.
        /// </summary>
        public DamageInfo DamageInfo { get; }
        
        /// <summary>
        /// Creates a new instance of the event arguments.
        /// </summary>
        /// <param name="ship">The ship that was damaged.</param>
        /// <param name="damageInfo">Information about the damage.</param>
        public ShipDamagedEventArgs(ShipEntity ship, DamageInfo damageInfo)
        {
            Ship = ship;
            DamageInfo = damageInfo;
        }
    }
    
    /// <summary>
    /// Event arguments for when a ship is destroyed.
    /// </summary>
    public class ShipDestroyedEventArgs : EventArgs
    {
        /// <summary>
        /// The ship that was destroyed.
        /// </summary>
        public ShipEntity Ship { get; }
        
        /// <summary>
        /// Information about the final damage that destroyed the ship.
        /// </summary>
        public DamageInfo DamageInfo { get; }
        
        /// <summary>
        /// Creates a new instance of the event arguments.
        /// </summary>
        /// <param name="ship">The ship that was destroyed.</param>
        /// <param name="damageInfo">Information about the final damage that destroyed the ship.</param>
        public ShipDestroyedEventArgs(ShipEntity ship, DamageInfo damageInfo)
        {
            Ship = ship;
            DamageInfo = damageInfo;
        }
    }
}
