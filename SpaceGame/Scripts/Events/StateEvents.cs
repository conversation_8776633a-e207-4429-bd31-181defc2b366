using System;
using SpaceGame.Helpers;
using SpaceGame.State;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Static class containing game state-related events.
    /// </summary>
    public static class StateEvents
    {
        /// <summary>
        /// Event triggered when the game state changes.
        /// </summary>
        public delegate void GameStateChangedEventHandler(GameStateChangeType stateChangeType);
        public static event GameStateChangedEventHandler GameStateChanged = delegate { };

        /// <summary>
        /// Event triggered when a save operation is completed.
        /// </summary>
        public delegate void SaveCompletedEventHandler(string saveName, bool success);
        public static event SaveCompletedEventHandler SaveCompleted = delegate { };

        /// <summary>
        /// Event triggered when a load operation is completed.
        /// </summary>
        public delegate void LoadCompletedEventHandler(GameState? loadedState, bool success);
        public static event LoadCompletedEventHandler LoadCompleted = delegate { };

        /// <summary>
        /// Raises the GameStateChanged event.
        /// </summary>
        /// <param name="stateChangeType">The type of state change.</param>
        public static void RaiseGameStateChanged(GameStateChangeType stateChangeType)
        {
            Logger.Debug($"Game state changed: {stateChangeType}");
            GameStateChanged(stateChangeType);
        }

        /// <summary>
        /// Raises the SaveCompleted event.
        /// </summary>
        /// <param name="saveName">The name of the save file.</param>
        /// <param name="success">Whether the save operation was successful.</param>
        public static void RaiseSaveCompleted(string saveName, bool success)
        {
            Logger.Debug($"Save completed: {saveName}, Success: {success}");
            SaveCompleted(saveName, success);
        }

        /// <summary>
        /// Raises the LoadCompleted event.
        /// </summary>
        /// <param name="loadedState">The loaded game state, or null if loading failed.</param>
        /// <param name="success">Whether the load operation was successful.</param>
        public static void RaiseLoadCompleted(GameState? loadedState, bool success)
        {
            Logger.Debug($"Load completed: Success: {success}");
            LoadCompleted(loadedState, success);
        }
    }
}
