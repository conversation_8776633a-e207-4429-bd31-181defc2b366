using System.Collections.Generic;
using SpaceGame.State;
using SpaceGame.Universe.Model;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Event raised when a new faction is created.
    /// </summary>
    public class FactionCreatedEvent : GameEvent
    {
        /// <summary>
        /// The ID of the created faction.
        /// </summary>
        public string FactionId { get; }
        
        /// <summary>
        /// The type of the faction.
        /// </summary>
        public FactionType Type { get; }
        
        /// <summary>
        /// Whether this faction is the player faction.
        /// </summary>
        public bool IsPlayerFaction { get; }
        
        /// <summary>
        /// The faction object that was created.
        /// </summary>
        public Faction? Faction { get; }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="FactionCreatedEvent"/> class.
        /// </summary>
        /// <param name="factionId">The ID of the faction that was created.</param>
        /// <param name="type">The type of the faction.</param>
        /// <param name="isPlayerFaction">Whether this faction is the player faction.</param>
        /// <param name="faction">The faction that was created (optional).</param>
        public FactionCreatedEvent(string factionId, FactionType type, bool isPlayerFaction, Faction? faction = null)
        {
            FactionId = factionId;
            Type = type;
            IsPlayerFaction = isPlayerFaction;
            Faction = faction;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FactionCreatedEvent"/> class.
        /// </summary>
        /// <param name="faction">The faction that was created.</param>
        public FactionCreatedEvent(Faction faction)
        {
            FactionId = faction.Id;
            Type = faction.Type;
            IsPlayerFaction = faction.IsPlayerFaction;
            Faction = faction;
        }
    }
    
    /// <summary>
    /// Event raised when a faction is deleted.
    /// </summary>
    public class FactionDeletedEvent : GameEvent
    {
        public FactionState Faction { get; }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="FactionDeletedEvent"/> class.
        /// </summary>
        /// <param name="factionId">The ID of the faction that was deleted.</param>
        public FactionDeletedEvent(FactionState faction)
        {
            Faction = faction;
        }
    }
    
    /// <summary>
    /// Event raised when a faction is updated.
    /// </summary>
    public class FactionUpdatedEvent : GameEvent
    {
        /// <summary>
        /// The ID of the updated faction.
        /// </summary>
        public string FactionId { get; }
        
        /// <summary>
        /// The type of the faction.
        /// </summary>
        public FactionType Type { get; }
        
        /// <summary>
        /// Whether this faction is the player faction.
        /// </summary>
        public bool IsPlayerFaction { get; }
        
        /// <summary>
        /// The updated faction object.
        /// </summary>
        public Faction? Faction { get; }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="FactionUpdatedEvent"/> class.
        /// </summary>
        /// <param name="factionId">The ID of the faction that was updated.</param>
        /// <param name="type">The type of the faction.</param>
        /// <param name="isPlayerFaction">Whether this faction is the player faction.</param>
        /// <param name="faction">The faction that was updated (optional).</param>
        public FactionUpdatedEvent(string factionId, FactionType type, bool isPlayerFaction, Faction? faction = null)
        {
            FactionId = factionId;
            Type = type;
            IsPlayerFaction = isPlayerFaction;
            Faction = faction;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FactionUpdatedEvent"/> class.
        /// </summary>
        /// <param name="faction">The faction that was updated.</param>
        public FactionUpdatedEvent(Faction faction)
        {
            FactionId = faction.Id;
            Type = faction.Type;
            IsPlayerFaction = faction.IsPlayerFaction;
            Faction = faction;
        }
    }
}