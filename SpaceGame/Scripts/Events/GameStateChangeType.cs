using System;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Represents different types of game state changes.
    /// </summary>
    public enum GameStateChangeType
    {
        /// <summary>
        /// A new game has been started.
        /// </summary>
        NewGame,
        
        /// <summary>
        /// A game has been saved.
        /// </summary>
        GameSaved,
        
        /// <summary>
        /// A game is about to be loaded (pre-load).
        /// </summary>
        PreLoad,
        
        /// <summary>
        /// A game has been loaded after processing (post-load).
        /// </summary>
        PostLoad,
        
        /// <summary>
        /// A game has been loaded (game-loaded).
        /// </summary>
        GameLoaded,
        
        /// <summary>
        /// Game state has been applied.
        /// </summary>
        GameStateApplied,
        
        /// <summary>
        /// A new game state has been created.
        /// </summary>
        NewGameStateCreated,
        
        /// <summary>
        /// Save operation has completed.
        /// </summary>
        SaveCompleted,
        
        /// <summary>
        /// A fleet has been created.
        /// </summary>
        FleetCreated,
        
        /// <summary>
        /// A fleet has been deleted.
        /// </summary>
        FleetDeleted,
        
        /// <summary>
        /// A faction has been created.
        /// </summary>
        FactionCreated,
        
        /// <summary>
        /// A faction has been deleted.
        /// </summary>
        FactionDeleted
    }
}
