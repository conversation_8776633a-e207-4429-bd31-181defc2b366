using System;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Base class for all game events.
    /// </summary>
    public abstract class GameEvent
    {
        /// <summary>
        /// Gets the timestamp when the event was created.
        /// </summary>
        public DateTime Timestamp { get; private set; }
        
        /// <summary>
        /// Creates a new game event with the current timestamp.
        /// </summary>
        public GameEvent()
        {
            Timestamp = DateTime.Now;
        }
    }
} 