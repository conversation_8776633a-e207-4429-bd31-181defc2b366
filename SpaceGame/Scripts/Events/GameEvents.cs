namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Event raised when the game state changes (e.g., new game, game loaded, game over).
    /// </summary>
    public class GameStateChangedEvent : GameEvent
    {
        /// <summary>
        /// The type of state change that occurred.
        /// </summary>
        public string StateChangeType { get; }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="GameStateChangedEvent"/> class.
        /// </summary>
        /// <param name="stateChangeType">The type of state change that occurred (e.g., "new_game", "game_loaded", "game_over").</param>
        public GameStateChangedEvent(string stateChangeType)
        {
            StateChangeType = stateChangeType;
        }
    }
} 