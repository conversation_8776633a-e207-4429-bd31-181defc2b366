using System;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Events
{
    /// <summary>
    /// Static class containing core game-related events.
    /// </summary>
    public static class CoreGameEvents
    {
        /// <summary>
        /// Event triggered when the game is started.
        /// </summary>
        public static event EventHandler GameStarted = delegate { };

        /// <summary>
        /// Event triggered when the game is paused.
        /// </summary>
        public static event EventHandler GamePaused = delegate { };

        /// <summary>
        /// Event triggered when the game is resumed.
        /// </summary>
        public static event EventHandler GameResumed = delegate { };

        /// <summary>
        /// Event triggered when the game is exited.
        /// </summary>
        public static event EventHandler GameExited = delegate { };

        /// <summary>
        /// Raises the GameStarted event.
        /// </summary>
        public static void RaiseGameStarted()
        {
            Logger.Debug("Game started");
            GameStarted(null, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the GamePaused event.
        /// </summary>
        public static void RaiseGamePaused()
        {
            Logger.Debug("Game paused");
            GamePaused(null, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the GameResumed event.
        /// </summary>
        public static void RaiseGameResumed()
        {
            Logger.Debug("Game resumed");
            GameResumed(null, EventArgs.Empty);
        }

        /// <summary>
        /// Raises the GameExited event.
        /// </summary>
        public static void RaiseGameExited()
        {
            Logger.Debug("Game exited");
            GameExited(null, EventArgs.Empty);
        }
    }
}
