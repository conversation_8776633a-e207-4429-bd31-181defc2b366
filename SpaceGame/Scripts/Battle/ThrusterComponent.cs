using Godot;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Component for handling thruster visuals and behavior.
/// Supports both sprite-based and particle-based thruster effects.
/// </summary>
public partial class ThrusterComponent : Node2D
{
    /// <summary>
    /// Enum defining the available thruster modes.
    /// </summary>
    public enum ThrusterMode
    {
        /// <summary>
        /// Uses sprite-based thruster effects.
        /// </summary>
        Sprite,
        
        /// <summary>
        /// Uses particle-based thruster effects.
        /// </summary>
        Particle
    }
    
    /// <summary>
    /// The current mode of the thruster (sprite or particle).
    /// </summary>
    [Export]
    public ThrusterMode Mode { get; set; } = ThrusterMode.Sprite;
    
    /// <summary>
    /// The sprite to use for sprite-based thruster effects.
    /// </summary>
    [Export]
    public AnimatedSprite2D ThrusterSprite { get; set; }
    
    /// <summary>
    /// The particle system to use for particle-based thruster effects.
    /// </summary>
    [Export]
    public GpuParticles2D ThrusterParticles { get; set; }
    
    /// <summary>
    /// The maximum intensity of the thruster effect.
    /// </summary>
    [Export]
    public float MaxIntensity { get; set; } = 1.0f;
    
    /// <summary>
    /// The current intensity of the thruster effect (0.0 to 1.0).
    /// </summary>
    private float _currentIntensity = 0.0f;
    
    /// <summary>
    /// Whether the thruster is currently active.
    /// </summary>
    private bool _isActive = false;
    
    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// </summary>
    public override void _Ready()
    {
        base._Ready();
        
        // Initialize thruster components
        if (ThrusterSprite == null)
        {
            ThrusterSprite = GetNodeOrNull<AnimatedSprite2D>("ThrusterSprite");
        }
        
        if (ThrusterParticles == null)
        {
            ThrusterParticles = GetNodeOrNull<GpuParticles2D>("ThrusterParticles");
        }
        
        // Set initial visibility based on mode
        UpdateVisibility();
        
        // Set initial intensity
        SetIntensity(0.0f);
    }
    
    /// <summary>
    /// Called every frame.
    /// </summary>
    /// <param name="delta">The elapsed time since the previous frame.</param>
    public override void _Process(double delta)
    {
        base._Process(delta);
        
        // Update thruster effects based on current intensity
        UpdateThrusterEffects();
    }
    
    /// <summary>
    /// Sets the current thruster mode.
    /// </summary>
    /// <param name="mode">The thruster mode to set.</param>
    public void SetMode(ThrusterMode mode)
    {
        if (Mode == mode)
            return;
            
        Mode = mode;
        UpdateVisibility();
    }
    
    /// <summary>
    /// Sets the current thruster intensity.
    /// </summary>
    /// <param name="intensity">The intensity value (0.0 to 1.0).</param>
    public void SetIntensity(float intensity)
    {
        _currentIntensity = Mathf.Clamp(intensity, 0.0f, 1.0f);
        _isActive = _currentIntensity > 0.0f;
        
        // Update visibility based on active state
        if (ThrusterSprite != null)
        {
            ThrusterSprite.Visible = Mode == ThrusterMode.Sprite && _isActive;
        }
        
        if (ThrusterParticles != null)
        {
            bool shouldEmit = Mode == ThrusterMode.Particle && _isActive;
            ThrusterParticles.Emitting = shouldEmit;
        }
    }
    
    /// <summary>
    /// Updates the visibility of thruster components based on the current mode.
    /// </summary>
    private void UpdateVisibility()
    {
        if (ThrusterSprite != null)
        {
            ThrusterSprite.Visible = Mode == ThrusterMode.Sprite && _isActive;
        }
        
        if (ThrusterParticles != null)
        {
            ThrusterParticles.Visible = Mode == ThrusterMode.Particle;
            ThrusterParticles.Emitting = Mode == ThrusterMode.Particle && _isActive;
        }
    }
    
    /// <summary>
    /// Updates the thruster effects based on the current intensity.
    /// </summary>
    private void UpdateThrusterEffects()
    {
        if (!_isActive)
            return;
            
        // Update sprite-based effects
        if (Mode == ThrusterMode.Sprite && ThrusterSprite != null)
        {
            // Scale the sprite based on intensity
            float scale = 0.5f + (_currentIntensity * 0.5f);
            ThrusterSprite.Scale = new Vector2(scale, scale);
            
            // Adjust opacity based on intensity
            Color modulate = ThrusterSprite.Modulate;
            modulate.A = 0.5f + (_currentIntensity * 0.5f);
            ThrusterSprite.Modulate = modulate;
        }
        
        // Update particle-based effects
        if (Mode == ThrusterMode.Particle && ThrusterParticles != null)
        {
            // Adjust particle emission rate based on intensity
            float baseAmount = ThrusterParticles.Amount;
            ThrusterParticles.AmountRatio = _currentIntensity;
            
            // Adjust particle speed based on intensity
            if (ThrusterParticles.ProcessMaterial is ParticleProcessMaterial particleMaterial)
            {
                float baseSpeed = particleMaterial.InitialVelocityMax;
                particleMaterial.InitialVelocityMax = baseSpeed * _currentIntensity;
            }
        }
    }
}
