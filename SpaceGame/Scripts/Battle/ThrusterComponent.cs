using Godot;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Component for handling thruster visuals and behavior.
/// Supports both sprite-based and particle-based thruster effects.
/// </summary>
public partial class ThrusterComponent : Node2D
{
    /// <summary>
    /// Enum defining the available thruster modes.
    /// </summary>
    public enum ThrusterMode
    {
        /// <summary>
        /// Uses sprite-based thruster effects.
        /// </summary>
        Sprite,
        
        /// <summary>
        /// Uses particle-based thruster effects.
        /// </summary>
        Particle
    }
    
    /// <summary>
    /// The current mode of the thruster (sprite or particle).
    /// </summary>
    [Export]
    public ThrusterMode Mode { get; set; } = ThrusterMode.Sprite;
    
    /// <summary>
    /// The sprite to use for sprite-based thruster effects.
    /// </summary>
    [Export]
    public AnimatedSprite2D ThrusterSprite { get; set; }
    
    /// <summary>
    /// The particle system to use for particle-based thruster effects.
    /// </summary>
    [Export]
    public GpuParticles2D ThrusterParticles { get; set; }

    /// <summary>
    /// Optional secondary particle system for additional thruster effects (like afterburner).
    /// </summary>
    [Export]
    public GpuParticles2D SecondaryThrusterParticles { get; set; }

    /// <summary>
    /// The maximum intensity of the thruster effect.
    /// </summary>
    [Export]
    public float MaxIntensity { get; set; } = 1.0f;

    /// <summary>
    /// Intensity threshold for activating secondary thruster effects.
    /// </summary>
    [Export]
    public float SecondaryThrusterThreshold { get; set; } = 0.7f;
    
    /// <summary>
    /// The current intensity of the thruster effect (0.0 to 1.0).
    /// </summary>
    private float _currentIntensity = 0.0f;
    
    /// <summary>
    /// Whether the thruster is currently active.
    /// </summary>
    private bool _isActive = false;
    
    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// </summary>
    public override void _Ready()
    {
        base._Ready();
        
        // Initialize thruster components
        if (ThrusterSprite == null)
        {
            ThrusterSprite = GetNodeOrNull<AnimatedSprite2D>("ThrusterSprite");
        }
        
        if (ThrusterParticles == null)
        {
            ThrusterParticles = GetNodeOrNull<GpuParticles2D>("ThrusterParticles");
        }

        if (SecondaryThrusterParticles == null)
        {
            SecondaryThrusterParticles = GetNodeOrNull<GpuParticles2D>("SecondaryThrusterParticles");
        }
        
        // Set initial visibility based on mode
        UpdateVisibility();
        
        // Set initial intensity
        SetIntensity(0.0f);
    }
    
    /// <summary>
    /// Called every frame.
    /// </summary>
    /// <param name="delta">The elapsed time since the previous frame.</param>
    public override void _Process(double delta)
    {
        base._Process(delta);
        
        // Update thruster effects based on current intensity
        UpdateThrusterEffects();
    }
    
    /// <summary>
    /// Sets the current thruster mode.
    /// </summary>
    /// <param name="mode">The thruster mode to set.</param>
    public void SetMode(ThrusterMode mode)
    {
        if (Mode == mode)
            return;
            
        Mode = mode;
        UpdateVisibility();
    }
    
    /// <summary>
    /// Sets the current thruster intensity.
    /// </summary>
    /// <param name="intensity">The intensity value (0.0 to 1.0).</param>
    public void SetIntensity(float intensity)
    {
        _currentIntensity = Mathf.Clamp(intensity, 0.0f, 1.0f);
        _isActive = _currentIntensity > 0.0f;
        
        // Update visibility based on active state
        if (ThrusterSprite != null)
        {
            ThrusterSprite.Visible = Mode == ThrusterMode.Sprite && _isActive;
        }
        
        if (ThrusterParticles != null)
        {
            bool shouldEmit = Mode == ThrusterMode.Particle && _isActive;
            ThrusterParticles.Emitting = shouldEmit;
        }

        if (SecondaryThrusterParticles != null)
        {
            bool shouldEmitSecondary = Mode == ThrusterMode.Particle && _isActive && _currentIntensity >= SecondaryThrusterThreshold;
            SecondaryThrusterParticles.Emitting = shouldEmitSecondary;
        }
    }
    
    /// <summary>
    /// Updates the visibility of thruster components based on the current mode.
    /// </summary>
    private void UpdateVisibility()
    {
        if (ThrusterSprite != null)
        {
            ThrusterSprite.Visible = Mode == ThrusterMode.Sprite && _isActive;
        }
        
        if (ThrusterParticles != null)
        {
            ThrusterParticles.Visible = Mode == ThrusterMode.Particle;
            ThrusterParticles.Emitting = Mode == ThrusterMode.Particle && _isActive;
        }

        if (SecondaryThrusterParticles != null)
        {
            SecondaryThrusterParticles.Visible = Mode == ThrusterMode.Particle;
            bool shouldEmitSecondary = Mode == ThrusterMode.Particle && _isActive && _currentIntensity >= SecondaryThrusterThreshold;
            SecondaryThrusterParticles.Emitting = shouldEmitSecondary;
        }
    }
    
    /// <summary>
    /// Updates the thruster effects based on the current intensity.
    /// </summary>
    private void UpdateThrusterEffects()
    {
        if (!_isActive)
            return;
            
        // Update sprite-based effects
        if (Mode == ThrusterMode.Sprite && ThrusterSprite != null)
        {
            // Scale the sprite based on intensity
            float scale = 0.5f + (_currentIntensity * 0.5f);
            ThrusterSprite.Scale = new Vector2(scale, scale);
            
            // Adjust opacity based on intensity
            Color modulate = ThrusterSprite.Modulate;
            modulate.A = 0.5f + (_currentIntensity * 0.5f);
            ThrusterSprite.Modulate = modulate;
        }
        
        // Update particle-based effects
        if (Mode == ThrusterMode.Particle && ThrusterParticles != null)
        {
            // Adjust particle emission rate based on intensity
            ThrusterParticles.AmountRatio = _currentIntensity;

            // Adjust particle speed based on intensity
            if (ThrusterParticles.ProcessMaterial is ParticleProcessMaterial particleMaterial)
            {
                // Store original values if not already stored
                if (!particleMaterial.HasMeta("original_velocity_max"))
                {
                    particleMaterial.SetMeta("original_velocity_max", particleMaterial.InitialVelocityMax);
                    particleMaterial.SetMeta("original_velocity_min", particleMaterial.InitialVelocityMin);
                }

                float originalMaxSpeed = particleMaterial.GetMeta("original_velocity_max").AsSingle();
                float originalMinSpeed = particleMaterial.GetMeta("original_velocity_min").AsSingle();

                particleMaterial.InitialVelocityMax = originalMaxSpeed * _currentIntensity;
                particleMaterial.InitialVelocityMin = originalMinSpeed * _currentIntensity;
            }
        }

        // Update secondary particle effects
        if (Mode == ThrusterMode.Particle && SecondaryThrusterParticles != null && _currentIntensity >= SecondaryThrusterThreshold)
        {
            // Calculate secondary intensity (0.0 to 1.0 based on how much above threshold we are)
            float secondaryIntensity = (_currentIntensity - SecondaryThrusterThreshold) / (1.0f - SecondaryThrusterThreshold);
            SecondaryThrusterParticles.AmountRatio = secondaryIntensity;

            if (SecondaryThrusterParticles.ProcessMaterial is ParticleProcessMaterial secondaryMaterial)
            {
                // Store original values if not already stored
                if (!secondaryMaterial.HasMeta("original_velocity_max"))
                {
                    secondaryMaterial.SetMeta("original_velocity_max", secondaryMaterial.InitialVelocityMax);
                    secondaryMaterial.SetMeta("original_velocity_min", secondaryMaterial.InitialVelocityMin);
                }

                float originalMaxSpeed = secondaryMaterial.GetMeta("original_velocity_max").AsSingle();
                float originalMinSpeed = secondaryMaterial.GetMeta("original_velocity_min").AsSingle();

                secondaryMaterial.InitialVelocityMax = originalMaxSpeed * secondaryIntensity;
                secondaryMaterial.InitialVelocityMin = originalMinSpeed * secondaryIntensity;
            }
        }
    }
}
