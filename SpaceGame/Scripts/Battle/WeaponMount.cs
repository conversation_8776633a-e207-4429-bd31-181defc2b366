using Godot;
using System;
using System.Collections.Generic;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Defines a weapon mounting point on a battle entity.
/// Handles position, rotation, and weapon type constraints.
/// </summary>
[GlobalClass]
public partial class WeaponMount : Node2D
{
    /// <summary>
    /// The unique identifier for this weapon mount.
    /// </summary>
    [Export]
    public string MountId { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// The display name for this weapon mount.
    /// </summary>
    [Export]
    public string MountName { get; set; } = "Weapon Mount";
    
    /// <summary>
    /// Optional Node2D representing the muzzle's position and orientation relative to this mount.
    /// If null, the mount's own transform is used as the muzzle point.
    /// </summary>
    [Export]
    public Node2D? MuzzlePoint { get; set; }
    
    /// <summary>
    /// The allowed weapon types for this mount.
    /// Empty means all types are allowed.
    /// </summary>
    [Export(PropertyHint.Flags)]
    public WeaponType AllowedWeaponTypeFlags { get; set; } = 0; // 0 means all allowed
    
    /// <summary>
    /// The sprite used to visualize this mount point.
    /// </summary>
    [Export]
    public Sprite2D MountIndicator { get; set; }
    
    /// <summary>
    /// The currently attached weapon, if any.
    /// </summary>
    private Node? _attachedWeapon;
    
    /// <summary>
    /// The data for the currently attached weapon, if any.
    /// </summary>
    private WeaponData? _weaponData;
    
    /// <summary>
    /// Event args for weapon attachment events.
    /// </summary>
    public class WeaponMountEventArgs : EventArgs
    {
        /// <summary>
        /// The ID of the mount.
        /// </summary>
        public string MountId { get; }
        
        /// <summary>
        /// The weapon data.
        /// </summary>
        public WeaponData WeaponData { get; }
        
        /// <summary>
        /// Constructor.
        /// </summary>
        /// <param name="mountId">The ID of the mount.</param>
        /// <param name="weaponData">The weapon data.</param>
        public WeaponMountEventArgs(string mountId, WeaponData weaponData)
        {
            MountId = mountId;
            WeaponData = weaponData;
        }
    }
    
    /// <summary>
    /// Event triggered when a weapon is attached to this mount.
    /// </summary>
    public event EventHandler<WeaponMountEventArgs> WeaponAttached;
    
    /// <summary>
    /// Event triggered when a weapon is detached from this mount.
    /// </summary>
    public event EventHandler<WeaponMountEventArgs> WeaponDetached;
    
    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// </summary>
    public override void _Ready()
    {
        base._Ready();
        
        // Find mount indicator if not set
        if (MountIndicator == null)
        {
            MountIndicator = GetNodeOrNull<Sprite2D>("MountIndicator");
        }
        
        // Set initial visibility of mount indicator
        if (MountIndicator != null)
        {
            // Only show mount indicator in editor
            MountIndicator.Visible = Engine.IsEditorHint();
        }
        
        // No need to convert flags, we'll check them directly in IsWeaponTypeAllowed
    }
    
    /// <summary>
    /// Checks if a weapon type is allowed for this mount.
    /// </summary>
    /// <param name="weaponType">The weapon type to check.</param>
    /// <returns>True if the weapon type is allowed, false otherwise.</returns>
    public bool IsWeaponTypeAllowed(WeaponType weaponType)
    {
        // If no flags are set (0), all types are allowed
        if (AllowedWeaponTypeFlags == 0)
        {
            return true;
        }
        
        return AllowedWeaponTypeFlags.HasFlag(weaponType);
    }
    
    /// <summary>
    /// Gets the global transform of the muzzle point.
    /// </summary>
    /// <returns>The global Transform2D of the muzzle.</returns>
    public Transform2D GetMuzzleGlobalTransform()
    {
        return MuzzlePoint != null ? MuzzlePoint.GlobalTransform : GlobalTransform;
    }
    
    /// <summary>
    /// Attaches a weapon to this mount.
    /// </summary>
    /// <param name="weaponData">The data for the weapon to attach.</param>
    /// <param name="weaponScene">The scene for the weapon to attach.</param>
    /// <param name="displayVisuals">Whether to display the visual elements of the weapon.</param>
    /// <returns>True if the weapon was attached successfully, false otherwise.</returns>
    public bool AttachWeapon(WeaponData weaponData, Node weaponScene, bool displayVisuals = true)
    {
        // Check if a weapon is already attached
        if (_attachedWeapon != null)
        {
            Logger.Warning($"Cannot attach weapon to mount {MountName}: Mount already has a weapon attached.");
            return false;
        }
        
        // Check if the weapon type is allowed
        if (!IsWeaponTypeAllowed(weaponData.Type))
        {
            Logger.Warning($"Cannot attach weapon to mount {MountName}: Weapon type {weaponData.Type} is not allowed.");
            return false;
        }
        
        // Attach the weapon
        _attachedWeapon = weaponScene;
        _weaponData = weaponData;
        
        // Add the weapon scene as a child
        AddChild(weaponScene);
        
        // Position the weapon at the mount point (if it's a Node2D)
        if (weaponScene is Node2D weaponNode2D)
        {
            weaponNode2D.Position = Vector2.Zero;
        }
        
        // Set visibility based on displayVisuals parameter
        if (weaponScene is Node2D node2D)
        {
            node2D.Visible = displayVisuals;
        }
        else if (weaponScene is Control control)
        {
            control.Visible = displayVisuals;
        }
        else
        {
            Logger.Warning($"WeaponMount: Cannot set visibility on weapon node of type {weaponScene.GetType().Name}. Not a Node2D or Control.");
        }
        
        // Raise the weapon attached event
        WeaponAttached?.Invoke(this, new WeaponMountEventArgs(MountId, weaponData));
        
        return true;
    }
    
    /// <summary>
    /// Detaches the currently attached weapon, if any.
    /// </summary>
    /// <returns>The detached weapon scene, or null if no weapon was attached.</returns>
    public Node? DetachWeapon()
    {
        // Check if a weapon is attached
        if (_attachedWeapon == null)
        {
            return null;
        }
        
        // Remove the weapon from the mount
        Node weapon = _attachedWeapon;
        WeaponData weaponData = _weaponData;
        
        RemoveChild(weapon);
        
        _attachedWeapon = null;
        _weaponData = null;
        
        // Raise the weapon detached event
        WeaponDetached?.Invoke(this, new WeaponMountEventArgs(MountId, weaponData));
        
        return weapon;
    }
    
    /// <summary>
    /// Gets the currently attached weapon data, if any.
    /// </summary>
    /// <returns>The attached weapon data, or null if no weapon is attached.</returns>
    public WeaponData GetAttachedWeaponData()
    {
        return _weaponData;
    }
    
    /// <summary>
    /// Gets the currently attached weapon scene, if any.
    /// </summary>
    /// <returns>The attached weapon scene, or null if no weapon is attached.</returns>
    public Node GetAttachedWeapon()
    {
        return _attachedWeapon;
    }
    
    /// <summary>
    /// Checks if this mount has a weapon attached.
    /// </summary>
    /// <returns>True if a weapon is attached, false otherwise.</returns>
    public bool HasWeaponAttached()
    {
        return _attachedWeapon != null;
    }
}
