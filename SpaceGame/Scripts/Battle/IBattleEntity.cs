using System;
using Godot;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Interface defining common functionality for all battle entities.
/// Provides methods for damage handling, allegiance assignment, and lifecycle management.
/// </summary>
public interface IBattleEntity
{
    /// <summary>
    /// Gets the entity's unique identifier.
    /// </summary>
    string Id { get; }
    
    /// <summary>
    /// Gets or sets the entity's current health.
    /// </summary>
    float CurrentHealth { get; set; }
    
    /// <summary>
    /// Gets the entity's maximum health.
    /// </summary>
    float MaxHealth { get; }
    
    /// <summary>
    /// Gets or sets the entity's allegiance.
    /// </summary>
    Allegiance CurrentAllegiance { get; set; }
    
    /// <summary>
    /// Gets the entity's position in the battle space.
    /// </summary>
    Vector2 Position { get; }
    
    /// <summary>
    /// Gets the entity's rotation in the battle space.
    /// </summary>
    float Rotation { get; }
    
    /// <summary>
    /// Event triggered when the entity takes damage.
    /// </summary>
    delegate void DamagedEventHandler(IBattleEntity entity, float amount, IBattleEntity source);
    static event DamagedEventHandler Damaged;
    
    /// <summary>
    /// Event triggered when the entity is destroyed.
    /// </summary>
    delegate void DestroyedEventHandler(IBattleEntity entity);
    static event DestroyedEventHandler Destroyed;
    
    /// <summary>
    /// Event triggered when the entity's allegiance changes.
    /// </summary>
    delegate void AllegianceChangedEventHandler(IBattleEntity entity, Allegiance oldAllegiance, Allegiance newAllegiance);
    static event AllegianceChangedEventHandler AllegianceChanged;
    
    /// <summary>
    /// Applies damage to the entity.
    /// </summary>
    /// <param name="amount">The amount of damage to apply.</param>
    /// <param name="source">The source of the damage, if any.</param>
    /// <returns>The actual amount of damage applied.</returns>
    float ApplyDamage(float amount, IBattleEntity source = null);
    
    /// <summary>
    /// Applies healing to the entity.
    /// </summary>
    /// <param name="amount">The amount of healing to apply.</param>
    /// <returns>The actual amount of healing applied.</returns>
    float ApplyHealing(float amount);
    
    /// <summary>
    /// Changes the entity's allegiance.
    /// </summary>
    /// <param name="newAllegiance">The new allegiance to assign.</param>
    void ChangeAllegiance(Allegiance newAllegiance);
    
    /// <summary>
    /// Checks if this entity is hostile to another entity.
    /// </summary>
    /// <param name="other">The other entity to check against.</param>
    /// <returns>True if the entities are hostile to each other, false otherwise.</returns>
    bool IsHostileTo(IBattleEntity other);
    
    /// <summary>
    /// Destroys the entity, removing it from the battle.
    /// </summary>
    void Destroy();
}
