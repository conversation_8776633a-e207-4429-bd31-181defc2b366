using System.Collections.Generic;

namespace SpaceGame.Scripts.Battle
{
    /// <summary>
    /// Configuration for spawning enemy waves in a battle.
    /// </summary>
    public class EnemySpawnConfig
    {
        /// <summary>
        /// Gets or sets the list of enemy waves to spawn.
        /// </summary>
        public List<EnemyWave> Waves { get; set; } = new List<EnemyWave>();
    }

    /// <summary>
    /// Configuration for a single wave of enemy ships.
    /// </summary>
    public class EnemyWave
    {
        /// <summary>
        /// Gets or sets the number of enemies to spawn in this wave.
        /// </summary>
        public int NumberToSpawn { get; set; } = 3;
        
        /// <summary>
        /// Gets or sets the types of enemy ships to spawn in this wave.
        /// </summary>
        public List<string> EnemyShipTypes { get; set; } = new List<string>();
        
        /// <summary>
        /// Gets or sets the delay in seconds before spawning this wave.
        /// </summary>
        public float DelayBeforeSpawnSeconds { get; set; } = 0f;
        
        /// <summary>
        /// Gets or sets the interval in seconds between spawning each enemy in this wave.
        /// </summary>
        public float SpawnIntervalSeconds { get; set; } = 1f;
    }
}
