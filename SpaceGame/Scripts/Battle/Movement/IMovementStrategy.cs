using Godot;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.Battle.Movement;

/// <summary>
/// Defines the contract for a movement strategy used by the BattleEntityMovementController.
/// Each strategy is responsible for a specific mode of movement and rotation.
/// </summary>
public interface IMovementStrategy
{
    /// <summary>
    /// Initializes the strategy with necessary references.
    /// </summary>
    /// <param name="shipEntity">The parent ship entity.</param>
    /// <param name="shipData">The ship's data containing movement parameters.</param>
    void Initialize(ShipEntity shipEntity, ShipEntityData shipData);

    /// <summary>
    /// Updates the strategy with new ship data if it changes after initialization.
    /// </summary>
    /// <param name="shipData">The new ship data.</param>
    void UpdateShipData(ShipEntityData shipData);

    /// <summary>
    /// Called when this strategy becomes the active strategy.
    /// Should reset any internal state specific to the strategy.
    /// </summary>
    /// <param name="currentVelocity">The current velocity of the ship when activating.</param>
    /// <param name="currentRotationDegrees">The current rotation of the ship in degrees when activating.</param>
    void Activate(Vector2 currentVelocity, float currentRotationDegrees);

    /// <summary>
    /// Called when this strategy is no longer the active strategy.
    /// </summary>
    void Deactivate();

    /// <summary>
    /// Processes rotation logic for the current frame.
    /// The strategy should calculate and set the ship's new global rotation.
    /// </summary>
    /// <param name="delta">The time elapsed since the last physics frame.</param>
    /// <param name="shipGlobalRotationDegrees">Input: current ship rotation. Output: new ship rotation.</param>
    /// <param name="directRotationInput">Direct rotation input, typically from -1.0 to 1.0. Strategies not using direct input can ignore this.</param>
    void UpdateRotation(double delta, ref float shipGlobalRotationDegrees, float directRotationInput);

    /// <summary>
    /// Processes movement logic for the current frame.
    /// The strategy should calculate and set the ship's new velocity (before universal physics like drag/max speed).
    /// </summary>
    /// <param name="delta">The time elapsed since the last physics frame.</param>
    /// <param name="currentVelocity">Input: current ship velocity. Output: new ship velocity intent.</param>
    /// <param name="shipGlobalRotationDegrees">The current global rotation of the ship in degrees.</param>
    /// <param name="directThrustInput">Direct thrust input, typically from -1.0 to 1.0. Strategies not using direct input can ignore this.</param>
    void UpdateMovement(double delta, ref Vector2 currentVelocity, float shipGlobalRotationDegrees, float directThrustInput);

    // --- Command Methods --- 

    /// <summary>
    /// Stops the entity's current targeted movement (e.g., clears MoveTo target).
    /// Deceleration will be handled by the strategy's UpdateMovement or by the facade.
    /// </summary>
    void StopMovement();

    /// <summary>
    /// Stops the entity's current targeted rotation.
    /// </summary>
    void StopRotation();

    // --- Query Methods --- 

    /// <summary>
    /// Gets the current thruster intensity based on the strategy's state.
    /// </summary>
    /// <param name="currentVelocity">The current velocity of the ship.</param>
    /// <param name="shipGlobalRotationDegrees">The current global rotation of the ship in degrees.</param>
    /// <returns>A value typically between 0.0 and 1.0.</returns>
    float GetThrusterIntensity(Vector2 currentVelocity, float shipGlobalRotationDegrees);
}
