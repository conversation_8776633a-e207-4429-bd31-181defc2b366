using Godot;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;
using System;

namespace SpaceGame.Scripts.Battle.Movement;

/// <summary>
/// Implements IMovementStrategy for direct player-like control (thrust and rotation inputs).
/// </summary>
public partial class DirectControlMovementStrategy : Node, IMovementStrategy
{
    private ShipEntity _shipEntity;
    private ShipEntityData _shipData;

    private float _lastDirectThrustInput = 0.0f; // Cache for GetThrusterIntensity

    public DirectControlMovementStrategy()
    {
        // Required for Godot
    }

    public void Initialize(ShipEntity shipEntity, ShipEntityData shipData)
    {
        _shipEntity = shipEntity;
        _shipData = shipData;
    }

    public void UpdateShipData(ShipEntityData shipData)
    {
        _shipData = shipData;
    }

    public void Activate(Vector2 currentVelocity, float currentRotationDegrees)
    {
        // Reset inputs when activated to ensure fresh control state
    }

    public void Deactivate()
    {
        // Reset inputs when deactivated to prevent lingering control signals
        _lastDirectThrustInput = 0.0f; // Reset cached thrust
    }

    public void UpdateRotation(double delta, ref float shipGlobalRotationDegrees, float directRotationInput)
    {
        if (_shipData == null) return;

        if (Godot.Mathf.Abs(directRotationInput) > 0.01f) 
        {
            shipGlobalRotationDegrees += directRotationInput * _shipData.RotationSpeed * (float)delta;
            shipGlobalRotationDegrees = Godot.Mathf.Wrap(shipGlobalRotationDegrees, -180.0f, 180.0f);
        }
    }

    public void UpdateMovement(double delta, ref Vector2 currentVelocity, float shipGlobalRotationDegrees, float directThrustInput)
    {
        if (_shipData == null || _shipEntity == null) return;

        _lastDirectThrustInput = directThrustInput; // Cache for GetThrusterIntensity

        Vector2 forwardDirection = Vector2.Up.Rotated(Godot.Mathf.DegToRad(shipGlobalRotationDegrees));

        if (Godot.Mathf.Abs(directThrustInput) > 0.01f) // Forward thrust or braking
        {
            if (directThrustInput > 0.01f) // Forward thrust
            {
                currentVelocity += forwardDirection * _shipData.Acceleration * directThrustInput * (float)delta;
            }
            else // Active braking (directThrustInput is negative)
            {
                float brakingFactor = Godot.Mathf.Abs(directThrustInput);
                float effectiveDeceleration = _shipData.Deceleration + (_shipData.Acceleration * brakingFactor); 
                currentVelocity = currentVelocity.MoveToward(Vector2.Zero, effectiveDeceleration * (float)delta);
            }
        }
        else // No thrust input, apply standard deceleration
        { 
            currentVelocity = currentVelocity.MoveToward(Vector2.Zero, _shipData.Deceleration * (float)delta);
        }
    }

    public void MoveTo(Vector2 targetPosition) { /* No-op for this strategy */ }
    public void RotateTowards(float targetAngleDegrees) { /* No-op for this strategy */ }
    public void RotateBy(float relativeAngleDegrees) { /* No-op for this strategy */ }
    public void SetRotateWithVelocity(bool enabled) { /* No-op for this strategy */ }
    public void StopMovement() { /* No-op for this strategy, handled by zeroing thrust input */ }
    public void StopRotation() { /* No-op for this strategy, handled by zeroing rotation input */ }

    public bool IsAtTargetPosition(Vector2 shipGlobalPosition, Vector2 currentVelocity)
    {
        return true; // Not applicable for direct control
    }

    public bool IsFacingTargetRotation(float shipGlobalRotationDegrees)
    {
        return true; // Not applicable for direct control
    }

    public float GetThrusterIntensity(Vector2 currentVelocity, float shipGlobalRotationDegrees)
    {
        // Player/direct control: thrust only appears during forward thrust, not when braking
        return _lastDirectThrustInput > 0.01f ? _lastDirectThrustInput : 0.0f;
    }
}
