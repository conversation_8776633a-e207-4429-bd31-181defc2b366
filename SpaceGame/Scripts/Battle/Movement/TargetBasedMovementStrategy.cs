using Godot;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;
using System;

namespace SpaceGame.Scripts.Battle.Movement;

/// <summary>
/// Implements IMovementStrategy for target-based movement (MoveTo, RotateTowards).
/// </summary>
public partial class TargetBasedMovementStrategy : Node, IMovementStrategy
{
    private ShipEntity _shipEntity;
    private ShipEntityData _shipData;

    private Vector2 _currentTargetPosition;
    private bool _isMovingToPosition = false;
    private float _currentTargetRotationDegrees = 0f;
    private bool _isRotatingToTarget = false;
    private bool _shouldRotateWithVelocity = false;

    private const float MOVEMENT_THRESHOLD_SQR = 25f; // 5 units squared
    private const float ROTATION_THRESHOLD_DEG = 1.0f;

    public TargetBasedMovementStrategy()
    {
        // Required for Godot
    }

    public void Initialize(ShipEntity shipEntity, ShipEntityData shipData)
    {
        _shipEntity = shipEntity;
        _shipData = shipData;
    }

    public void UpdateShipData(ShipEntityData shipData)
    {
        _shipData = shipData;
    }

    public void Activate(Vector2 currentVelocity, float currentRotationDegrees)
    {
        // Reset state when activated, but retain current targets if they were set just before activation.
        // If this strategy is activated by a MoveTo/RotateTo call, those methods will set the targets.
        // If activated for other reasons (e.g. direct control stopping), we don't want to lose prior commands.
    }

    public void Deactivate()
    {
        // No specific deactivation logic needed for this strategy beyond what the controller handles.
    }

    public void UpdateRotation(double delta, ref float shipGlobalRotationDegrees, float directRotationInput)
    {
        if (_shipData == null) return;

        if (_isRotatingToTarget)
        {
            float angleToTarget = Godot.Mathf.RadToDeg(Godot.Mathf.AngleDifference(Godot.Mathf.DegToRad(shipGlobalRotationDegrees), Godot.Mathf.DegToRad(_currentTargetRotationDegrees)));
            float rotationThisFrame = Godot.Mathf.Sign(angleToTarget) * _shipData.RotationSpeed * (float)delta;

            if (Godot.Mathf.Abs(angleToTarget) < Godot.Mathf.Abs(rotationThisFrame) || Godot.Mathf.Abs(angleToTarget) < ROTATION_THRESHOLD_DEG)
            {
                shipGlobalRotationDegrees = _currentTargetRotationDegrees;
                _isRotatingToTarget = false; // Reached target
            }
            else
            {
                shipGlobalRotationDegrees += rotationThisFrame;
            }
            shipGlobalRotationDegrees = Godot.Mathf.Wrap(shipGlobalRotationDegrees, -180.0f, 180.0f);
        }
        // Velocity for this check will be passed in UpdateMovement and used there if needed for rotation trigger.
        // This method only handles the rotation itself if _isRotatingToTarget or _shouldRotateWithVelocity (when called from UpdateMovement)
    }

    public void UpdateMovement(double delta, ref Vector2 currentVelocity, float shipGlobalRotationDegrees, float directThrustInput)
    {
        if (_shipData == null || _shipEntity == null) return;

        if (_isMovingToPosition)
        {
            Vector2 directionToTarget = (_currentTargetPosition - _shipEntity.GlobalPosition).Normalized();
            float distanceToTarget = _shipEntity.GlobalPosition.DistanceTo(_currentTargetPosition);
            float targetSpeed = _shipData.MaxSpeed;

            if (distanceToTarget < _shipData.SlowingDistance && _shipData.SlowingDistance > 0)
            {
                targetSpeed = Godot.Mathf.Lerp(0f, _shipData.MaxSpeed, distanceToTarget / _shipData.SlowingDistance);
                targetSpeed = Godot.Mathf.Max(targetSpeed, 0f); // Ensure non-negative
            }

            currentVelocity = currentVelocity.MoveToward(directionToTarget * targetSpeed, _shipData.Acceleration * (float)delta);

            if (distanceToTarget < Godot.Mathf.Max(1.0f, _shipData.Acceleration * (float)delta))
            {
                _isMovingToPosition = false;
                currentVelocity = Vector2.Zero;
            }

            if (_shouldRotateWithVelocity && currentVelocity.LengthSquared() > 0.1f)
            {
                float targetAngle = Godot.Mathf.RadToDeg(currentVelocity.Angle()) + 90f; 
                // Internal call to set rotation target, actual rotation happens in UpdateRotation if _isRotatingToTarget becomes true.
                // We need to ensure UpdateRotation can handle this if called subsequently in the same frame by the controller.
                // For now, let's assume the controller calls UpdateRotation after UpdateMovement.
                // The original logic was to call this.RotateTowards directly.
                float angleToTarget = Godot.Mathf.RadToDeg(Godot.Mathf.AngleDifference(Godot.Mathf.DegToRad(shipGlobalRotationDegrees), Godot.Mathf.DegToRad(targetAngle)));
                float rotationThisFrame = Godot.Mathf.Sign(angleToTarget) * _shipData.RotationSpeed * (float)delta;

                if (Godot.Mathf.Abs(angleToTarget) < Godot.Mathf.Abs(rotationThisFrame) || Godot.Mathf.Abs(angleToTarget) < ROTATION_THRESHOLD_DEG)
                {
                    shipGlobalRotationDegrees = targetAngle;
                }
                else
                {
                    shipGlobalRotationDegrees += rotationThisFrame;
                }
                shipGlobalRotationDegrees = Godot.Mathf.Wrap(shipGlobalRotationDegrees, -180.0f, 180.0f);
            }
        }
        else
        {
            currentVelocity = currentVelocity.MoveToward(Vector2.Zero, _shipData.Deceleration * (float)delta);
        }
    }

    public void MoveTo(Vector2 targetPosition)
    {
        _currentTargetPosition = targetPosition;
        _isMovingToPosition = true;
        _isRotatingToTarget = false; 
    }

    public void RotateTowards(float targetAngleDegrees)
    {
        _currentTargetRotationDegrees = Godot.Mathf.Wrap(targetAngleDegrees, -180.0f, 180.0f);
        _isRotatingToTarget = true;
        _shouldRotateWithVelocity = false; 
    }

    public void RotateBy(float relativeAngleDegrees)
    {
        if (_shipEntity == null) return;
        RotateTowards(Godot.Mathf.Wrap(_shipEntity.GlobalRotationDegrees + relativeAngleDegrees, -180.0f, 180.0f)); 
    }

    public void SetRotateWithVelocity(bool enabled)
    {
        _shouldRotateWithVelocity = enabled;
        if (enabled)
        {
            _isRotatingToTarget = false; // Velocity-based rotation takes precedence
        }
    }

    public void StopMovement()
    {
        _isMovingToPosition = false;
        // Velocity will naturally decay via UpdateMovement's deceleration logic.
    }

    public void StopRotation()
    {
        _isRotatingToTarget = false;
        _shouldRotateWithVelocity = false;
    }

    public bool IsAtTargetPosition(Vector2 shipGlobalPosition, Vector2 currentVelocity)
    {
        if (!_isMovingToPosition) return true; // Not trying to move, so considered 'at target'
        // Check if close enough AND velocity is low (or moving away from target, indicating overshoot/pass)
        bool closeEnough = shipGlobalPosition.DistanceSquaredTo(_currentTargetPosition) < MOVEMENT_THRESHOLD_SQR;
        bool slowEnough = currentVelocity.LengthSquared() < (_shipData.Deceleration * 0.1f) * (_shipData.Deceleration * 0.1f); // Arbitrary low speed
        
        // More robust check: if close and velocity is not significantly towards target anymore
        if (closeEnough) {
            if (currentVelocity.LengthSquared() < 0.1f) return true; // Practically stopped
            Vector2 directionToTarget = (_currentTargetPosition - shipGlobalPosition).Normalized();
            if (currentVelocity.Normalized().Dot(directionToTarget) < 0.5f) return true; // Velocity no longer strongly aligned with target
        }
        return false;
    }

    public bool IsFacingTargetRotation(float shipGlobalRotationDegrees)
    {
        if (!_isRotatingToTarget) return true; // Not trying to rotate, so considered 'facing target'
        return Godot.Mathf.Abs(Godot.Mathf.RadToDeg(Godot.Mathf.AngleDifference(Godot.Mathf.DegToRad(shipGlobalRotationDegrees), Godot.Mathf.DegToRad(_currentTargetRotationDegrees)))) < ROTATION_THRESHOLD_DEG;
    }

    public float GetThrusterIntensity(Vector2 currentVelocity, float shipGlobalRotationDegrees)
    {
        if (_isMovingToPosition && _shipData != null && _shipEntity != null)
        {
            if (_shipEntity.GlobalPosition.DistanceSquaredTo(_currentTargetPosition) > MOVEMENT_THRESHOLD_SQR * 4) 
            {
                 Vector2 directionToTarget = (_currentTargetPosition - _shipEntity.GlobalPosition).Normalized();
                 Vector2 forwardDir = Vector2.Up.Rotated(Godot.Mathf.DegToRad(shipGlobalRotationDegrees));
                 float dot = forwardDir.Dot(directionToTarget);
                 // Only apply thrust if generally facing towards target and moving
                 if (dot > 0.5f && currentVelocity.LengthSquared() > 0.1f) 
                 {
                     // Intensity based on alignment and speed relative to max speed
                     float speedRatio = Godot.Mathf.Clamp(currentVelocity.Length() / _shipData.MaxSpeed, 0.0f, 1.0f);
                     return Godot.Mathf.Clamp(dot * speedRatio, 0.1f, 1.0f); 
                 }
            }
        }
        return 0.0f;
    }
}
