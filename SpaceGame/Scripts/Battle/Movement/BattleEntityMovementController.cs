using System.Diagnostics.CodeAnalysis;
using Godot;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.Battle.Movement;

/// <summary>
/// Controls the movement and rotation of a BattleEntity by delegating to movement strategies.
/// </summary>
[GlobalClass]
public partial class BattleEntityMovementController : Node
{
    private ShipEntity _shipEntity;
    private ShipEntityData _shipData; 

    private Vector2 _currentVelocity = Vector2.Zero;
    // Note: _shipEntity.GlobalRotationDegrees is the source of truth for rotation.

    private IMovementStrategy _activeStrategy;
    private TargetBasedMovementStrategy _targetBasedStrategy = new();
    private DirectControlMovementStrategy _directControlStrategy = new();

    private ThrusterComponent _thrusterComponent;
    private MovementStrategyType _currentStrategyType;

    // Internal state for direct control inputs
    private float _currentDirectThrustInput = 0.0f;
    private float _currentDirectRotationInput = 0.0f;

    public bool IsUnderDirectControl => _currentStrategyType == MovementStrategyType.DirectControl;
    public MovementStrategyType CurrentStrategyType => _currentStrategyType;

    public BattleEntityMovementController()
    {
    }

    public override void _Ready()
    {
        base._Ready();

        _shipEntity = GetParent<ShipEntity>();
        if (_shipEntity == null)
        {
            Logger.Error($"BattleEntityMovementController on {Name} could not find parent ShipEntity.");
            SetProcess(false);
            SetPhysicsProcess(false);
            return;
        }

        _shipData = _shipEntity.EntityData;
        if (_shipData == null)
        {
            Logger.Error($"BattleEntityMovementController on {_shipEntity.Name} could not find ShipEntityData.");
            SetProcess(false);
            SetPhysicsProcess(false);
            return;
        }

        _targetBasedStrategy.Initialize(_shipEntity, _shipData);
        _directControlStrategy.Initialize(_shipEntity, _shipData);

        // Default to target-based strategy, which handles deceleration if no commands are given.
        // Explicitly set the initial strategy using the new method structure.
        RequestStrategyChange(MovementStrategyType.TargetBased);

        _thrusterComponent = _shipEntity.GetNode<ThrusterComponent>("ThrusterComponent"); 
        if (_thrusterComponent == null)
        {
            Logger.Warning($"BattleEntityMovementController on {_shipEntity.Name}: ThrusterComponent node named 'ThrusterComponent' not found as a child. Thrusters will not function.");
        }
    }

    /// <summary>
    /// Updates the controller's properties from the provided ship entity data.
    /// Also updates the underlying movement strategies.
    /// </summary>
    /// <param name="shipData">The ship data containing movement properties.</param>
    public void UpdateFromEntityData(ShipEntityData shipData)
    {
        if (shipData == null)
        {
            Logger.Warning($"BattleEntityMovementController on {Name}: Attempted to update from null ShipEntityData");
            return;
        }
        
        _shipData = shipData;
        _targetBasedStrategy.UpdateShipData(_shipData);
        _directControlStrategy.UpdateShipData(_shipData);
        Logger.Debug($"BattleEntityMovementController: Updated movement properties. MaxSpeed: {_shipData.MaxSpeed}");
    }
    
    /// <summary>
    /// Requests a change to the specified movement strategy.
    /// </summary>
    /// <param name="strategyType">The type of strategy to switch to.</param>
    public void RequestStrategyChange(MovementStrategyType strategyType)
    {
        IMovementStrategy newStrategy = strategyType switch
        {
            MovementStrategyType.DirectControl => _directControlStrategy,
            MovementStrategyType.TargetBased => _targetBasedStrategy,
            _ => _targetBasedStrategy // Default or throw exception
        };
        SwitchToStrategyInternal(newStrategy, strategyType);
    }

    private void SwitchToStrategyInternal(IMovementStrategy newStrategy, MovementStrategyType newStrategyType)
    {
        if (_activeStrategy == newStrategy && _currentStrategyType == newStrategyType) return;

        _activeStrategy?.Deactivate();
        _activeStrategy = newStrategy;
        _currentStrategyType = newStrategyType;
        _activeStrategy?.Activate(_currentVelocity, _shipEntity.GlobalRotationDegrees);
        // Logger.Debug($"Switched to strategy: {newStrategyType}");
    }

    #region Public Control Methods

    /// <summary>
    /// Commands the entity to move towards the specified target position.
    /// This command is only effective if the TargetBased strategy is active or becomes active.
    /// </summary>
    /// <param name="targetPosition">The world position to move to.</param>
    public void MoveTo(Vector2 targetPosition)
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased && _activeStrategy is TargetBasedMovementStrategy targetStrategy)
        {
            targetStrategy.MoveTo(targetPosition);
        }
        else
        {
            Logger.Warning($"MoveTo called but current strategy is {_currentStrategyType}, not TargetBased.");
        }
    }

    /// <summary>
    /// Commands the entity to rotate towards the specified target angle.
    /// This command is only effective if the TargetBased strategy is active or becomes active.
    /// </summary>
    /// <param name="targetAngleDegrees">The target angle in degrees.</param>
    public void RotateTowards(float targetAngleDegrees)
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased && _activeStrategy is TargetBasedMovementStrategy targetStrategy)
        {
            targetStrategy.RotateTowards(targetAngleDegrees);
        }
        else
        {
            Logger.Warning($"RotateTowards called but current strategy is {_currentStrategyType}, not TargetBased.");
        }
    }

    /// <summary>
    /// Commands the entity to rotate by the specified relative angle.
    /// This command is only effective if the TargetBased strategy is active or becomes active.
    /// </summary>
    /// <param name="relativeAngleDegrees">The relative angle in degrees to rotate by.</param>
    public void RotateBy(float relativeAngleDegrees)
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased && _activeStrategy is TargetBasedMovementStrategy targetStrategy)
        {
            targetStrategy.RotateBy(relativeAngleDegrees);
        }
        else
        {
            Logger.Warning($"RotateBy called but current strategy is {_currentStrategyType}, not TargetBased.");
        }
    }

    /// <summary>
    /// Configures the entity to automatically rotate towards its current movement direction.
    /// This command is only effective if the TargetBased strategy is active or becomes active.
    /// </summary>
    /// <param name="enabled">True to enable rotation with velocity, false to disable.</param>
    public void SetRotateWithVelocity(bool enabled)
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased && _activeStrategy is TargetBasedMovementStrategy targetStrategy)
        {
            targetStrategy.SetRotateWithVelocity(enabled);
        }
        else
        {
            Logger.Warning($"SetRotateWithVelocity called but current strategy is {_currentStrategyType}, not TargetBased.");
        }
    }

    /// <summary>
    /// Stops the entity's current targeted movement. Deceleration will be handled by the active strategy or physics.
    /// </summary>
    public void StopMovement()
    {
        // This command is generic enough to apply to any strategy that supports it.
        // For TargetBased, it clears the target. For DirectControl, it might do nothing or clear current thrust input.
        // For now, assume it's primarily for TargetBased.
        if (_currentStrategyType == MovementStrategyType.TargetBased)
        {
            _activeStrategy.StopMovement();
        }
    }

    /// <summary>
    /// Stops the entity's current targeted rotation.
    /// </summary>
    public void StopRotation()
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased)
        {
            _activeStrategy.StopRotation();
        }
    }

    /// <summary>
    /// Immediately stops all movement and rotation, and resets velocity.
    /// This will switch to DirectControl strategy to ensure immediate cessation of movement.
    /// </summary>
    public void FullStop()
    {
        RequestStrategyChange(MovementStrategyType.DirectControl);
        // DirectControlStrategy stops when inputs are zero.
        _currentDirectThrustInput = 0f;
        _currentDirectRotationInput = 0f;
    }

    /// <summary>
    /// Checks if the entity is at its current target position (within the defined threshold).
    /// </summary>
    /// <returns>True if at target or not currently moving as per active strategy; false otherwise.</returns>
    public bool IsAtTargetPosition()
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased && _activeStrategy is TargetBasedMovementStrategy targetStrategy)
        {
            return targetStrategy.IsAtTargetPosition(_shipEntity.GlobalPosition, _currentVelocity);
        }
        Logger.Debug($"IsAtTargetPosition queried but current strategy is {_currentStrategyType}, not TargetBased. Returning true as default.");
        return true; // Default for non-target strategies or if cast fails
    }

    /// <summary>
    /// Checks if the entity is facing its current target rotation (within the defined threshold).
    /// </summary>
    /// <returns>True if facing target or not currently rotating as per active strategy; false otherwise.</returns>
    public bool IsFacingTargetRotation()
    {
        if (_currentStrategyType == MovementStrategyType.TargetBased && _activeStrategy is TargetBasedMovementStrategy targetStrategy)
        {
            return targetStrategy.IsFacingTargetRotation(_shipEntity.GlobalRotationDegrees);
        }
        Logger.Debug($"IsFacingTargetRotation queried but current strategy is {_currentStrategyType}, not TargetBased. Returning true as default.");
        return true; // Default for non-target strategies or if cast fails
    }

    #endregion

    #region Direct Control Input Methods

    /// <summary>
    /// Sets the direct thrust input for the entity.
    /// This input is only used if the DirectControl strategy is active.
    /// </summary>
    public void SetDirectThrustInput(float thrustInput)
    {
        // Strategy change is now explicit. This only sets the input value.
        _currentDirectThrustInput = Godot.Mathf.Clamp(thrustInput, -1.0f, 1.0f);
        if (_currentStrategyType != MovementStrategyType.DirectControl)
        {
            // Logger.Warning($"{_shipEntity.Name}: SetDirectThrustInput called but not in DirectControl strategy. Input stored but inactive. Current: {_currentStrategyType}");
        }
    }

    /// <summary>
    /// Sets the direct rotation input for the entity.
    /// This input is only used if the DirectControl strategy is active.
    /// </summary>
    public void SetDirectRotationInput(float rotationInput)
    {
        _currentDirectRotationInput = Godot.Mathf.Clamp(rotationInput, -1.0f, 1.0f);
        if (_currentStrategyType != MovementStrategyType.DirectControl)
        {
            // Logger.Warning($"{_shipEntity.Name}: SetDirectRotationInput called but not in DirectControl strategy. Input stored but inactive. Current: {_currentStrategyType}");
        }
    }

    /// <summary>
    /// Activates direct control mode. Target-based movement and rotation commands will be ignored by this strategy.
    /// </summary>
    public void StartDirectControl()
    {
        RequestStrategyChange(MovementStrategyType.DirectControl);
    }

    /// <summary>
    /// Deactivates direct control mode and switches to TargetBased strategy. Resets direct inputs.
    /// Entity will decelerate via the target-based strategy unless new commands are given.
    /// </summary>
    public void StopDirectControl()
    {
        RequestStrategyChange(MovementStrategyType.TargetBased);
        _currentDirectThrustInput = 0f;
        _currentDirectRotationInput = 0f;
    }

    #endregion

    #region Velocity Access

    /// <summary>
    /// Gets the current velocity of the entity.
    /// </summary>
    public Vector2 GetCurrentVelocity()
    {
        return _currentVelocity;
    }

    /// <summary>
    /// Sets the current velocity of the entity.
    /// This will override any ongoing movement calculations from the active strategy for the current frame.
    /// Use with caution as it bypasses strategy logic for one frame.
    /// </summary>
    public void SetCurrentVelocity(Vector2 velocity)
    {
        _currentVelocity = velocity;
    }

    #endregion

    public override void _PhysicsProcess(double delta)
    {
        if (_shipEntity == null || _shipData == null || _activeStrategy == null)
        {
            return;
        }

        // 1. Get current state from ShipEntity (rotation)
        // Velocity is managed internally by this controller
        float currentGlobalRotationDegrees = _shipEntity.GlobalRotationDegrees;

        // 2. Update active strategy
        // Pass direct inputs to the strategy; it will use them if it's DirectControl, or ignore if TargetBased
        _activeStrategy.UpdateRotation(delta, ref currentGlobalRotationDegrees, _currentDirectRotationInput);
        _shipEntity.GlobalRotationDegrees = currentGlobalRotationDegrees; // Apply the rotation calculated by the strategy

        _activeStrategy.UpdateMovement(delta, ref _currentVelocity, currentGlobalRotationDegrees, _currentDirectThrustInput);

        // 3. Apply drag/friction
        _currentVelocity *= (1.0f - Mathf.Clamp(_shipData.DragFactor * (float)delta, 0.0f, 1.0f));

        // 4. Apply max speed limit
        if (_currentVelocity.LengthSquared() > _shipData.MaxSpeed * _shipData.MaxSpeed)
        {
            _currentVelocity = _currentVelocity.Normalized() * _shipData.MaxSpeed;
        }

        // 4. Apply final velocity using CharacterBody2D physics
        _shipEntity.Velocity = _currentVelocity;
        _shipEntity.MoveAndSlide();

        // 5. Sync our internal velocity with the actual velocity after collision resolution
        _currentVelocity = _shipEntity.Velocity;

        // 6. Update thruster visuals based on active strategy's state
        if (_thrusterComponent != null)
        {
            _thrusterComponent.SetIntensity(_activeStrategy.GetThrusterIntensity(_currentVelocity, _shipEntity.GlobalRotationDegrees));
        }
    }

    // This private method was at the end of the file, potentially a duplicate or refactor of SetActiveStrategy.
    // I'm removing it to consolidate into SwitchToStrategyInternal.
    /*
    private void SwitchToStrategy(IMovementStrategy newStrategy)
    {
        if (_activeStrategy == newStrategy) return;

        _activeStrategy?.Deactivate();
        _activeStrategy = newStrategy;
        _activeStrategy?.Activate(_currentVelocity, _shipEntity.GlobalRotationDegrees);
    }
    */
}
