using System;
using Godot;
using SpaceGame.Scripts.Events;

namespace SpaceGame.Scripts.Battle
{
    /// <summary>
    /// Configuration data for a battle.
    /// </summary>
    public class BattleConfig
    {
        /// <summary>
        /// Gets or sets the ID of the battle.
        /// </summary>
        public string BattleId { get; set; }
        
        /// <summary>
        /// Gets or sets the difficulty level of the battle.
        /// </summary>
        public int DifficultyLevel { get; set; }
        
        /// <summary>
        /// Gets or sets the type of battle.
        /// </summary>
        public string BattleType { get; set; }
        
        /// <summary>
        /// Gets or sets the player ship type.
        /// </summary>
        public string PlayerShipType { get; set; }
        
        /// <summary>
        /// Gets or sets the maximum number of enemy ships.
        /// </summary>
        public int MaxEnemyShips { get; set; }
        
        /// <summary>
        /// Gets or sets the background scene for the battle.
        /// </summary>
        public string BackgroundScene { get; set; }
        
        /// <summary>
        /// Gets or sets any additional data for the battle.
        /// </summary>
        public Godot.Collections.Dictionary AdditionalData { get; set; } = new Godot.Collections.Dictionary();

        /// <summary>
        /// Creates a new battle configuration with default values.
        /// </summary>
        public BattleConfig()
        {
            BattleId = Guid.NewGuid().ToString("N");
            DifficultyLevel = 1;
            BattleType = "Standard";
            PlayerShipType = "Fighter";
            MaxEnemyShips = 5;
            BackgroundScene = "default_space";
        }

        /// <summary>
        /// Creates a copy of this battle configuration.
        /// </summary>
        /// <returns>A copy of this battle configuration.</returns>
        public BattleConfig Clone()
        {
            var clone = new BattleConfig
            {
                BattleId = BattleId,
                DifficultyLevel = DifficultyLevel,
                BattleType = BattleType,
                PlayerShipType = PlayerShipType,
                MaxEnemyShips = MaxEnemyShips,
                BackgroundScene = BackgroundScene
            };
            
            // Clone additional data
            foreach (var key in AdditionalData.Keys)
            {
                clone.AdditionalData[key] = AdditionalData[key];
            }
            
            return clone;
        }
    }
}
