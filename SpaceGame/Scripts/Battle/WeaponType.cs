using System;

namespace SpaceGame.Scripts.Battle
{
    /// <summary>
    /// Enum defining the different types of weapons available in the game.
    /// </summary>
    [Flags]
    public enum WeaponType
    {
        /// <summary>
        /// Kinetic cannon weapon type.
        /// </summary>
        KineticCannon = 1 << 0,
        
        /// <summary>
        /// Laser beam weapon type.
        /// </summary>
        LaserBeam = 1 << 1,
        
        /// <summary>
        /// Missile launcher weapon type.
        /// </summary>
        MissileLauncher = 1 << 2,
        
        /// <summary>
        /// Point defense weapon type.
        /// </summary>
        PointDefense = 1 << 3,
        
        /// <summary>
        /// Plasma turret weapon type.
        /// </summary>
        PlasmaTurret = 1 << 4
    }
}
