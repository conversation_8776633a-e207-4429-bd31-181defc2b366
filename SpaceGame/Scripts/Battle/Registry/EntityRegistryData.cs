// BattleEntityRegistryData.cs
using Godot;
using Godot.Collections;
using SpaceGame.Battle.AI;
using SpaceGame.Scripts.Battle;

namespace SpaceGame.Battle.Registry
{
    [GlobalClass]
    public partial class EntityRegistryData : Resource
    {
        [Export]
        public Dictionary<string, PackedScene> EditorRegisteredShipEntities { get; set; } = new();
        
        [Export]
        public Dictionary<string, WeaponData> EditorRegisteredWeaponData { get; set; } = new();
        
        [Export]
        public Dictionary<string, EnemyBehaviorConfiguration> EditorRegisteredBehaviors { get; set; } = new();
        
        [Export]
        public EnemyBehaviorConfiguration DefaultBehavior { get; set; }
        
        public EntityRegistryData() { }
    }
}
