using Godot;
using System.Collections.Generic;
using SpaceGame.Battle.AI;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;

namespace SpaceGame.Battle.Registry
{
    public partial class BattleEntityRegistry : Node
    {
        [Export] // Export the data resource for editor assignment
        public EntityRegistryData? EditorRegistryData { get; set; }

        private Dictionary<string, PackedScene> _codeRegisteredShipEntities = new Dictionary<string, PackedScene>();
        private Dictionary<string, WeaponData> _codeRegisteredWeaponData = new Dictionary<string, WeaponData>();

        public static BattleEntityRegistry Instance { get; private set; }

        // Public parameterless constructor required for Godot nodes
        public BattleEntityRegistry() { }

        public override void _Ready()
        {
            if (Instance != null)
            {
                Logger.Error("BattleEntityRegistry: Attempted to create multiple instances. Destroying self.");
                QueueFree();
                return;
            }
            Instance = this;
            Logger.Info("BattleEntityRegistry Instance Initialized.");

            if (EditorRegistryData == null)
            {
                Logger.Warning("BattleEntityRegistry: No EditorRegistryData assigned in the editor.");
            }
            else
            {
                Logger.Info($"BattleEntityRegistry: Loaded {EditorRegistryData.EditorRegisteredShipEntities?.Count ?? 0} entities from assigned EditorRegistryData.");
            }
        }

        public override void _ExitTree()
        {
            if (Instance == this)
            {
                Instance = null;
                Logger.Info("BattleEntityRegistry Instance Cleaned Up.");
            }
        }

        // --- Instance Methods (formerly static) ---

        public void RegisterShipEntity(string id, PackedScene scene)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.Error("BattleEntityRegistry: Attempted to register entity with null or empty ID.");
                return;
            }
            
            if (scene == null)
            {
                Logger.Error($"BattleEntityRegistry: Attempted to register a null scene for ID '{id}'.");
                return;
            }
            
            string lowerId = id.ToLowerInvariant();
            if (_codeRegisteredShipEntities.ContainsKey(lowerId))
            {
                Logger.Info($"BattleEntityRegistry: Battle Entity with ID '{id}' (as '{lowerId}') is already code-registered. Overwriting.");
            }
            
            _codeRegisteredShipEntities[lowerId] = scene;
            Logger.Debug($"BattleEntityRegistry: Code-registered entity '{id}' (as '{lowerId}').");
        }

        public void RegisterShipEntity(string id, string scenePath)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.Error("BattleEntityRegistry: Attempted to register entity with null or empty ID.");
                return;
            }
            
            if (string.IsNullOrEmpty(scenePath))
            {
                Logger.Error($"BattleEntityRegistry: Attempted to register entity '{id}' with null or empty scene path.");
                return;
            }

            if (!FileAccess.FileExists(scenePath))
            {
                Logger.Error($"BattleEntityRegistry: Scene file does not exist at path '{scenePath}' for Battle Entity ID '{id}'.");
                return;
            }

            PackedScene scene = GD.Load<PackedScene>(scenePath);
            if (scene != null)
            {
                RegisterShipEntity(id, scene); // Will be handled by the other overload with ToLowerInvariant
            }
            else
            {
                Logger.Error($"BattleEntityRegistry: Failed to load scene from path '{scenePath}' for Battle Entity ID '{id}'.");
            }
        }

        public PackedScene? GetShipEntityScene(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.Warning("BattleEntityRegistry: Attempted to get entity scene with null or empty ID.");
                return null;
            }

            string lowerId = id.ToLowerInvariant();

            if (_codeRegisteredShipEntities.TryGetValue(lowerId, out PackedScene scene))
            {
                Logger.Debug($"BattleEntityRegistry: Found code-registered scene for ID '{id}' (as '{lowerId}').");
                return scene;
            }

            if (EditorRegistryData != null)
            {
                foreach (var entry in EditorRegistryData.EditorRegisteredShipEntities)
                {
                    if (entry.Key.ToLowerInvariant() == lowerId)
                    {
                        Logger.Debug($"BattleEntityRegistry: Found editor-registered scene for ID '{id}' (matching '{entry.Key}' as '{lowerId}').");
                        return entry.Value;
                    }
                }
            }

            Logger.Warning($"BattleEntityRegistry: Battle Entity scene with ID '{id}' (as '{lowerId}') not found in code or editor registry.");
            return null;
        }

        public void UnregisterShipEntity(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.Warning("BattleEntityRegistry: Attempted to unregister entity with null or empty ID.");
                return;
            }
            string lowerId = id.ToLowerInvariant();
            if (_codeRegisteredShipEntities.Remove(lowerId))
            {
                Logger.Debug($"BattleEntityRegistry: Unregistered code entity '{id}' (as '{lowerId}').");
            }
            else
            {
                 Logger.Debug($"BattleEntityRegistry: Attempted to unregister code entity '{id}' (as '{lowerId}'), but it was not found.");
            }
        }

        public void ClearCodeRegisteredEntities()
        {
            int shipCount = _codeRegisteredShipEntities.Count;
            _codeRegisteredShipEntities.Clear();
            int weaponCount = _codeRegisteredWeaponData.Count;
            _codeRegisteredWeaponData.Clear();
            Logger.Info($"BattleEntityRegistry: Cleared {shipCount} code-registered ship entities and {weaponCount} weapon entities.");
        }
        
        /// <summary>
        /// Registers a weapon data resource in the registry for later retrieval by ID.
        /// </summary>
        /// <param name="weaponData">The weapon data to register.</param>
        public void RegisterWeaponData(WeaponData? weaponData)
        {
            if (weaponData == null)
            {
                Logger.Error("BattleEntityRegistry: Attempted to register null WeaponData.");
                return;
            }
            
            if (string.IsNullOrEmpty(weaponData.Id))
            {
                Logger.Error("BattleEntityRegistry: Attempted to register WeaponData with null or empty ID.");
                return;
            }
            
            string lowerId = weaponData.Id.ToLowerInvariant();
            if (_codeRegisteredWeaponData.ContainsKey(lowerId))
            {
                Logger.Info($"BattleEntityRegistry: WeaponData with ID '{weaponData.Id}' (as '{lowerId}') is already registered. Overwriting.");
            }
            
            _codeRegisteredWeaponData[lowerId] = weaponData;
            Logger.Debug($"BattleEntityRegistry: Registered WeaponData '{weaponData.WeaponName}' with ID '{weaponData.Id}' (as '{lowerId}').");
        }
        
        /// <summary>
        /// Registers a weapon data resource from a resource path.
        /// </summary>
        /// <param name="resourcePath">Path to the weapon data resource.</param>
        public void RegisterWeaponDataFromPath(string resourcePath)
        {
            if (string.IsNullOrEmpty(resourcePath))
            {
                Logger.Error("BattleEntityRegistry: Attempted to register WeaponData with null or empty resource path.");
                return;
            }
            
            if (!ResourceLoader.Exists(resourcePath))
            {
                Logger.Error($"BattleEntityRegistry: WeaponData resource does not exist at path '{resourcePath}'.");
                return;
            }
            
            WeaponData? weaponData = ResourceLoader.Load<WeaponData>(resourcePath);
            if (weaponData != null)
            {
                RegisterWeaponData(weaponData);
            }
            else
            {
                Logger.Error($"BattleEntityRegistry: Failed to load WeaponData from path '{resourcePath}'.");
            }
        }
        
        /// <summary>
        /// Gets a registered weapon data by its ID.
        /// </summary>
        /// <param name="weaponId">The ID of the weapon data to retrieve.</param>
        /// <returns>The weapon data, or null if not found.</returns>
        public WeaponData? GetWeaponData(string weaponId)
        {
            if (string.IsNullOrEmpty(weaponId))
            {
                Logger.Warning("BattleEntityRegistry: Attempted to get WeaponData with null or empty ID.");
                return null;
            }
            
            string lowerId = weaponId.ToLowerInvariant();
            if (_codeRegisteredWeaponData.TryGetValue(lowerId, out WeaponData weaponData))
            {
                return weaponData;
            }
            
            
            if (EditorRegistryData != null)
            {
                foreach (var entry in EditorRegistryData.EditorRegisteredWeaponData)
                {
                    if (entry.Key.ToLowerInvariant() == lowerId)
                    {
                        Logger.Debug($"BattleEntityRegistry: Found editor-registered scene for ID '{weaponId}' (matching '{entry.Key}' as '{lowerId}').");
                        return entry.Value;
                    }
                }
            }
            
            Logger.Warning($"BattleEntityRegistry: WeaponData with ID '{weaponId}' (as '{lowerId}') not found in registry.");
            return null;
        }
        
        /// <summary>
        /// Unregisters a weapon data by its ID.
        /// </summary>
        /// <param name="weaponId">The ID of the weapon data to unregister.</param>
        public void UnregisterWeaponData(string weaponId)
        {
            if (string.IsNullOrEmpty(weaponId))
            {
                Logger.Warning("BattleEntityRegistry: Attempted to unregister WeaponData with null or empty ID.");
                return;
            }
            
            string lowerId = weaponId.ToLowerInvariant();
            if (_codeRegisteredWeaponData.Remove(lowerId))
            {
                Logger.Debug($"BattleEntityRegistry: Unregistered WeaponData with ID '{weaponId}' (as '{lowerId}').");
            }
            else
            {
                Logger.Debug($"BattleEntityRegistry: Attempted to unregister WeaponData with ID '{weaponId}' (as '{lowerId}'), but it was not found.");
            }
        }
        
        /// <summary>
        /// Clears all registered weapon data.
        /// </summary>
        public void ClearRegisteredWeaponData()
        {
            int count = _codeRegisteredWeaponData.Count;
            _codeRegisteredWeaponData.Clear();
            Logger.Info($"BattleEntityRegistry: Cleared {count} registered weapon data entries.");
        }
        
        /// <summary>
        /// Gets a registered behavior configuration by its ID.
        /// </summary>
        /// <param name="behaviorId">The ID of the behavior to retrieve.</param>
        /// <returns>The behavior configuration, or the default behavior if not found.</returns>
        public EnemyBehaviorConfiguration GetBehavior(string behaviorId)
        {
            if (string.IsNullOrEmpty(behaviorId))
            {
                Logger.Warning("BattleEntityRegistry: Attempted to get behavior with null or empty ID.");
                return EditorRegistryData?.DefaultBehavior;
            }
            
            string lowerId = behaviorId.ToLowerInvariant();
            
            if (EditorRegistryData != null)
            {
                foreach (var entry in EditorRegistryData.EditorRegisteredBehaviors)
                {
                    if (entry.Key.ToLowerInvariant() == lowerId)
                    {
                        Logger.Debug($"BattleEntityRegistry: Found editor-registered behavior for ID '{behaviorId}' (matching '{entry.Key}' as '{lowerId}').");
                        return entry.Value;
                    }
                }
            }
            
            Logger.Warning($"BattleEntityRegistry: Behavior with ID '{behaviorId}' (as '{lowerId}') not found in registry. Using default behavior.");
            return EditorRegistryData?.DefaultBehavior;
        }
    }
}
