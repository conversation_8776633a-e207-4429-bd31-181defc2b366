using System;
using SpaceGame.Scripts.Battle; // Added for Allegiance enum
using SpaceGame.Scripts; // Added for ShipType
using SpaceGame.Scripts.Battle.Data; // Added for ProjectileType
using SpaceGame.Scripts.Battle.Entities; // Added for ProjectileType

namespace SpaceGame.Scripts.Battle.State;

/// <summary>
/// Represents the serializable state of battle entity data objects.
/// This class is a Plain Old C# Object (POCO) for easy serialization.
/// </summary>
public class BattleEntityStateData
{
    /// <summary>
    /// Gets or sets the unique identifier for the entity.
    /// </summary>
    public string Id { get; set; } = "";

    /// <summary>
    /// Gets or sets the display name of the entity.
    /// </summary>
    public string Name { get; set; } = "Battle Entity";

    /// <summary>
    /// Gets or sets the maximum health of the entity.
    /// </summary>
    public float MaxHealth { get; set; } = 100f;

    /// <summary>
    /// Gets or sets the maximum movement speed of this entity in units per second.
    /// </summary>
    public float MaxSpeed { get; set; } = 100.0f;
    
    /// <summary>
    /// Gets or sets the base rotation speed of this entity in degrees per second.
    /// </summary>
    public float RotationSpeed { get; set; } = 180.0f;
    
    /// <summary>
    /// Gets or sets the acceleration rate of this entity in units per second^2.
    /// </summary>
    public float Acceleration { get; set; } = 200.0f;
    
    /// <summary>
    /// Gets or sets the deceleration rate of this entity in units per second^2 (for stopping).
    /// </summary>
    public float Deceleration { get; set; } = 300.0f;
    
    /// <summary>
    /// Gets or sets the drag factor affecting the entity. Higher values mean more drag.
    /// Applied each physics frame: velocity *= (1 - drag * delta). Range typically 0-5.
    /// </summary>
    public float DragFactor { get; set; } = 0.8f;

    /// <summary>
    /// Gets or sets the distance to the target to consider the entity 'arrived'.
    /// </summary>
    public float MovementThreshold { get; set; } = 1.0f;
    
    /// <summary>
    /// Gets or sets the angle (in degrees) to the target rotation to consider the entity 'aligned'.
    /// </summary>
    public float RotationThreshold { get; set; } = 1.0f;

    /// <summary>
    /// Gets or sets the distance at which the entity starts to slow down when approaching a target.
    /// </summary>
    public float SlowingDistance { get; set; } = 50.0f;

    /// <summary>
    /// Gets or sets the allegiance of the entity.
    /// </summary>
    public Allegiance Allegiance { get; set; } = Allegiance.NeutralForces;
    
    /// <summary>
    /// Gets or sets the base category of this entity (e.g., Ship, Projectile).
    /// </summary>
    public BaseEntityType BaseType { get; set; } = BaseEntityType.Ship;

    /// <summary>
    /// Gets or sets the specific type of ship if BaseType is Ship (e.g., Fighter, Scout).
    /// </summary>
    public ShipType ShipDetailType { get; set; } = ShipType.Fighter;
    
    /// <summary>
    /// Gets or sets the projectile type (e.g., bullet, missile, energy).
    /// Only relevant if BaseType is Projectile.
    /// </summary>
    public ProjectileType ProjectileType { get; set; } = ProjectileType.Bullet;
    
    /// <summary>
    /// Gets or sets the base damage this projectile inflicts on impact.
    /// Only relevant if BaseType is Projectile.
    /// </summary>
    public float BaseDamage { get; set; } = 10.0f;
    
    /// <summary>
    /// Gets or sets the maximum lifetime of this projectile in seconds.
    /// Only relevant if BaseType is Projectile.
    /// </summary>
    public float Lifetime { get; set; } = 5.0f;
    
    /// <summary>
    /// Gets or sets whether this projectile should be destroyed on impact.
    /// Only relevant if BaseType is Projectile.
    /// </summary>
    public bool DestroyOnImpact { get; set; } = true;
    
    /// <summary>
    /// Gets or sets the area of effect radius for this projectile.
    /// Only relevant if BaseType is Projectile.
    /// </summary>
    public float AreaEffectRadius { get; set; } = 0.0f;

    /// <summary>
    /// Default constructor.
    /// </summary>
    public BattleEntityStateData()
    {
    }
}
