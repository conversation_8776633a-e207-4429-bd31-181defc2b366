using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Events;

namespace SpaceGame.Scripts.Battle
{
    /// <summary>
    /// System for calculating and applying damage to ships and other entities.
    /// </summary>
    public static class DamageSystem
    {
        // Damage type effectiveness multipliers
        private const float KINETIC_VS_SHIELD_MULTIPLIER = 0.7f;
        private const float ENERGY_VS_SHIELD_MULTIPLIER = 1.2f;
        private const float EXPLOSIVE_VS_SHIELD_MULTIPLIER = 0.9f;
        
        private const float KINETIC_VS_HULL_MULTIPLIER = 1.2f;
        private const float ENERGY_VS_HULL_MULTIPLIER = 0.8f;
        private const float EXPLOSIVE_VS_HULL_MULTIPLIER = 1.5f;
        
        /// <summary>
        /// Applies damage to a ship entity, handling shield and hull damage calculations.
        /// </summary>
        /// <param name="target">The ship to damage</param>
        /// <param name="damageInfo">Information about the damage being applied</param>
        /// <returns>The actual amount of damage applied</returns>
        public static float ApplyDamageToShip(ShipEntity target, DamageInfo damageInfo)
        {
            if (target == null || target.IsDead)
            {
                return 0f;
            }
            
            float actualDamage = 0f;
            bool shieldDamage = false;
            
            // Handle shield damage first if the ship has shields and the damage type doesn't bypass shields
            if (target.CurrentShields > 0 && damageInfo.DamageType != DamageType.Bypass)
            {
                // Calculate shield damage based on damage type
                float shieldDamageMultiplier = GetShieldDamageMultiplier(damageInfo.DamageType);
                float shieldDamageAmount = damageInfo.Amount * shieldDamageMultiplier;
                
                // Apply damage to shields
                float previousShields = target.CurrentShields;
                target.CurrentShields = Mathf.Max(0, target.CurrentShields - shieldDamageAmount);
                float shieldDamageApplied = previousShields - target.CurrentShields;
                
                actualDamage += shieldDamageApplied;
                shieldDamage = true;
                
                Logger.Debug($"{target.Name} shields took {shieldDamageApplied:F1} damage ({shieldDamageMultiplier:F1}x multiplier). Shields: {target.CurrentShields:F1}/{target.MaxShields:F1}");
                
                // If we have damage remaining after shields are depleted, apply to hull
                float remainingDamage = shieldDamageAmount - shieldDamageApplied;
                if (remainingDamage > 0)
                {
                    float hullDamageMultiplier = GetHullDamageMultiplier(damageInfo.DamageType);
                    float hullDamageAmount = remainingDamage * hullDamageMultiplier;
                    
                    float previousHealth = target.CurrentHealth;
                    target.CurrentHealth = Mathf.Max(0, target.CurrentHealth - hullDamageAmount);
                    float hullDamageApplied = previousHealth - target.CurrentHealth;
                    
                    actualDamage += hullDamageApplied;
                    
                    Logger.Debug($"{target.Name} hull took {hullDamageApplied:F1} damage from shield overflow. Hull: {target.CurrentHealth:F1}/{target.MaxHealth:F1}");
                }
                
                // Update damage info
                damageInfo.AbsorbedByShields = true;
            }
            else
            {
                // Direct hull damage
                float hullDamageMultiplier = GetHullDamageMultiplier(damageInfo.DamageType);
                float hullDamageAmount = damageInfo.Amount * hullDamageMultiplier;
                
                float previousHealth = target.CurrentHealth;
                target.CurrentHealth = Mathf.Max(0, target.CurrentHealth - hullDamageAmount);
                float hullDamageApplied = previousHealth - target.CurrentHealth;
                
                actualDamage += hullDamageApplied;
                
                Logger.Debug($"{target.Name} hull took {hullDamageApplied:F1} direct damage ({hullDamageMultiplier:F1}x multiplier). Hull: {target.CurrentHealth:F1}/{target.MaxHealth:F1}");
                
                // Update damage info
                damageInfo.AbsorbedByShields = false;
            }
            
            // Fire appropriate events
            BattleEvents.FireShipDamaged(target, damageInfo);
            
            // Check if the ship was destroyed
            if (target.CurrentHealth <= 0 && !target.IsDead)
            {
                target.IsDead = true;
                BattleEvents.FireShipDestroyed(target, damageInfo);
                Logger.Info($"{target.Name} was destroyed by {damageInfo.Source?.Name ?? "unknown"}");
            }
            
            return actualDamage;
        }
        
        /// <summary>
        /// Gets the damage multiplier for a specific damage type against shields.
        /// </summary>
        /// <param name="damageType">The type of damage</param>
        /// <returns>The damage multiplier</returns>
        private static float GetShieldDamageMultiplier(DamageType damageType)
        {
            switch (damageType)
            {
                case DamageType.Kinetic:
                    return KINETIC_VS_SHIELD_MULTIPLIER;
                case DamageType.Energy:
                    return ENERGY_VS_SHIELD_MULTIPLIER;
                case DamageType.Explosive:
                    return EXPLOSIVE_VS_SHIELD_MULTIPLIER;
                case DamageType.Bypass:
                    return 0f; // Bypass damage doesn't affect shields
                default:
                    return 1.0f;
            }
        }
        
        /// <summary>
        /// Gets the damage multiplier for a specific damage type against hull.
        /// </summary>
        /// <param name="damageType">The type of damage</param>
        /// <returns>The damage multiplier</returns>
        private static float GetHullDamageMultiplier(DamageType damageType)
        {
            switch (damageType)
            {
                case DamageType.Kinetic:
                    return KINETIC_VS_HULL_MULTIPLIER;
                case DamageType.Energy:
                    return ENERGY_VS_HULL_MULTIPLIER;
                case DamageType.Explosive:
                    return EXPLOSIVE_VS_HULL_MULTIPLIER;
                case DamageType.Bypass:
                    return 1.5f; // Bypass damage is extra effective against hull
                default:
                    return 1.0f;
            }
        }
    }
}
