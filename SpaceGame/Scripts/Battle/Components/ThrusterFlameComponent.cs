using Godot;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.Battle.Components
{
    /// <summary>
    /// Component for creating solid, cone-shaped thruster flames using sprites or polygons.
    /// Creates the classic space game thruster look with solid energy cones.
    /// </summary>
    public partial class ThrusterFlameComponent : Node2D
    {
        /// <summary>
        /// The main thruster flame sprite.
        /// </summary>
        [Export]
        public Sprite2D FlameSprite { get; set; }
        
        /// <summary>
        /// Optional secondary flame for layered effect.
        /// </summary>
        [Export]
        public Sprite2D SecondaryFlameSprite { get; set; }
        
        /// <summary>
        /// Polygon2D for creating custom flame shapes.
        /// </summary>
        [Export]
        public Polygon2D FlamePolygon { get; set; }
        
        /// <summary>
        /// Maximum length of the thruster flame.
        /// </summary>
        [Export]
        public float MaxFlameLength { get; set; } = 40.0f;
        
        /// <summary>
        /// Base width of the flame at the thruster.
        /// </summary>
        [Export]
        public float FlameBaseWidth { get; set; } = 8.0f;
        
        /// <summary>
        /// Color of the flame core.
        /// </summary>
        [Export]
        public Color FlameCoreColor { get; set; } = new Color(1.0f, 1.0f, 1.0f, 0.9f);
        
        /// <summary>
        /// Color of the flame edges.
        /// </summary>
        [Export]
        public Color FlameEdgeColor { get; set; } = new Color(0.3f, 0.7f, 1.0f, 0.7f);
        
        /// <summary>
        /// How much the flame flickers (0.0 = no flicker, 1.0 = max flicker).
        /// </summary>
        [Export]
        public float FlickerIntensity { get; set; } = 0.1f;
        
        /// <summary>
        /// Speed of the flame flicker animation.
        /// </summary>
        [Export]
        public float FlickerSpeed { get; set; } = 8.0f;
        
        /// <summary>
        /// Whether the flame is currently active.
        /// </summary>
        [Export]
        public bool FlameActive { get; set; } = false;
        
        /// <summary>
        /// Current intensity of the flame (0.0 to 1.0).
        /// </summary>
        private float _currentIntensity = 0.0f;
        
        /// <summary>
        /// Target intensity for smooth transitions.
        /// </summary>
        private float _targetIntensity = 0.0f;
        
        /// <summary>
        /// Time accumulator for flicker animation.
        /// </summary>
        private float _flickerTime = 0.0f;
        
        /// <summary>
        /// Original flame polygon points for scaling.
        /// </summary>
        private Vector2[] _originalPolygonPoints;
        
        /// <summary>
        /// Reference to the ship entity this flame belongs to.
        /// </summary>
        private BaseBattleEntity _shipEntity;
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            // Get reference to the ship entity
            _shipEntity = GetParent().GetParent<BaseBattleEntity>();
            
            // Initialize components
            SetupFlameComponents();
            
            // Store original polygon points if using polygon
            if (FlamePolygon != null && FlamePolygon.Polygon.Length > 0)
            {
                _originalPolygonPoints = FlamePolygon.Polygon;
            }
            else
            {
                CreateDefaultFlamePolygon();
            }
            
            // Initially hide the flame
            SetFlameVisibility(false);
        }
        
        /// <summary>
        /// Called every frame to update the flame.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        public override void _Process(double delta)
        {
            base._Process(delta);
            
            UpdateFlameIntensity((float)delta);
            UpdateFlameAppearance((float)delta);
        }
        
        /// <summary>
        /// Sets up the flame components if they're not assigned.
        /// </summary>
        private void SetupFlameComponents()
        {
            if (FlameSprite == null)
            {
                FlameSprite = GetNodeOrNull<Sprite2D>("FlameSprite");
            }
            
            if (SecondaryFlameSprite == null)
            {
                SecondaryFlameSprite = GetNodeOrNull<Sprite2D>("SecondaryFlameSprite");
            }
            
            if (FlamePolygon == null)
            {
                FlamePolygon = GetNodeOrNull<Polygon2D>("FlamePolygon");
            }
        }
        
        /// <summary>
        /// Creates a default flame polygon if none exists.
        /// </summary>
        private void CreateDefaultFlamePolygon()
        {
            if (FlamePolygon == null)
            {
                FlamePolygon = new Polygon2D();
                AddChild(FlamePolygon);
            }
            
            // Create a cone-shaped polygon
            var points = new Vector2[]
            {
                new Vector2(-FlameBaseWidth * 0.5f, 0),           // Left base
                new Vector2(FlameBaseWidth * 0.5f, 0),            // Right base
                new Vector2(FlameBaseWidth * 0.3f, MaxFlameLength * 0.7f),  // Right mid
                new Vector2(0, MaxFlameLength),                   // Tip
                new Vector2(-FlameBaseWidth * 0.3f, MaxFlameLength * 0.7f)  // Left mid
            };
            
            FlamePolygon.Polygon = points;
            _originalPolygonPoints = points;
            
            // Set up gradient
            var gradient = new Gradient();
            gradient.Colors = new Color[] { FlameCoreColor, FlameEdgeColor };
            gradient.Offsets = new float[] { 0.0f, 1.0f };
            FlamePolygon.Color = FlameCoreColor;
        }
        
        /// <summary>
        /// Updates the flame intensity with smooth transitions.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        private void UpdateFlameIntensity(float delta)
        {
            // Smooth transition to target intensity
            float transitionSpeed = 5.0f;
            _currentIntensity = Mathf.MoveToward(_currentIntensity, _targetIntensity, transitionSpeed * delta);
            
            // Update visibility based on intensity
            bool shouldBeVisible = _currentIntensity > 0.01f && FlameActive;
            SetFlameVisibility(shouldBeVisible);
        }
        
        /// <summary>
        /// Updates the flame appearance including flicker and scaling.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        private void UpdateFlameAppearance(float delta)
        {
            if (_currentIntensity <= 0.01f)
                return;
                
            // Update flicker time
            _flickerTime += delta * FlickerSpeed;
            
            // Calculate flicker effect
            float flickerOffset = Mathf.Sin(_flickerTime) * FlickerIntensity;
            float flickerScale = 1.0f + flickerOffset * 0.1f;
            
            // Calculate final intensity with flicker
            float finalIntensity = _currentIntensity * flickerScale;
            finalIntensity = Mathf.Clamp(finalIntensity, 0.0f, 1.0f);
            
            // Update polygon if using polygon
            if (FlamePolygon != null && _originalPolygonPoints != null)
            {
                UpdatePolygonFlame(finalIntensity, flickerOffset);
            }
            
            // Update sprites if using sprites
            if (FlameSprite != null)
            {
                UpdateSpriteFlame(FlameSprite, finalIntensity, 1.0f);
            }
            
            if (SecondaryFlameSprite != null)
            {
                UpdateSpriteFlame(SecondaryFlameSprite, finalIntensity, 0.8f);
            }
        }
        
        /// <summary>
        /// Updates the polygon-based flame.
        /// </summary>
        /// <param name="intensity">Current flame intensity.</param>
        /// <param name="flickerOffset">Flicker offset for animation.</param>
        private void UpdatePolygonFlame(float intensity, float flickerOffset)
        {
            var scaledPoints = new Vector2[_originalPolygonPoints.Length];
            
            for (int i = 0; i < _originalPolygonPoints.Length; i++)
            {
                Vector2 point = _originalPolygonPoints[i];
                
                // Scale length based on intensity
                point.Y *= intensity;
                
                // Add slight width variation for flicker
                if (point.X != 0) // Don't modify the tip point
                {
                    point.X *= (1.0f + flickerOffset * 0.1f);
                }
                
                scaledPoints[i] = point;
            }
            
            FlamePolygon.Polygon = scaledPoints;
            
            // Update color based on intensity
            Color currentColor = FlameCoreColor.Lerp(FlameEdgeColor, 1.0f - intensity);
            currentColor.A = intensity * FlameCoreColor.A;
            FlamePolygon.Color = currentColor;
        }
        
        /// <summary>
        /// Updates a sprite-based flame.
        /// </summary>
        /// <param name="sprite">The sprite to update.</param>
        /// <param name="intensity">Current flame intensity.</param>
        /// <param name="layerMultiplier">Multiplier for layered effects.</param>
        private void UpdateSpriteFlame(Sprite2D sprite, float intensity, float layerMultiplier)
        {
            // Scale the sprite based on intensity
            float scaleY = intensity * layerMultiplier;
            float scaleX = 1.0f + (intensity - 1.0f) * 0.1f; // Slight width variation
            
            sprite.Scale = new Vector2(scaleX, scaleY);
            
            // Update color and transparency
            Color spriteColor = sprite.Modulate;
            spriteColor.A = intensity * layerMultiplier;
            sprite.Modulate = spriteColor;
        }
        
        /// <summary>
        /// Sets the visibility of all flame components.
        /// </summary>
        /// <param name="visible">Whether the flame should be visible.</param>
        private void SetFlameVisibility(bool visible)
        {
            if (FlameSprite != null)
                FlameSprite.Visible = visible;
                
            if (SecondaryFlameSprite != null)
                SecondaryFlameSprite.Visible = visible;
                
            if (FlamePolygon != null)
                FlamePolygon.Visible = visible;
        }
        
        /// <summary>
        /// Sets the flame intensity.
        /// </summary>
        /// <param name="intensity">Target intensity (0.0 to 1.0).</param>
        public void SetFlameIntensity(float intensity)
        {
            _targetIntensity = Mathf.Clamp(intensity, 0.0f, 1.0f);
        }
        
        /// <summary>
        /// Sets whether the flame is active.
        /// </summary>
        /// <param name="active">Whether the flame should be active.</param>
        public void SetFlameActive(bool active)
        {
            FlameActive = active;
            if (!active)
            {
                _targetIntensity = 0.0f;
            }
        }
        
        /// <summary>
        /// Sets the flame colors.
        /// </summary>
        /// <param name="coreColor">Core flame color.</param>
        /// <param name="edgeColor">Edge flame color.</param>
        public void SetFlameColors(Color coreColor, Color edgeColor)
        {
            FlameCoreColor = coreColor;
            FlameEdgeColor = edgeColor;
        }
        
        /// <summary>
        /// Sets the flame size parameters.
        /// </summary>
        /// <param name="maxLength">Maximum flame length.</param>
        /// <param name="baseWidth">Base width of the flame.</param>
        public void SetFlameSize(float maxLength, float baseWidth)
        {
            MaxFlameLength = maxLength;
            FlameBaseWidth = baseWidth;
            
            // Recreate polygon with new size
            if (FlamePolygon != null)
            {
                CreateDefaultFlamePolygon();
            }
        }
    }
}
