using Godot;
using System;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Battle.Movement;

/// <summary>
/// Base component that provides control functionality for battle entities.
/// This serves as the base class for all control components like player control and AI control.
/// </summary>
[GlobalClass]
public abstract partial class EntityControlComponent : Node
{
    /// <summary>
    /// Reference to the parent battle entity.
    /// </summary>
    protected BaseBattleEntity _entity;
    public BaseBattleEntity Entity => _entity;

    /// <summary>
    /// Reference to the movement controller of the parent entity.
    /// </summary>
    protected BattleEntityMovementController _movementController;

    /// <summary>
    /// Whether this control component is currently active.
    /// </summary>
    [Export]
    public bool IsActive { get; set; } = false;

    /// <summary>
    /// Event triggered when this control component is activated.
    /// </summary>
    public static event EventHandler<EntityControlEventArgs> ControlActivated;

    /// <summary>
    /// Event triggered when this control component is deactivated.
    /// </summary>
    public static event EventHandler<EntityControlEventArgs> ControlDeactivated;

    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// </summary>
    public override void _Ready()
    {
        // Get the parent entity
        _entity = GetParent<BaseBattleEntity>();
        
        if (_entity == null)
        {
            Logger.Error($"{GetType().Name} must be attached to a BaseBattleEntity. Parent is {GetParent().GetType().Name}");
            QueueFree();
            return;
        }

        // Get the movement controller
        if (_entity is ShipEntity shipEntity)
        {
            _movementController = shipEntity.MovementController;
            if (_movementController == null)
            {
                Logger.Error($"{GetType().Name} on {_entity.Name}: BattleEntityMovementController not found on parent ShipEntity.");
                // Depending on requirements, could also QueueFree() or disable the component
            }
        }
        else
        {
            Logger.Warning($"{GetType().Name} attached to a non-ShipEntity {_entity.Name}. Movement control will not be available.");
        }
        
        Logger.Debug($"{GetType().Name} attached to {_entity.Name}");
        
        // Initialize in inactive state by default
        IsActive = false;
    }

    /// <summary>
    /// Called every frame to process control logic.
    /// </summary>
    /// <param name="delta">The elapsed time since the previous frame.</param>
    public override void _Process(double delta)
    {
        // Only process control if active
        if (IsActive)
        {
            ProcessControl(delta);
        }
    }

    /// <summary>
    /// Processes control logic for the entity.
    /// Must be implemented by derived classes.
    /// </summary>
    /// <param name="delta">The elapsed time since the previous frame.</param>
    protected abstract void ProcessControl(double delta);

    /// <summary>
    /// Activates this control component.
    /// </summary>
    public virtual void Activate()
    {
        if (IsActive)
            return;

        IsActive = true;
        OnControlActivated();
    }

    /// <summary>
    /// Deactivates this control component.
    /// </summary>
    public virtual void Deactivate()
    {
        if (!IsActive)
            return;

        IsActive = false;
        OnControlDeactivated();
    }

    /// <summary>
    /// Toggles the active state of this control component.
    /// </summary>
    public virtual void ToggleActive()
    {
        if (IsActive)
        {
            Deactivate();
        }
        else
        {
            Activate();
        }
    }

    /// <summary>
    /// Called when this control component is activated.
    /// </summary>
    protected virtual void OnControlActivated()
    {
        Logger.Debug($"{GetType().Name} activated for {_entity.Name}");
        
        // Trigger the event
        ControlActivated?.Invoke(this, new EntityControlEventArgs(_entity));
    }

    /// <summary>
    /// Called when this control component is deactivated.
    /// </summary>
    protected virtual void OnControlDeactivated()
    {
        Logger.Debug($"{GetType().Name} deactivated for {_entity.Name}");
        
        // Reset inputs when control is deactivated
        _movementController?.SetDirectThrustInput(0.0f);
        _movementController?.SetDirectRotationInput(0.0f);
        
        // Trigger the event
        ControlDeactivated?.Invoke(this, new EntityControlEventArgs(_entity));
    }
}

/// <summary>
/// Event arguments for entity control events.
/// </summary>
public class EntityControlEventArgs : EventArgs
{
    /// <summary>
    /// The entity that is being controlled.
    /// </summary>
    public BaseBattleEntity Entity { get; }

    /// <summary>
    /// Creates a new instance of EntityControlEventArgs.
    /// </summary>
    /// <param name="entity">The entity being controlled.</param>
    public EntityControlEventArgs(BaseBattleEntity entity)
    {
        Entity = entity;
    }
}
