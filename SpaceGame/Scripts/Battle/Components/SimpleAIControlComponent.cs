using Godot;
using System;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Components;

/// <summary>
/// Component that provides simple AI control for a battle entity.
/// This is used when the entity is not being controlled by the player.
/// </summary>
[GlobalClass]
public partial class SimpleAIControlComponent : EntityControlComponent
{
    /// <summary>
    /// The maximum distance the AI will wander from its starting position.
    /// </summary>
    [Export]
    public float WanderRadius { get; set; } = 500.0f;

    /// <summary>
    /// How often the AI changes its movement direction, in seconds.
    /// </summary>
    [Export]
    public float DirectionChangeInterval { get; set; } = 3.0f;

    /// <summary>
    /// The starting position of the entity.
    /// </summary>
    private Vector2 _startingPosition;

    /// <summary>
    /// Timer for changing direction.
    /// </summary>
    private float _directionChangeTimer = 0.0f;

    /// <summary>
    /// Current target rotation for the entity.
    /// </summary>
    private float _targetRotation = 0.0f;

    /// <summary>
    /// Current thrust input for the entity.
    /// </summary>
    private float _thrustInput = 0.0f;

    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// </summary>
    public override void _Ready()
    {
        base._Ready();
        
        // Store the starting position
        _startingPosition = _entity.Position;
        
        // Initialize with random direction
        _targetRotation = (float)GD.RandRange(0, Mathf.Pi * 2);
        _directionChangeTimer = 0.0f;
        
        // Check for PlayerControlComponent and subscribe to its events
        var playerControlComponent = _entity.GetNodeOrNull<PlayerControlComponent>("PlayerControlComponent");
        if (playerControlComponent != null)
        {
            PlayerControlComponent.PlayerControlGained += OnPlayerControlGained;
            PlayerControlComponent.PlayerControlLost += OnPlayerControlLost;
            
            // Disable AI if player control is active
            IsActive = !playerControlComponent.IsPlayerControlled;
        }
        else
        {
            // If there's no PlayerControlComponent, activate AI by default
            IsActive = true;
        }
    }

    /// <summary>
    /// Called when the node exits the scene tree.
    /// </summary>
    public override void _ExitTree()
    {
        // Unsubscribe from events
        PlayerControlComponent.PlayerControlGained -= OnPlayerControlGained;
        PlayerControlComponent.PlayerControlLost -= OnPlayerControlLost;
    }

    /// <summary>
    /// Processes AI control logic for the entity.
    /// </summary>
    /// <param name="delta">The elapsed time since the previous frame.</param>
    protected override void ProcessControl(double delta)
    {
        // Update direction change timer
        _directionChangeTimer -= (float)delta;
        
        // Check if we need to change direction
        if (_directionChangeTimer <= 0.0f)
        {
            ChooseNewDirection();
            _directionChangeTimer = DirectionChangeInterval;
        }
        
        // Calculate rotation needed to reach target rotation
        float currentRotation = _entity.Rotation;
        float rotationDifference = Mathf.AngleDifference(currentRotation, _targetRotation);
        
        // Apply rotation input
        float rotationInput = Mathf.Sign(rotationDifference) * Mathf.Min(1.0f, Mathf.Abs(rotationDifference) * 2.0f);
        _movementController?.SetDirectRotationInput(-rotationInput);
        
        // Apply thrust input
        _movementController?.SetDirectThrustInput(_thrustInput);
    }

    /// <summary>
    /// Chooses a new direction for the AI to move in.
    /// </summary>
    private void ChooseNewDirection()
    {
        // Check if we're too far from the starting position
        float distanceFromStart = (_entity.Position - _startingPosition).Length();
        
        if (distanceFromStart > WanderRadius)
        {
            // Head back toward the starting position
            Vector2 directionToStart = (_startingPosition - _entity.Position).Normalized();
            _targetRotation = Mathf.Atan2(directionToStart.Y, directionToStart.X);
            _thrustInput = 0.5f; // Use moderate thrust to return
        }
        else
        {
            // Choose a random direction
            _targetRotation = (float)GD.RandRange(0, Mathf.Pi * 2);
            _thrustInput = (float)GD.RandRange(0.3f, 0.7f); // Random thrust between 30% and 70%
        }
    }

    /// <summary>
    /// Called when player control is gained.
    /// </summary>
    private void OnPlayerControlGained(object sender, PlayerControlEventArgs args)
    {
        // Only respond to events for our parent entity
        if (args.Entity == _entity)
        {
            // Disable AI when player takes control
            IsActive = false;
            Logger.Debug($"AI control disabled for {_entity.Name} due to player control");
        }
    }

    /// <summary>
    /// Called when player control is lost.
    /// </summary>
    private void OnPlayerControlLost(object sender, PlayerControlEventArgs args)
    {
        // Only respond to events for our parent entity
        if (args.Entity == _entity)
        {
            // Enable AI when player releases control
            IsActive = true;
            Logger.Debug($"AI control enabled for {_entity.Name} after player control released");
        }
    }
}
