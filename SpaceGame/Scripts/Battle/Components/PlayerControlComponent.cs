using Godot;
using System;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Events;

namespace SpaceGame.Scripts.Battle.Components
{
    /// <summary>
    /// Component that allows a battle entity to be controlled by the player.
    /// This can be attached to any ShipEntity to make it player-controllable.
    /// </summary>
    [GlobalClass]
    public partial class PlayerControlComponent : EntityControlComponent
    {
        /// <summary>
        /// Whether the player is currently controlling this entity.
        /// This is an alias for IsActive from the base class for backward compatibility.
        /// </summary>
        public bool IsPlayerControlled
        {
            get => IsActive;
            set => IsActive = value;
        }

        /// <summary>
        /// Event triggered when player control is gained.
        /// </summary>
        public static event EventHandler<PlayerControlEventArgs> PlayerControlGained;

        /// <summary>
        /// Event triggered when player control is lost.
        /// </summary>
        public static event EventHandler<PlayerControlEventArgs> PlayerControlLost;

        /// <summary>
        /// Activates player control for this entity.
        /// </summary>
        public override void Activate()
        {
            if (IsActive)
                return;
                
            base.Activate();
            _movementController?.StartDirectControl(); 
            OnPlayerControlGained();
        }
        
        /// <summary>
        /// Deactivates player control for this entity.
        /// </summary>
        public override void Deactivate()
        {
            if (!IsActive)
                return;
                
            base.Deactivate();
            _movementController?.StopDirectControl(); 
            OnPlayerControlLost();
        }

        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            // _entity and _movementController are initialized in base._Ready()
            // We can add a specific check for _movementController if needed after base._Ready()
            if (_movementController == null) 
            {
                Logger.Error($"PlayerControlComponent on {_entity?.Name ?? "UnknownEntity"}: BattleEntityMovementController (inherited _movementController) is null after base._Ready().");
            }
            
            if (IsPlayerControlled) // This refers to the IsActive property
            {
                // Ensure _movementController is set before calling Activate
                if (_movementController != null) 
                {
                    Activate();
                }
                else
                {
                    Logger.Warning($"PlayerControlComponent on {_entity?.Name ?? "UnknownEntity"}: IsPlayerControlled is true in _Ready, but _movementController is not available. Control will not be activated.");
                }
            }
        }

        /// <summary>
        /// Processes player input for the entity.
        /// </summary>
        /// <param name="delta">The elapsed time since the previous frame.</param>
        protected override void ProcessControl(double delta)
        {
            // Ensure movement controller is available before processing input
            if (_movementController == null) 
            {
                return;
            }
            ProcessPlayerInput();
        }

        /// <summary>
        /// Processes player input and applies it to the entity.
        /// </summary>
        protected virtual void ProcessPlayerInput()
        {
            // Get movement input
            float thrustInput = 0.0f;
            if (Input.IsActionPressed("move_forward"))
            {
                thrustInput += 1.0f;
            }
            if (Input.IsActionPressed("move_backward"))
            {
                thrustInput -= 1.0f;
            }
            
            // Get rotation input
            float rotationInput = 0.0f;
            if (Input.IsActionPressed("rotate_left"))
            {
                rotationInput -= 1.0f;
            }
            if (Input.IsActionPressed("rotate_right"))
            {
                rotationInput += 1.0f;
            }
            
            // Apply inputs to the movement controller
            _movementController?.SetDirectThrustInput(thrustInput);     
            _movementController?.SetDirectRotationInput(rotationInput);

            if (Input.IsActionPressed("fire_primary"))
            {
                (_entity as ShipEntity)?.FirePrimaryWeapons();
            }
        }

        /// <summary>
        /// Called when this entity gains player control.
        /// </summary>
        protected virtual void OnPlayerControlGained()
        {
            Logger.Debug($"Player gained control of {_entity.Name}");
            
            // Trigger the component event
            PlayerControlGained?.Invoke(this, new PlayerControlEventArgs(_entity));
            
            // Trigger the global event
            // Cast to ShipEntity since BattleEvents.RaisePlayerControlGiven expects a ShipEntity
            if (_entity is ShipEntity shipEntity)
            {
                BattleEvents.RaisePlayerControlGiven(shipEntity);
            }
            else
            {
                Logger.Warning($"Player control given to non-ShipEntity: {_entity.Name}");
            }
        }

        /// <summary>
        /// Called when this entity loses player control.
        /// </summary>
        protected virtual void OnPlayerControlLost()
        {
            Logger.Debug($"Player lost control of {_entity.Name}");
            
            // Inputs are reset by _movementController.StopDirectControl()
            
            // Trigger the component event
            PlayerControlLost?.Invoke(this, new PlayerControlEventArgs(_entity));
            
            // Trigger the global event
            // Cast to ShipEntity since BattleEvents.RaisePlayerControlRemoved expects a ShipEntity
            if (_entity is ShipEntity shipEntity)
            {
                BattleEvents.RaisePlayerControlRemoved(shipEntity);
            }
            else
            {
                Logger.Warning($"Player control removed from non-ShipEntity: {_entity.Name}");
            }
        }

        /// <summary>
        /// Gives player control to this entity.
        /// </summary>
        public void GivePlayerControl()
        {
            Activate();
        }

        /// <summary>
        /// Removes player control from this entity.
        /// </summary>
        public void RemovePlayerControl()
        {
            Deactivate();
        }

        /// <summary>
        /// Toggles player control of this entity.
        /// </summary>
        public void TogglePlayerControl()
        {
            ToggleActive();
        }
    }

    /// <summary>
    /// Event arguments for player control events.
    /// </summary>
    public class PlayerControlEventArgs : EventArgs
    {
        /// <summary>
        /// The entity that gained or lost player control.
        /// </summary>
        public BaseBattleEntity Entity { get; }
        
        /// <summary>
        /// Gets the entity as a ShipEntity if it is one, or null otherwise.
        /// </summary>
        public ShipEntity? ShipEntity => Entity as ShipEntity;

        /// <summary>
        /// Creates a new instance of PlayerControlEventArgs.
        /// </summary>
        /// <param name="entity">The entity that gained or lost player control.</param>
        public PlayerControlEventArgs(BaseBattleEntity entity)
        {
            Entity = entity;
        }
    }
}