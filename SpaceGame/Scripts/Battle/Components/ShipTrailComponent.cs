using Godot;
using System.Collections.Generic;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.Battle.Components
{
    /// <summary>
    /// Component for creating visual trails behind moving spaceships.
    /// Uses Line2D to create smooth, fading trails that show the ship's movement path.
    /// </summary>
    public partial class ShipTrailComponent : Node2D
    {
        /// <summary>
        /// The Line2D node used to render the trail.
        /// </summary>
        [Export]
        public Line2D TrailLine { get; set; }
        
        /// <summary>
        /// Maximum number of points in the trail.
        /// </summary>
        [Export]
        public int MaxTrailPoints { get; set; } = 50;
        
        /// <summary>
        /// Minimum distance the ship must move before adding a new trail point.
        /// </summary>
        [Export]
        public float MinPointDistance { get; set; } = 5.0f;
        
        /// <summary>
        /// Width of the trail at the newest point.
        /// </summary>
        [Export]
        public float TrailWidth { get; set; } = 3.0f;
        
        /// <summary>
        /// Color of the trail at the newest point.
        /// </summary>
        [Export]
        public Color TrailColor { get; set; } = new Color(0.3f, 0.7f, 1.0f, 0.8f);
        
        /// <summary>
        /// How quickly the trail fades out (0.0 = never fades, 1.0 = fades immediately).
        /// </summary>
        [Export]
        public float FadeRate { get; set; } = 0.02f;
        
        /// <summary>
        /// Minimum velocity required to show the trail.
        /// </summary>
        [Export]
        public float MinVelocityThreshold { get; set; } = 10.0f;
        
        /// <summary>
        /// Whether the trail is currently enabled.
        /// </summary>
        [Export]
        public bool TrailEnabled { get; set; } = true;
        
        /// <summary>
        /// Reference to the ship entity this trail belongs to.
        /// </summary>
        private BaseBattleEntity _shipEntity;
        
        /// <summary>
        /// List of trail points with their alpha values for fading.
        /// </summary>
        private List<TrailPoint> _trailPoints = new List<TrailPoint>();
        
        /// <summary>
        /// Last position where a trail point was added.
        /// </summary>
        private Vector2 _lastTrailPosition = Vector2.Zero;
        
        /// <summary>
        /// Structure to hold trail point data.
        /// </summary>
        private struct TrailPoint
        {
            public Vector2 Position;
            public float Alpha;
            public float Width;
            
            public TrailPoint(Vector2 position, float alpha, float width)
            {
                Position = position;
                Alpha = alpha;
                Width = width;
            }
        }
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            // Get reference to the ship entity
            _shipEntity = GetParent<BaseBattleEntity>();
            
            // Initialize the trail line if not set
            if (TrailLine == null)
            {
                TrailLine = GetNodeOrNull<Line2D>("TrailLine");
                if (TrailLine == null)
                {
                    // Create a new Line2D if one doesn't exist
                    TrailLine = new Line2D();
                    AddChild(TrailLine);
                }
            }
            
            // Configure the trail line
            SetupTrailLine();
            
            // Initialize the last trail position
            if (_shipEntity != null)
            {
                _lastTrailPosition = _shipEntity.GlobalPosition;
            }
        }
        
        /// <summary>
        /// Called every frame to update the trail.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        public override void _Process(double delta)
        {
            base._Process(delta);
            
            if (!TrailEnabled || _shipEntity == null)
            {
                return;
            }
            
            UpdateTrail((float)delta);
        }
        
        /// <summary>
        /// Sets up the initial configuration of the trail line.
        /// </summary>
        private void SetupTrailLine()
        {
            if (TrailLine == null)
                return;
                
            TrailLine.Width = TrailWidth;
            TrailLine.DefaultColor = TrailColor;
            TrailLine.JointMode = Line2D.LineJointMode.Round;
            TrailLine.BeginCapMode = Line2D.LineCapMode.Round;
            TrailLine.EndCapMode = Line2D.LineCapMode.Round;
            TrailLine.Antialiased = true;
        }
        
        /// <summary>
        /// Updates the trail based on the ship's current position and velocity.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        private void UpdateTrail(float delta)
        {
            Vector2 currentPosition = _shipEntity.GlobalPosition;
            Vector2 currentVelocity = _shipEntity.Velocity;
            
            // Check if the ship is moving fast enough to show a trail
            bool shouldShowTrail = currentVelocity.Length() >= MinVelocityThreshold;
            
            if (shouldShowTrail)
            {
                // Add new trail point if the ship has moved far enough
                float distanceMoved = currentPosition.DistanceTo(_lastTrailPosition);
                if (distanceMoved >= MinPointDistance)
                {
                    AddTrailPoint(currentPosition);
                    _lastTrailPosition = currentPosition;
                }
            }
            
            // Update existing trail points (fade them out)
            UpdateTrailPoints(delta);
            
            // Update the visual representation
            UpdateTrailVisuals();
        }
        
        /// <summary>
        /// Adds a new point to the trail.
        /// </summary>
        /// <param name="position">The global position to add.</param>
        private void AddTrailPoint(Vector2 position)
        {
            // Convert global position to local position relative to this node
            Vector2 localPosition = ToLocal(position);
            
            // Add the new point with full alpha
            _trailPoints.Add(new TrailPoint(localPosition, 1.0f, TrailWidth));
            
            // Remove old points if we exceed the maximum
            while (_trailPoints.Count > MaxTrailPoints)
            {
                _trailPoints.RemoveAt(0);
            }
        }
        
        /// <summary>
        /// Updates the alpha values of existing trail points to create a fading effect.
        /// </summary>
        /// <param name="delta">Time elapsed since the last frame.</param>
        private void UpdateTrailPoints(float delta)
        {
            for (int i = _trailPoints.Count - 1; i >= 0; i--)
            {
                var point = _trailPoints[i];
                point.Alpha -= FadeRate * delta * 60.0f; // Normalize for 60 FPS
                
                if (point.Alpha <= 0.0f)
                {
                    _trailPoints.RemoveAt(i);
                }
                else
                {
                    _trailPoints[i] = point;
                }
            }
        }
        
        /// <summary>
        /// Updates the visual representation of the trail.
        /// </summary>
        private void UpdateTrailVisuals()
        {
            if (TrailLine == null)
                return;
                
            // Clear existing points
            TrailLine.ClearPoints();
            
            if (_trailPoints.Count < 2)
            {
                TrailLine.Visible = false;
                return;
            }
            
            TrailLine.Visible = true;
            
            // Add points to the line
            foreach (var point in _trailPoints)
            {
                TrailLine.AddPoint(point.Position);
            }
            
            // Create gradient for fading effect
            var gradient = new Gradient();
            var colors = new Color[_trailPoints.Count];
            var offsets = new float[_trailPoints.Count];
            
            for (int i = 0; i < _trailPoints.Count; i++)
            {
                float normalizedPosition = (float)i / (_trailPoints.Count - 1);
                offsets[i] = normalizedPosition;
                
                Color pointColor = TrailColor;
                pointColor.A = _trailPoints[i].Alpha * TrailColor.A;
                colors[i] = pointColor;
            }
            
            gradient.Colors = colors;
            gradient.Offsets = offsets;
            
            // Apply gradient to the line
            TrailLine.Gradient = gradient;
        }
        
        /// <summary>
        /// Clears all trail points.
        /// </summary>
        public void ClearTrail()
        {
            _trailPoints.Clear();
            if (TrailLine != null)
            {
                TrailLine.ClearPoints();
                TrailLine.Visible = false;
            }
        }
        
        /// <summary>
        /// Sets the trail color.
        /// </summary>
        /// <param name="color">The new trail color.</param>
        public void SetTrailColor(Color color)
        {
            TrailColor = color;
            if (TrailLine != null)
            {
                TrailLine.DefaultColor = color;
            }
        }
        
        /// <summary>
        /// Sets the trail width.
        /// </summary>
        /// <param name="width">The new trail width.</param>
        public void SetTrailWidth(float width)
        {
            TrailWidth = width;
            if (TrailLine != null)
            {
                TrailLine.Width = width;
            }
        }
        
        /// <summary>
        /// Enables or disables the trail.
        /// </summary>
        /// <param name="enabled">Whether the trail should be enabled.</param>
        public void SetTrailEnabled(bool enabled)
        {
            TrailEnabled = enabled;
            if (!enabled)
            {
                ClearTrail();
            }
        }
    }
}
