# Entity Control System Migration Guide

This document explains how to migrate from the previous inheritance-based `PlayerControllableEntity` approach to the new component-based entity control system.

## Overview of the New System

The new system uses a component-based approach that allows any `BattleEntity` to be player-controllable by attaching components:

1. **EntityControlComponent**: Base abstract class for all control components
2. **PlayerControlComponent**: Attaches to any `BattleEntity` to make it player-controllable
3. **SimpleAIControlComponent**: Provides basic AI control when the entity is not player-controlled
4. **EntityControlManager**: Static utility class that manages which entity is currently controlled by the player

The system is designed to be extensible, allowing you to create custom control components by inheriting from `EntityControlComponent`.

## Migration Steps

### 1. Replace PlayerControllableEntity Inheritance with Components

**Before:**
```csharp
public partial class MyShip : PlayerControllableEntity
{
    // Ship-specific code
}
```

**After:**
```csharp
public partial class MyShip : BattleEntity
{
    // Ship-specific code
}
```

Then add the PlayerControlComponent to your entity in the scene editor:
1. Add a new Node as a child of your entity
2. Select "PlayerControlComponent" as the node type
3. Name it "PlayerControlComponent"

### 2. Initialize EntityControlManager

The EntityControlManager is now a static utility class, so you don't need to add it as an autoload. Instead, initialize it at the start of each battle:

```csharp
// Reset the EntityControlManager state at the start of a new battle
EntityControlManager.Initialize();
```

### 3. Register Controllable Entities

In your battle initialization code, register entities that should be controllable:

```csharp
// Register an entity as controllable (and optionally set it as the initially controlled entity)
EntityControlManager.RegisterControllableEntity(myEntity, setAsActive: true);
```

### 4. Add Input Actions for Entity Switching (Optional)

If you want to allow the player to switch between controllable entities, add these input actions to your project:

- "switch_entity_next" - Switch to the next controllable entity
- "switch_entity_prev" - Switch to the previous controllable entity

Then call `EntityControlManager.ProcessInput()` in your game update loop to process these inputs:

```csharp
public override void _Process(double delta)
{
    // Process entity switching input
    EntityControlManager.ProcessInput();
    
    // Rest of your game logic
}
```

## Optional: Add AI Control

To add simple AI control to entities when they're not being controlled by the player:

1. Add a new Node as a child of your entity
2. Select "SimpleAIControlComponent" as the node type
3. Name it "SimpleAIControlComponent"
4. Configure its properties in the Inspector

The system is designed to automatically activate AI control when player control is released, and deactivate AI control when player control is gained.

## Events

The new system provides several events you can subscribe to:

```csharp
// Subscribe to player control events
PlayerControlComponent.PlayerControlGained += OnPlayerControlGained;
PlayerControlComponent.PlayerControlLost += OnPlayerControlLost;

// Subscribe to entity control switching events
EntityControlManager.ControlSwitched += OnControlSwitched;

// Subscribe to global battle events
BattleEvents.PlayerControlGiven += OnPlayerControlGiven;
BattleEvents.PlayerControlRemoved += OnPlayerControlRemoved;
BattleEvents.PlayerControlSwitched += OnPlayerControlSwitched;
```

## Example: Converting a Player Ship

Here's an example of how to convert a player ship to use the new system:

1. Change the class inheritance from `PlayerControllableEntity` to `BattleEntity`
2. Add a `PlayerControlComponent` to the ship in the scene editor
3. Register the ship with the `EntityControlManager` when the battle starts
4. Optionally add a `SimpleAIControlComponent` for when the player isn't controlling it

## Creating Custom Control Components

You can create custom control components by inheriting from `EntityControlComponent`:

```csharp
public partial class MyCustomControlComponent : EntityControlComponent
{
    // Override the ProcessControl method to implement your control logic
    protected override void ProcessControl(double delta)
    {
        // Your custom control logic here
        // Use _entity to access the parent entity
        _entity.SetThrustInput(0.5f); // Example
        _entity.SetRotationInput(0.2f); // Example
    }
    
    // Optionally override Activate and Deactivate for custom activation/deactivation logic
    public override void Activate()
    {
        base.Activate();
        // Your custom activation logic here
    }
}
```

This new system allows for much more flexibility, as any battle entity can now be player-controllable without requiring a specific inheritance hierarchy, and you can create custom control components for different types of AI or special control modes.
