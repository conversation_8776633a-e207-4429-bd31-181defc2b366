using System;
using Godot;
using System.Text.Json;
using SpaceGame.Scripts.Battle.State;
using SpaceGame.Utility.Interfaces;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Battle.Data
{
    [GlobalClass]
    public partial class BaseBattleEntityData : Resource, IJsonPopulatable
    {
        /// <summary>
        /// Gets or sets the unique identifier for this entity instance.
        /// </summary>
        [Export]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Gets or sets the display name of this entity.
        /// </summary>
        [Export]
        public string Name { get; set; } = "Battle Entity";
        
        /// <summary>
        /// Gets or sets the maximum health of this entity.
        /// </summary>
        [Export]
        public float MaxHealth { get; set; } = 100f;
        
        
        /// <summary>
        /// Gets or sets the base category of this entity (e.g., Ship, Projectile).
        /// </summary>
        [Export]
        public BaseEntityType BaseType { get; set; } = BaseEntityType.Ship;
        
        /// <summary>
        /// Default constructor.
        /// </summary>
        public BaseBattleEntityData()
        {
        }
        
        /// <summary>
        /// Copy constructor.
        /// </summary>
        /// <param name="other">The entity data to copy from.</param>
        public BaseBattleEntityData(BaseBattleEntityData other)
        {
            if (other == null)
                return;
                
            Id = other.Id;
            Name = other.Name;
            MaxHealth = other.MaxHealth;
            BaseType = other.BaseType;
        }
        
        /// <summary>
        /// Creates a clone of this entity data.
        /// </summary>
        /// <param name="generateNewId">Whether to generate a new ID for the clone.</param>
        /// <returns>A new instance with the same properties.</returns>
        public virtual BaseBattleEntityData Clone(bool generateNewId = true)
        {
            BaseBattleEntityData clone = new BaseBattleEntityData(this);
            
            if (generateNewId)
            {
                clone.Id = Guid.NewGuid().ToString();
            }

            return clone;
        }

        /// <summary>
        /// Applies state from a BattleEntityStateData object to this instance.
        /// </summary>
        /// <param name="stateData">The state data to apply.</param>
        public virtual void ApplyState(BattleEntityStateData stateData)
        {
            if (stateData == null)
            {
                GD.PrintErr("Attempted to apply null state data to BaseBattleEntityData.");
                return;
            }

            Id = stateData.Id;
            Name = stateData.Name;
            MaxHealth = stateData.MaxHealth;
            BaseType = stateData.BaseType;
        }
        
        /// <summary>
        /// Populates this entity's properties from JSON data.
        /// </summary>
        /// <param name="jsonOverrides">The JSON element containing property overrides</param>
        /// <param name="serializerOptions">Options for JSON serialization/deserialization</param>
        public virtual void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)
        {
            try
            {
                // Handle simple properties
                foreach (var property in jsonOverrides.EnumerateObject())
                {
                    string propertyName = property.Name;
                    JsonElement propertyValue = property.Value;
                    
                    string propNameLower = propertyName.ToLowerInvariant();
                    
                    // Using nameof for property names to make them refactoring-safe
                    if (propNameLower == nameof(Id).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String)
                            Id = propertyValue.GetString();
                    }
                    else if (propNameLower == nameof(Name).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String)
                            Name = propertyValue.GetString();
                    }
                    else if (propNameLower == nameof(MaxHealth).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            MaxHealth = propertyValue.GetSingle();
                    }

                    else if (propNameLower == nameof(BaseType).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            BaseType = JsonSerializer.Deserialize<BaseEntityType>(propertyValue.GetRawText(), serializerOptions);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error populating BaseBattleEntityData from JSON: {ex.Message}", ex);
            }
        }
    }
}
