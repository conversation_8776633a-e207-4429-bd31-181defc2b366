using System;
using System.Collections.Generic;
using Godot;
using System.Text.Json;
using SpaceGame.Scripts.Battle.State;
using SpaceGame.Scripts.Ships.Data;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Battle.Data
{
    /// <summary>
    /// Serializable data class for ship entities.
    /// Contains ship-specific properties and serialization support.
    /// </summary>
    [GlobalClass]
    public partial class ShipEntityData : BaseBattleEntityData
    {
        /// <summary>
        /// Gets or sets the maximum movement speed of this ship in units per second.
        /// </summary>
        [ExportGroup("Movement Physics")]
        [Export]
        public float MaxSpeed { get; set; } = 100.0f;
        
        /// <summary>
        /// Gets or sets the acceleration rate of this ship in units per second^2.
        /// </summary>
        [Export]
        public float Acceleration { get; set; } = 200.0f;
        
        /// <summary>
        /// Gets or sets the deceleration rate of this ship in units per second^2 (for stopping).
        /// </summary>
        [Export]
        public float Deceleration { get; set; } = 300.0f;
        
        /// <summary>
        /// Gets or sets the drag factor affecting the ship. Higher values mean more drag.
        /// Applied each physics frame: velocity *= (1 - drag * delta). Range typically 0-5.
        /// </summary>
        [Export(PropertyHint.Range, "0.0,5.0,0.01")]
        public float DragFactor { get; set; } = 0.8f;

        /// <summary>
        /// Gets or sets the base rotation speed of this ship in degrees per second.
        /// </summary>
        [Export]
        public float RotationSpeed { get; set; } = 180.0f;
        
        /// <summary>
        /// Gets or sets the distance to the target to consider the ship 'arrived'.
        /// </summary>
        [ExportGroup("Movement Thresholds")]
        [Export(PropertyHint.Range, "0.1,50.0,0.1")] 
        public float MovementThreshold { get; set; } = 2.0f;
        
        /// <summary>
        /// Gets or sets the angle (in degrees) to the target rotation to consider the ship 'aligned'.
        /// </summary>
        [Export(PropertyHint.Range, "0.1,10.0,0.1")]
        public float RotationThreshold { get; set; } = 1.0f;
        
        /// <summary>
        /// Gets or sets the distance at which the ship starts to slow down when approaching a target.
        /// </summary>
        [Export(PropertyHint.Range, "10.0,500.0,1.0")]
        public float SlowingDistance { get; set; } = 50.0f;
        
        /// <summary>
        /// Gets or sets the specific type of ship if BaseType is Ship (e.g., Fighter, Scout).
        /// </summary>
        [Export]
        public ShipType ShipDetailType { get; set; } = ShipType.Fighter;
        
        /// <summary>
        /// Gets or sets the weapon loadout for this ship.
        /// Each element defines a weapon to be mounted at a specific point.
        /// </summary>
        [ExportGroup("Armament")]
        [Export]
        public Godot.Collections.Array<WeaponSlotData> WeaponSlots { get; set; } = new();
        
        /// <summary>
        /// Gets or sets the default behavior configuration for this ship when spawned as an enemy.
        /// </summary>
        [ExportGroup("AI Behavior")]
        [Export]
        public SpaceGame.Battle.AI.EnemyBehaviorConfiguration DefaultBehavior { get; set; }
        
        /// <summary>
        /// Gets or sets the maximum shield strength of this ship.
        /// </summary>
        [ExportGroup("Combat Properties")]
        [Export]
        public float MaxShields { get; set; } = 50.0f;
        
        /// <summary>
        /// Gets or sets the shield regeneration rate per second.
        /// </summary>
        [Export]
        public float ShieldRegenRate { get; set; } = 2.0f;
        
        /// <summary>
        /// Gets or sets the shield damage resistance factor (1.0 = normal damage, 0.5 = half damage).
        /// </summary>
        [Export(PropertyHint.Range, "0.1,2.0,0.1")]
        public float ShieldResistance { get; set; } = 1.0f;
        
        /// <summary>
        /// Default constructor.
        /// </summary>
        public ShipEntityData()
        {
            BaseType = BaseEntityType.Ship;
        }
        
        /// <summary>
        /// Copy constructor.
        /// </summary>
        /// <param name="other">The entity data to copy from.</param>
        public ShipEntityData(ShipEntityData other) : base(other)
        {
            if (other == null)
                return;
                
            MaxSpeed = other.MaxSpeed;
            RotationSpeed = other.RotationSpeed;
            Acceleration = other.Acceleration;
            Deceleration = other.Deceleration;
            MaxShields = other.MaxShields;
            ShieldRegenRate = other.ShieldRegenRate;
            ShieldResistance = other.ShieldResistance;
            DragFactor = other.DragFactor;
            MovementThreshold = other.MovementThreshold;
            RotationThreshold = other.RotationThreshold;
            SlowingDistance = other.SlowingDistance;
            ShipDetailType = other.ShipDetailType;
            
            // Copy weapon slots
            WeaponSlots = new Godot.Collections.Array<WeaponSlotData>();
            foreach (var slot in other.WeaponSlots)
            {
                WeaponSlots.Add(slot.Duplicate() as WeaponSlotData);
            }
        }
        
        /// <summary>
        /// Creates a clone of this entity data.
        /// </summary>
        /// <param name="generateNewId">Whether to generate a new ID for the clone.</param>
        /// <returns>A new instance with the same properties.</returns>
        public override BaseBattleEntityData Clone(bool generateNewId = true)
        {
            ShipEntityData clone = new ShipEntityData(this);
            
            if (generateNewId)
            {
                clone.Id = Guid.NewGuid().ToString();
            }

            return clone;
        }

        /// <summary>
        /// Applies state from a BattleEntityStateData object to this instance.
        /// </summary>
        /// <param name="stateData">The state data to apply.</param>
        public override void ApplyState(BattleEntityStateData stateData)
        {
            base.ApplyState(stateData);

            if (stateData == null)
                return;

            MaxSpeed = stateData.MaxSpeed;
            RotationSpeed = stateData.RotationSpeed;
            Acceleration = stateData.Acceleration;
            Deceleration = stateData.Deceleration;
            DragFactor = stateData.DragFactor;
            MovementThreshold = stateData.MovementThreshold;
            RotationThreshold = stateData.RotationThreshold;
            SlowingDistance = stateData.SlowingDistance;
            ShipDetailType = stateData.ShipDetailType;
            
            // NOTE: Weapon slots are not part of the runtime state data, as they are defined in the entity template
        }
        
        /// <summary>
        /// Populates this ship entity's properties from JSON data.
        /// </summary>
        /// <param name="jsonOverrides">The JSON element containing property overrides</param>
        /// <param name="serializerOptions">Options for JSON serialization/deserialization</param>
        public override void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)
        {
            // First apply base class properties
            base.PopulateFromJson(jsonOverrides, serializerOptions);
            
            try
            {
                // Handle ship-specific properties
                foreach (var property in jsonOverrides.EnumerateObject())
                {
                    string propertyName = property.Name;
                    JsonElement propertyValue = property.Value;
                    
                    string propNameLower = propertyName.ToLowerInvariant();
                    
                    // Using nameof for property names to make them refactoring-safe
                    if (propNameLower == nameof(MaxSpeed).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            MaxSpeed = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(RotationSpeed).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            RotationSpeed = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(Acceleration).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            Acceleration = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(Deceleration).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            Deceleration = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(DragFactor).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            DragFactor = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(MovementThreshold).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            MovementThreshold = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(RotationThreshold).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            RotationThreshold = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(SlowingDistance).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            SlowingDistance = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(ShipDetailType).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            ShipDetailType = JsonSerializer.Deserialize<ShipType>(propertyValue.GetRawText(), serializerOptions);
                    }
                    else if (propNameLower == nameof(MaxShields).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            MaxShields = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(ShieldRegenRate).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            ShieldRegenRate = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(ShieldResistance).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            ShieldResistance = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(WeaponSlots).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Array)
                            {
                                // Handle weapon slots array
                                HandleWeaponSlotsOverrides(propertyValue, serializerOptions);
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error populating ShipEntityData from JSON: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// Handles overrides for the WeaponSlots collection.
        /// </summary>
        private void HandleWeaponSlotsOverrides(JsonElement weaponSlotsJson, JsonSerializerOptions serializerOptions)
        {
            try
            {
                int i = 0;
                foreach (var slotJson in weaponSlotsJson.EnumerateArray())
                {
                    // If we have an existing slot at this index, update it
                    if (i < WeaponSlots.Count)
                    {
                        var existingSlot = WeaponSlots[i];
                        if (existingSlot is SpaceGame.Utility.Interfaces.IJsonPopulatable jsonPopulatable)
                        {
                            // Use IJsonPopulatable interface if available
                            jsonPopulatable.PopulateFromJson(slotJson, serializerOptions);
                        }
                        else
                        {
                            // Otherwise use PropertyOverrideServiceV2
                            var slotOverrides = JsonSerializer.Deserialize<Dictionary<string, object>>(slotJson.GetRawText(), serializerOptions);
                            if (slotOverrides != null)
                            {
                                SpaceGame.Utility.PropertyOverrideServiceV2.ApplyOverrides(existingSlot, slotOverrides, serializerOptions);
                            }
                        }
                    }
                    // Otherwise, create a new slot and add it
                    else
                    {
                        try
                        {
                            // Try to deserialize directly to WeaponSlotData
                            var newSlot = JsonSerializer.Deserialize<WeaponSlotData>(slotJson.GetRawText(), serializerOptions);
                            if (newSlot != null)
                            {
                                WeaponSlots.Add(newSlot);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Warning($"Could not deserialize new weapon slot: {ex.Message}");
                        }
                    }
                    i++;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error handling weapon slots overrides: {ex.Message}", ex);
            }
        }
    }
}
