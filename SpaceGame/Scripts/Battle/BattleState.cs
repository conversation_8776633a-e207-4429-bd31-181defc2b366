using System;
using System.Collections.Generic;
using Godot;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Represents the state of a battle.
/// </summary>
public class BattleState
{
    /// <summary>
    /// The unique identifier for this battle.
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Whether the battle is completed.
    /// </summary>
    public bool IsCompleted { get; set; } = false;

    /// <summary>
    /// Whether the player won the battle.
    /// </summary>
    public bool PlayerVictory { get; set; } = false;

    /// <summary>
    /// The positions of entities in the battle.
    /// </summary>
    public Dictionary<string, Vector2> EntityPositions { get; set; } =
        new Dictionary<string, Vector2>();

    /// <summary>
    /// The health of entities in the battle.
    /// </summary>
    public Dictionary<string, float> EntityHealth { get; set; } = new Dictionary<string, float>();

    /// <summary>
    /// The timestamp when the battle was created.
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// The timestamp when the battle was last updated.
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}
