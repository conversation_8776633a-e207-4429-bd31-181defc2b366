using Godot;
using SpaceGame.Helpers;
using SpaceGame.Battle.Registry;
using System.Collections.Generic;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Factory for creating battle entities.
/// Handles instantiation and configuration of battle entities using the BattleEntityRegistry.
/// </summary>
public class BattleEntityFactory
{
    private static BattleEntityFactory? _instance;
    public static BattleEntityFactory Instance => _instance ??= new BattleEntityFactory();

    /// <summary>
    /// Dictionary of cached instances of WeaponData.
    /// </summary>
    private readonly Dictionary<string, PackedScene> _cachedWeaponInstances = new();

    private BattleEntityRegistry _battleEntityRegistry => BattleEntityRegistry.Instance;

    /// <summary>
    /// Creates a projectile entity.
    /// </summary>
    /// <param name="projectileId">The registry ID of the projectile to create.</param>
    /// <param name="position">The initial position of the projectile.</param>
    /// <param name="rotation">The initial rotation of the projectile.</param>
    /// <param name="velocity">The initial velocity of the projectile (currently not used by CreateBattleEntityById but kept for signature compatibility if needed later by projectile-specific logic).</param>
    /// <param name="allegiance">The allegiance of the projectile.</param>
    /// <returns>The created projectile as a ProjectileEntity, or null on failure.</returns>
    public ProjectileEntity? CreateProjectile(string projectileId, Vector2 position, float rotation, Vector2 velocity, Allegiance allegiance = Allegiance.NeutralForces)
    {
        if (string.IsNullOrEmpty(projectileId))
        {
            Logger.Error("BattleEntityFactory: projectileId is null or empty. Cannot create projectile.");
            return null;
        }

        BaseBattleEntity? instanceNode = CreateBattleEntityById(projectileId, position, rotation, allegiance);
        if (instanceNode is ProjectileEntity projectileEntity) 
        {
            // Set velocity on the projectile if needed
            projectileEntity.SetVelocity(velocity);
            Logger.Debug($"Created projectile (ID: '{projectileId}') at {position} with rotation {rotation} and allegiance {allegiance}.");
            return projectileEntity;
        }
        else
        {
            Logger.Error($"BattleEntityFactory: Failed to create projectile with ID '{projectileId}' or entity is not a ProjectileEntity.");
            return null;
        }
    }

    /// <summary>
    /// Spawns a weapon based on its ID from the registry.
    /// </summary>
    /// <param name="weaponId">The ID of the weapon to spawn.</param>
    /// <returns>The instantiated weapon node, or null if not found or on error.</returns>
    public Node? SpawnWeapon(string weaponId)
    {
        if (string.IsNullOrEmpty(weaponId))
        {
            Logger.Error("BattleEntityFactory: weaponId is null or empty. Cannot spawn weapon.");
            return null;
        }

        // Try to get the WeaponData resource
        var weaponData = _battleEntityRegistry.GetWeaponData(weaponId);
        if (weaponData == null)
        {
            Logger.Error($"BattleEntityFactory: WeaponData with ID '{weaponId}' not found in registry.");
            return null;
        }

        // Check if we have a WeaponScene to instantiate
        if (weaponData.WeaponScene == null)
        {
            Logger.Error($"BattleEntityFactory: WeaponData with ID '{weaponId}' has a null WeaponScene.");
            return null;
        }

        // Instantiate the weapon scene
        Node weaponInstance = weaponData.WeaponScene.Instantiate();
        if (weaponInstance == null)
        {
            Logger.Error($"BattleEntityFactory: Failed to instantiate WeaponScene for weapon ID '{weaponId}'.");
            return null;
        }

        Logger.Debug($"BattleEntityFactory: Successfully spawned weapon '{weaponId}'.");
        return weaponInstance;
    }

    public ProjectileEntity? SpawnProjectileFromWeaponData(WeaponData weaponData, BaseBattleEntity sourceEntity, Transform2D spawnTransform, Node? parentNode = null)
    {
        if (weaponData == null)
        {
            Logger.Error("BattleEntityFactory: weaponData is null. Cannot spawn projectile.");
            return null;
        }

        if (weaponData.ProjectileScene == null)
        {
            Logger.Error($"BattleEntityFactory: ProjectileScene is null for weapon '{weaponData.Id}'. Cannot spawn projectile.");
            return null;
        }
        
        if (weaponData.ProjectileDefinition == null)
        {
            Logger.Error($"BattleEntityFactory: ProjectileDefinition is null for weapon '{weaponData.Id}'. Cannot spawn projectile.");
            return null;
        }

        ProjectileEntity? projectile = weaponData.ProjectileScene.InstantiateOrNull<ProjectileEntity>();
        if (projectile == null)
        {
            Logger.Error($"BattleEntityFactory: Failed to instantiate projectile from weapon '{weaponData.Id}'.");
            return null;
        }

        projectile.GlobalTransform = spawnTransform;
        // Set velocity directly using the spawn transform's X axis (forward direction)
        Vector2 direction = -spawnTransform.Y.Normalized(); // Upwards
        projectile.SetVelocity(direction * weaponData.ProjectileDefinition.Speed);
        projectile.Initialize(weaponData.ProjectileDefinition, sourceEntity);
        
        // Configure behaviors if any are defined in the weapon data
        ConfigureProjectileBehaviors(projectile, weaponData, sourceEntity);

        Node? actualParent = parentNode;
        if (actualParent == null)
        {
            if (sourceEntity.IsInsideTree() && sourceEntity.GetTree() != null && sourceEntity.GetTree().CurrentScene != null)
            {
                // Try to find a dedicated 'Projectiles' node, otherwise use current scene root.
                actualParent = sourceEntity.GetTree().CurrentScene.GetNode("ProjectilesLayer") ?? sourceEntity.GetTree().CurrentScene;
            }
        }

        if (actualParent != null)
        {
            actualParent.AddChild(projectile, true);
        }
        else
        {
            Logger.Error($"BattleEntityFactory: Could not add projectile from weapon '{weaponData.Id}' to scene tree. No suitable parent found.");
            projectile.QueueFree();
            return null;
        }

        return projectile;
    }

    /// <summary>
    /// Clears the scene caches.
    /// </summary>
    public void ClearCaches()
    {
        _cachedWeaponInstances.Clear();
        Logger.Debug("Cleared weapon scene cache. Entity scenes are managed by BattleEntityRegistry.");
    }

    /// <summary>
    /// Creates a battle entity from a specific ID using the BattleEntityRegistry.
    /// </summary>
    public BaseBattleEntity? CreateBattleEntityById(string entityId, Vector2 position, float rotation, Allegiance allegiance)
    {
        if (string.IsNullOrEmpty(entityId))
        {
            Logger.Error("BattleEntityFactory: Cannot create entity with null or empty entityId.");
            return null;
        }

        PackedScene? scene = _battleEntityRegistry.GetShipEntityScene(entityId);
        if (scene == null)
        {
            return null;
        }

        // Try to instantiate as BaseBattleEntity first
        BaseBattleEntity? instance = scene.InstantiateOrNull<BaseBattleEntity>();

        if (instance != null)
        {
            // Check for specific entity types
            if (instance is ShipEntity shipEntity)
            {
                shipEntity.SetRegistryId(entityId);
            }

            instance.GlobalPosition = position;
            instance.Rotation = rotation;
            instance.CurrentAllegiance = allegiance;
            Logger.Debug($"BattleEntityFactory: Successfully instantiated entity '{entityId}' at {position}.");
            return instance;
        }

        return null;
    }

    private void ConfigureEntityFromData(BaseBattleEntity entity, BaseBattleEntityData data)
    {
        if (entity == null || data == null)
        {
            Logger.Error("BattleEntityFactory: Cannot configure entity. Entity or data is null.");
            return;
        }

        entity.Name = data.Name; // Name is usually a Node property
        
        // Handle specific entity types
        if (entity is ShipEntity shipEntity && data is ShipEntityData shipData)
        {
            shipEntity.SetEntityData(shipData);
            Logger.Debug($"Configured ship entity '{entity.Name}' using ship data '{data.ResourceName}'.");
        }
        else if (entity is ProjectileEntity projectileEntity && data is ProjectileEntityData projectileData)
        {
            projectileEntity.Initialize(projectileData, null);
            Logger.Debug($"Configured projectile entity '{entity.Name}' using projectile data.");
        }
        else
        {
            Logger.Warning($"ConfigureEntityFromData: Entity type {entity.GetType().Name} or data type {data.GetType().Name} mismatch. Some configuration may not be applied.");
        }
    }
    
    /// <summary>
    /// Configures behaviors for a projectile based on weapon data.
    /// </summary>
    /// <param name="projectile">The projectile to configure</param>
    /// <param name="weaponData">The weapon data containing behavior settings</param>
    /// <param name="sourceEntity">The entity that fired the projectile</param>
    private void ConfigureProjectileBehaviors(ProjectileEntity projectile, WeaponData weaponData, BaseBattleEntity sourceEntity)
    {
        // Apply any behavior settings from the weapon data to existing behaviors
        var behaviorsNode = projectile.GetNodeOrNull<Node>("Behaviors");
        if (behaviorsNode != null && weaponData.BehaviorSettings.Count > 0)
        {
            foreach (var child in behaviorsNode.GetChildren())
            {
                if (child is Entities.Behaviors.ProjectileBehavior behavior)
                {
                    // Configure behavior based on weapon data settings if needed
                    // Example: if (behavior is Entities.Behaviors.HomingBehavior homingBehavior) { ... }
                }
            }
            
            Logger.Debug($"Applied behavior settings to projectile from weapon '{weaponData.Id}'.");
        }
    }
    
    /// <summary>
    /// Adds a homing behavior to a projectile and sets its target.
    /// </summary>
    /// <param name="projectile">The projectile to add homing behavior to</param>
    /// <param name="target">The target to home in on</param>
    /// <param name="turningRate">How quickly the projectile can turn (radians/sec)</param>
    /// <param name="maxTrackingRange">Maximum distance to track target (0 for unlimited)</param>
    /// <returns>The added HomingBehavior, or null if failed</returns>
    public Entities.Behaviors.HomingBehavior? AddHomingBehaviorToProjectile(ProjectileEntity projectile, Node2D target, float turningRate = 2.0f, float maxTrackingRange = 0.0f)
    {
        if (projectile == null || target == null)
        {
            Logger.Error("BattleEntityFactory: Cannot add homing behavior. Projectile or target is null.");
            return null;
        }
        
        // Get or create the Behaviors node
        Node behaviorsNode = projectile.GetNodeOrNull<Node>("Behaviors");
        if (behaviorsNode == null)
        {
            behaviorsNode = new Node { Name = "Behaviors" };
            projectile.AddChild(behaviorsNode);
            Logger.Debug($"Created Behaviors node for projectile {projectile.Name}.");
        }
        
        var homingBehavior = new Entities.Behaviors.HomingBehavior
        {
            Target = target,
            TurningRate = turningRate,
            MaxTrackingRange = maxTrackingRange
        };
        
        behaviorsNode.AddChild(homingBehavior);
        Logger.Debug($"Added HomingBehavior to projectile {projectile.Name} targeting {target.Name}.");
        
        return homingBehavior;
    }
}

