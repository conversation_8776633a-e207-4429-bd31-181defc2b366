using Godot;
using System;
using System.Collections.Generic;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Components;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Events;

namespace SpaceGame.Scripts.Battle.Controllers;

/// <summary>
/// Manages player control of battle entities, allowing the player to switch between controllable entities.
/// Static utility class that doesn't require being part of the scene tree.
/// </summary>
public static class EntityControlManager
{
    /// <summary>
    /// The currently player-controlled entity.
    /// </summary>
    private static ShipEntity _currentControlledEntity;

    /// <summary>
    /// List of all entities that have a PlayerControlComponent attached.
    /// </summary>
    private static readonly List<ShipEntity> _controllableEntities = new();

    /// <summary>
    /// Static constructor to initialize the EntityControlManager.
    /// </summary>
    static EntityControlManager()
    {
        // Subscribe to player control events
        PlayerControlComponent.PlayerControlGained += OnPlayerControlGained;
        PlayerControlComponent.PlayerControlLost += OnPlayerControlLost;
        
        Logger.Debug("EntityControlManager initialized");
    }
    
    /// <summary>
    /// Initializes the EntityControlManager for a new battle.
    /// Call this when starting a new battle to reset the state.
    /// </summary>
    public static void Initialize()
    {
        // Clear the current controlled entity and controllable entities list
        _currentControlledEntity = null;
        _controllableEntities.Clear();
        
        Logger.Debug("EntityControlManager state reset for new battle");
    }
    
    /// <summary>
    /// Processes input for entity switching. Call this from a game controller or battle scene.
    /// </summary>
    public static void ProcessInput()
    {
        // Check for entity switching input
        if (Input.IsActionJustPressed("switch_entity_next"))
        {
            SwitchToNextEntity();
        }
        else if (Input.IsActionJustPressed("switch_entity_prev"))
        {
            SwitchToPreviousEntity();
        }
    }

    /// <summary>
    /// Registers a controllable entity with the manager.
    /// </summary>
    /// <param name="entity">The entity to register.</param>
    /// <param name="setAsActive">Whether to immediately give this entity player control.</param>
    public static void RegisterControllableEntity(ShipEntity entity, bool setAsActive = false)
    {
        if (entity == null)
            return;

        // Check if the entity has a PlayerControlComponent
        var controlComponent = entity.GetNodeOrNull<PlayerControlComponent>("PlayerControlComponent");
        if (controlComponent == null)
        {
            // Try to find any PlayerControlComponent in the entity's children
            controlComponent = entity.FindChild("*", false, false) as PlayerControlComponent;
            
            if (controlComponent == null)
            {
                Logger.Warning($"Cannot register entity {entity.Name} as controllable: No PlayerControlComponent found");
                return;
            }
        }

        // Add to the list if not already present
        if (!_controllableEntities.Contains(entity))
        {
            _controllableEntities.Add(entity);
            Logger.Debug($"Registered entity {entity.Name} as controllable");
        }

        // Set as active if requested
        if (setAsActive)
        {
            GiveControlTo(entity);
        }
    }

    /// <summary>
    /// Unregisters a controllable entity from the manager.
    /// </summary>
    /// <param name="entity">The entity to unregister.</param>
    public static void UnregisterControllableEntity(ShipEntity entity)
    {
        if (entity == null)
            return;

        // Remove from the list
        if (_controllableEntities.Contains(entity))
        {
            _controllableEntities.Remove(entity);
            Logger.Debug($"Unregistered entity {entity.Name} from controllable entities");

            // If this was the currently controlled entity, switch to another one
            if (_currentControlledEntity == entity)
            {
                _currentControlledEntity = null;
                
                // Try to switch to another entity if available
                if (_controllableEntities.Count > 0)
                {
                    GiveControlTo(_controllableEntities[0]);
                }
            }
        }
    }

    /// <summary>
    /// Gives player control to the specified entity.
    /// </summary>
    /// <param name="entity">The entity to give control to.</param>
    public static void GiveControlTo(ShipEntity entity)
    {
        if (entity == null)
            return;

        // Check if the entity has a PlayerControlComponent
        var controlComponent = entity.GetNodeOrNull<PlayerControlComponent>("PlayerControlComponent");
        if (controlComponent == null)
        {
            // Try to find any PlayerControlComponent in the entity's children
            controlComponent = entity.FindChild("*", false, false) as PlayerControlComponent;
            
            if (controlComponent == null)
            {
                Logger.Warning($"Cannot give control to entity {entity.Name}: No PlayerControlComponent found");
                return;
            }
        }

        // Remove control from current entity
        if (_currentControlledEntity != null && _currentControlledEntity != entity)
        {
            // First try to get the component by exact name
            var currentControlComponent = _currentControlledEntity.GetNodeOrNull<PlayerControlComponent>("PlayerControlComponent");
            
            // If not found, try to find any PlayerControlComponent in the entity's children
            if (currentControlComponent == null)
            {
                currentControlComponent = _currentControlledEntity.FindChild("*", false, false) as PlayerControlComponent;
            }
            
            if (currentControlComponent != null)
            {
                currentControlComponent.RemovePlayerControl();
            }
        }

        // Give control to new entity
        controlComponent.GivePlayerControl();
        _currentControlledEntity = entity;
        
        Logger.Debug($"Player control given to {entity.Name}");
        
        // No need to raise BattleEvents.RaisePlayerControlGiven here as it will be raised by the PlayerControlComponent
    }

    /// <summary>
    /// Switches player control to the next entity in the list.
    /// </summary>
    public static void SwitchToNextEntity()
    {
        if (_controllableEntities.Count <= 1)
            return;

        int currentIndex = _currentControlledEntity != null ? _controllableEntities.IndexOf(_currentControlledEntity) : -1;
        int nextIndex = (currentIndex + 1) % _controllableEntities.Count;
        
        GiveControlTo(_controllableEntities[nextIndex]);
    }

    /// <summary>
    /// Switches player control to the previous entity in the list.
    /// </summary>
    public static void SwitchToPreviousEntity()
    {
        if (_controllableEntities.Count <= 1)
            return;

        int currentIndex = _currentControlledEntity != null ? _controllableEntities.IndexOf(_currentControlledEntity) : 0;
        int prevIndex = (currentIndex - 1 + _controllableEntities.Count) % _controllableEntities.Count;
        
        GiveControlTo(_controllableEntities[prevIndex]);
    }

    /// <summary>
    /// Gets the currently player-controlled entity.
    /// </summary>
    /// <returns>The currently player-controlled entity, or null if none.</returns>
    public static ShipEntity GetCurrentControlledEntity()
    {
        return _currentControlledEntity;
    }

    /// <summary>
    /// Gets all registered controllable entities.
    /// </summary>
    /// <returns>A list of all controllable entities.</returns>
    public static IReadOnlyList<ShipEntity> GetControllableEntities()
    {
        return _controllableEntities;
    }

    /// <summary>
    /// Called when an entity gains player control.
    /// </summary>
    private static void OnPlayerControlGained(object sender, PlayerControlEventArgs args)
    {
        ShipEntity entity = args.Entity as ShipEntity;
        if (entity == null)
        {
            Logger.Warning("Received control event for a non-ShipEntity. Ignoring.");
            return;
        }
        
        // Update current controlled entity
        if (entity != _currentControlledEntity)
        {
            ShipEntity previousEntity = _currentControlledEntity;
            _currentControlledEntity = entity;
            
            // Trigger the global event
            BattleEvents.RaisePlayerControlSwitched(previousEntity, entity);
            
            // Deactivate other control components on this entity
            DeactivateOtherControlComponents(entity);
        }
        
        // Make sure the entity is in our list
        if (!_controllableEntities.Contains(entity))
        {
            _controllableEntities.Add(entity);
        }
    }

    /// <summary>
    /// Called when an entity loses player control.
    /// </summary>
    private static void OnPlayerControlLost(object sender, PlayerControlEventArgs args)
    {
        // If this was the currently controlled entity, clear the reference
        if (args.Entity == _currentControlledEntity)
        {
            _currentControlledEntity = null;
            
            // Activate AI control component if available, but only for ShipEntity types
            if (args.Entity is ShipEntity shipEntity)
            {
                ActivateAIControlComponent(shipEntity);
            }
            else
            {
                Logger.Warning("Cannot activate AI control component for non-ShipEntity");
            }
        }
    }
    
    /// <summary>
    /// Deactivates all other control components on an entity except the PlayerControlComponent.
    /// </summary>
    /// <param name="entity">The entity to process.</param>
    private static void DeactivateOtherControlComponents(ShipEntity entity)
    {
        if (entity == null)
            return;
            
        // Find all EntityControlComponent children that are not PlayerControlComponent
        foreach (var child in entity.GetChildren())
        {
            if (child is EntityControlComponent controlComponent && !(child is PlayerControlComponent))
            {
                controlComponent.Deactivate();
            }
        }
    }
    
    /// <summary>
    /// Activates the first AI control component found on an entity.
    /// </summary>
    /// <param name="entity">The entity to process.</param>
    private static void ActivateAIControlComponent(ShipEntity entity)
    {
        if (entity == null)
            return;
            
        // Find the first AI control component (any EntityControlComponent that is not PlayerControlComponent)
        foreach (var child in entity.GetChildren())
        {
            if (child is EntityControlComponent controlComponent && !(child is PlayerControlComponent))
            {
                controlComponent.Activate();
                Logger.Debug($"Activated AI control for {entity.Name}");
                break; // Only activate the first one found
            }
        }
    }
}

/// <summary>
/// Event arguments for entity control switched events.
/// </summary>
public class EntityControlSwitchedEventArgs : EventArgs
{
    /// <summary>
    /// The entity that previously had player control.
    /// </summary>
    public ShipEntity PreviousEntity { get; }
    
    /// <summary>
    /// The entity that now has player control.
    /// </summary>
    public ShipEntity CurrentEntity { get; }

    /// <summary>
    /// Creates a new instance of EntityControlSwitchedEventArgs.
    /// </summary>
    /// <param name="previousEntity">The entity that previously had player control.</param>
    /// <param name="currentEntity">The entity that now has player control.</param>
    public EntityControlSwitchedEventArgs(ShipEntity previousEntity, ShipEntity currentEntity)
    {
        PreviousEntity = previousEntity;
        CurrentEntity = currentEntity;
    }
}
