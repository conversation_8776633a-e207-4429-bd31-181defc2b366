using System;
using Godot;
using SpaceGame.Scripts.Battle.State;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Serializable data class for battle entities.
/// Contains base properties and serialization support.
/// </summary>
[GlobalClass]
public partial class BattleEntityData : Godot.Resource
{
    /// <summary>
    /// Gets or sets the unique identifier for this entity instance.
    /// </summary>
    [Export]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Gets or sets the display name of this entity.
    /// </summary>
    [Export]
    public string Name { get; set; } = "Battle Entity";
    
    /// <summary>
    /// Gets or sets the maximum health of this entity.
    /// </summary>
    [Export]
    public float MaxHealth { get; set; } = 100f;
    
    /// <summary>
    /// Gets or sets the maximum movement speed of this entity in units per second.
    /// </summary>
    [ExportGroup("Movement Physics")]
    [Export]
    public float MaxSpeed { get; set; } = 100.0f;
    
    /// <summary>
    /// Gets or sets the acceleration rate of this entity in units per second^2.
    /// </summary>
    [Export]
    public float Acceleration { get; set; } = 200.0f;
    
    /// <summary>
    /// Gets or sets the deceleration rate of this entity in units per second^2 (for stopping).
    /// </summary>
    [Export]
    public float Deceleration { get; set; } = 300.0f;
    
    /// <summary>
    /// Gets or sets the drag factor affecting the entity. Higher values mean more drag.
    /// Applied each physics frame: velocity *= (1 - drag * delta). Range typically 0-5.
    /// </summary>
    [Export(PropertyHint.Range, "0.0,5.0,0.01")]
    public float DragFactor { get; set; } = 0.8f;

    /// <summary>
    /// Gets or sets the base rotation speed of this entity in degrees per second.
    /// </summary>
    [Export]
    public float RotationSpeed { get; set; } = 180.0f;
    
    /// <summary>
    /// Gets or sets the distance to the target to consider the entity 'arrived'.
    /// </summary>
    [ExportGroup("Movement Thresholds")]
    [Export(PropertyHint.Range, "0.1,50.0,0.1")] 
    public float MovementThreshold { get; set; } = 2.0f;
    
    /// <summary>
    /// Gets or sets the angle (in degrees) to the target rotation to consider the entity 'aligned'.
    /// </summary>
    [Export(PropertyHint.Range, "0.1,10.0,0.1")]
    public float RotationThreshold { get; set; } = 1.0f;
    
    /// <summary>
    /// Gets or sets the distance at which the entity starts to slow down when approaching a target.
    /// </summary>
    [Export(PropertyHint.Range, "10.0,500.0,1.0")]
    public float SlowingDistance { get; set; } = 50.0f;
    
    /// <summary>
    /// Gets or sets the allegiance of this entity.
    /// </summary>
    [Export]
    public Allegiance Allegiance { get; set; } = Allegiance.NeutralForces;
    
    /// <summary>
    /// Gets or sets the base category of this entity (e.g., Ship, Projectile).
    /// </summary>
    [Export]
    public BaseEntityType BaseType { get; set; } = BaseEntityType.Ship;

    /// <summary>
    /// Gets or sets the specific type of ship if BaseType is Ship (e.g., Fighter, Scout).
    /// </summary>
    [Export]
    public ShipType ShipDetailType { get; set; } = ShipType.Fighter;
    
    /// <summary>
    /// Default constructor.
    /// </summary>
    public BattleEntityData()
    {
    }
    
    /// <summary>
    /// Copy constructor.
    /// </summary>
    /// <param name="other">The entity data to copy from.</param>
    public BattleEntityData(BattleEntityData other)
    {
        if (other == null)
            return;
            
        Id = other.Id;
        Name = other.Name;
        MaxHealth = other.MaxHealth;
        MaxSpeed = other.MaxSpeed;
        RotationSpeed = other.RotationSpeed;
        Acceleration = other.Acceleration;
        Deceleration = other.Deceleration;
        DragFactor = other.DragFactor;
        MovementThreshold = other.MovementThreshold;
        RotationThreshold = other.RotationThreshold;
        SlowingDistance = other.SlowingDistance;
        Allegiance = other.Allegiance;
        BaseType = other.BaseType;
        ShipDetailType = other.ShipDetailType;
    }
    
    /// <summary>
    /// Creates a clone of this entity data.
    /// </summary>
    /// <param name="generateNewId">Whether to generate a new ID for the clone.</param>
    /// <returns>A new instance with the same properties.</returns>
    public BattleEntityData Clone(bool generateNewId = true)
    {
        BattleEntityData clone = new BattleEntityData(this);
        
        if (generateNewId)
        {
            clone.Id = Guid.NewGuid().ToString();
        }

        return clone;
    }

    /// <summary>
    /// Applies state from a BattleEntityStateData object to this instance.
    /// </summary>
    /// <param name="stateData">The state data to apply.</param>
    public void ApplyState(BattleEntityStateData stateData)
    {
        if (stateData == null)
        {
            GD.PrintErr("Attempted to apply null state data to BattleEntityData.");
            return;
        }

        Id = stateData.Id;
        Name = stateData.Name;
        MaxHealth = stateData.MaxHealth;
        MaxSpeed = stateData.MaxSpeed;
        RotationSpeed = stateData.RotationSpeed;
        Acceleration = stateData.Acceleration;
        Deceleration = stateData.Deceleration;
        DragFactor = stateData.DragFactor;
        MovementThreshold = stateData.MovementThreshold;
        RotationThreshold = stateData.RotationThreshold;
        SlowingDistance = stateData.SlowingDistance;
        Allegiance = stateData.Allegiance;
        BaseType = stateData.BaseType;
        ShipDetailType = stateData.ShipDetailType;
    }
}
