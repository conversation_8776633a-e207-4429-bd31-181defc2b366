using Godot;
using System;

namespace SpaceGame.Scripts.Battle.Entities.Behaviors
{
    /// <summary>
    /// Behavior that makes a projectile spin as it moves.
    /// </summary>
    public partial class SpinningBehavior : ProjectileBehavior
    {
        /// <summary>
        /// Rotation speed in radians per second.
        /// </summary>
        [Export]
        public float RotationSpeed { get; set; } = Mathf.Pi; // Default: 180 degrees per second
        
        /// <summary>
        /// Whether to rotate clockwise (true) or counter-clockwise (false).
        /// </summary>
        [Export]
        public bool RotateClockwise { get; set; } = true;
        
        /// <summary>
        /// Default constructor required by <PERSON><PERSON>
        /// </summary>
        public SpinningBehavior()
        {
        }
        
        /// <summary>
        /// Updates the projectile's rotation to create a spinning effect.
        /// </summary>
        /// <param name="projectile">The projectile entity to update</param>
        /// <param name="delta">Time elapsed since last frame</param>
        public override void UpdateBehavior(ProjectileEntity projectile, double delta)
        {
            float rotationAmount = RotationSpeed * (float)delta;
            
            if (!RotateClockwise)
            {
                rotationAmount = -rotationAmount;
            }
            
            // Apply rotation to the projectile
            projectile.Rotation += rotationAmount;
        }
    }
}
