using Godot;
using System;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Battle.Entities.Behaviors
{
    /// <summary>
    /// Behavior that makes a projectile home in on a target.
    /// </summary>
    public partial class HomingBehavior : ProjectileBehavior
    {
        /// <summary>
        /// The target node to home in on.
        /// </summary>
        [Export]
        public Node2D Target { get; set; }
        
        /// <summary>
        /// How quickly the projectile can turn toward the target (in radians per second).
        /// </summary>
        [Export]
        public float TurningRate { get; set; } = 2.0f;
        
        /// <summary>
        /// Maximum distance to track the target. If target is beyond this distance, homing will stop.
        /// Set to 0 for unlimited range.
        /// </summary>
        [Export]
        public float MaxTrackingRange { get; set; } = 0.0f;
        
        /// <summary>
        /// Default constructor required by God<PERSON>
        /// </summary>
        public HomingBehavior()
        {
        }
        
        /// <summary>
        /// Sets the target for this homing behavior.
        /// </summary>
        /// <param name="target">The target to home in on</param>
        public void SetTarget(Node2D target)
        {
            Target = target;
        }
        
        /// <summary>
        /// Updates the projectile's direction to home in on the target.
        /// </summary>
        /// <param name="projectile">The projectile entity to update</param>
        /// <param name="delta">Time elapsed since last frame</param>
        public override void UpdateBehavior(ProjectileEntity projectile, double delta)
        {
            if (Target == null || !IsInstanceValid(Target) || !Target.IsInsideTree())
            {
                // No valid target, maintain current direction
                return;
            }
            
            // Check if target is within tracking range (if specified)
            if (MaxTrackingRange > 0)
            {
                float distanceToTarget = projectile.GlobalPosition.DistanceTo(Target.GlobalPosition);
                if (distanceToTarget > MaxTrackingRange)
                {
                    // Target is too far away, stop tracking
                    return;
                }
            }
            
            // Calculate direction to target
            Vector2 currentPosition = projectile.GlobalPosition;
            Vector2 targetPosition = Target.GlobalPosition;
            Vector2 directionToTarget = (targetPosition - currentPosition).Normalized();
            
            // Get current direction and speed
            Vector2 currentDirection = projectile.Direction;
            float currentSpeed = projectile.GetSpeed();
            
            // Calculate the angle between current direction and direction to target
            float currentAngle = Mathf.Atan2(currentDirection.Y, currentDirection.X);
            float targetAngle = Mathf.Atan2(directionToTarget.Y, directionToTarget.X);
            
            // Find the shortest rotation direction
            float angleDifference = Mathf.AngleDifference(currentAngle, targetAngle);
            
            // Calculate maximum rotation this frame
            float maxRotationThisFrame = TurningRate * (float)delta;
            
            // Limit rotation to maximum turning rate
            float rotation = Mathf.Clamp(angleDifference, -maxRotationThisFrame, maxRotationThisFrame);
            
            // Apply rotation to get new direction
            float newAngle = currentAngle + rotation;
            Vector2 newDirection = new Vector2(Mathf.Cos(newAngle), Mathf.Sin(newAngle));
            
            // Update projectile direction, maintaining current speed
            projectile.SetDirection(newDirection);
            projectile.SetSpeed(currentSpeed);
            
            Logger.Debug($"Homing: Target={Target.Name}, Angle diff={angleDifference}, New direction={newDirection}");
        }
    }
}
