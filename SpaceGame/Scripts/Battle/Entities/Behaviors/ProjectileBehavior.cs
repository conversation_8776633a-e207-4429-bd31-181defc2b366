using Godot;
using System;

namespace SpaceGame.Scripts.Battle.Entities.Behaviors
{
    /// <summary>
    /// Abstract base class for all projectile behaviors.
    /// Behaviors are attached as child nodes to ProjectileEntity and provide modular functionality.
    /// </summary>
    public abstract partial class ProjectileBehavior : Node
    {
        /// <summary>
        /// Called by the parent ProjectileEntity during physics processing to update this behavior.
        /// </summary>
        /// <param name="projectile">The parent ProjectileEntity this behavior is attached to</param>
        /// <param name="delta">Time elapsed since the last frame</param>
        public abstract void UpdateBehavior(ProjectileEntity projectile, double delta);
        
        /// <summary>
        /// Default constructor required by Godot
        /// </summary>
        public ProjectileBehavior()
        {
        }
    }
}