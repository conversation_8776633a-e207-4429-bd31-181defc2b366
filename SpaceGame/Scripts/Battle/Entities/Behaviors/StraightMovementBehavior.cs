using Godot;
using System;

namespace SpaceGame.Scripts.Battle.Entities.Behaviors
{
    /// <summary>
    /// Behavior that moves a projectile in a straight line based on its direction and speed.
    /// </summary>
    public partial class StraightMovementBehavior : ProjectileBehavior
    {
        /// <summary>
        /// Speed multiplier for the projectile. Applied once when the behavior is initialized.
        /// </summary>
        [Export]
        public float SpeedMultiplier { get; set; } = 1.0f;
        
        /// <summary>
        /// Whether the speed multiplier has been applied yet
        /// </summary>
        private bool _speedMultiplierApplied = false;
        
        /// <summary>
        /// Default constructor required by <PERSON><PERSON>
        /// </summary>
        public StraightMovementBehavior()
        {
        }
        
        /// <summary>
        /// Updates the projectile's movement in a straight line.
        /// </summary>
        /// <param name="projectile">The projectile entity to update</param>
        /// <param name="delta">Time elapsed since last frame</param>
        public override void UpdateBehavior(ProjectileEntity projectile, double delta)
        {
            // This behavior simply maintains the current direction and speed
            // The actual movement is handled by the ProjectileEntity's _PhysicsProcess
            
            // Apply speed multiplier only once when the behavior is first initialized
            if (!_speedMultiplierApplied && SpeedMultiplier != 1.0f)
            {
                float currentSpeed = projectile.GetSpeed();
                projectile.SetSpeed(currentSpeed * SpeedMultiplier);
                _speedMultiplierApplied = true;
            }
        }
    }
}
