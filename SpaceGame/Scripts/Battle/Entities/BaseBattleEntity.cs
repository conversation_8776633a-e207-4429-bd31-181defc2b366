using System;
using Godot;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Registry;

namespace SpaceGame.Scripts.Battle.Entities
{
    /// <summary>
    /// Base implementation for battle entities.
    /// Provides common functionality for health, damage, and allegiance management.
    /// </summary>
    public abstract partial class BaseBattleEntity : CharacterBody2D, IBattleEntity
    {
        // Unique identifier for this entity
        private string _id = Guid.NewGuid().ToString();
        
        // Health properties
        protected float currentHealth;
        private float _maxHealth = 100f;
        
        // Allegiance property
        private Allegiance _currentAllegiance = Allegiance.NeutralForces;
        
        // Reference to the allegiance manager
        private AllegianceManager _allegianceManager => AllegianceManager.Instance;
        
        // Reference to the battle entity tracker
        private BattleEntityTracker _battleEntityTracker => BattleEntityTracker.Instance;

        // Events
        public static event IBattleEntity.DamagedEventHandler Damaged;
        public static event IBattleEntity.DestroyedEventHandler Destroyed;
        public static event IBattleEntity.AllegianceChangedEventHandler AllegianceChanged;
        
        /// <summary>
        /// The sprite for this entity.
        /// </summary>
        [Export]
        public AnimatedSprite2D EntitySprite { get; set; }

        /// <summary>
        /// Optional trail component for visual movement trails.
        /// </summary>
        [Export]
        public Node2D TrailComponent { get; set; }
        
        /// <summary>
        /// The data for this entity.
        /// </summary>
        [Export] 
        public BaseBattleEntityData EntityData { get; protected set; }
        
        /// <summary>
        /// Gets the entity's unique identifier.
        /// </summary>
        public string Id => _id;
        
        /// <summary>
        /// Gets or sets the entity's current health.
        /// </summary>
        public float CurrentHealth
        {
            get => currentHealth;
            set
            {
                currentHealth = Mathf.Clamp(value, 0, MaxHealth);
                
                // Check if the entity is destroyed
                if (currentHealth <= 0)
                {
                    Destroy();
                }
            }
        }
        
        public bool IsDead => currentHealth <= 0f;
        
        /// <summary>
        /// Gets the entity's maximum health.
        /// </summary>
        public float MaxHealth => _maxHealth;
        
        /// <summary>
        /// Gets or sets the entity's allegiance.
        /// </summary>
        public Allegiance CurrentAllegiance
        {
            get => _currentAllegiance;
            set => ChangeAllegiance(value);
        }
        
        /// <summary>
        /// Gets or sets the entity's position in the battle space.
        /// </summary>
        public new Vector2 Position
        {
            get => GlobalPosition;
            set => GlobalPosition = value;
        }

        /// <summary>
        /// Gets or sets the entity's rotation in the battle space.
        /// </summary>
        public new float Rotation
        {
            get => GlobalRotation;
            set => GlobalRotation = value;
        }

        /// <summary>
        /// Gets or sets the entity's velocity for CharacterBody2D physics.
        /// </summary>
        public new Vector2 Velocity
        {
            get => base.Velocity;
            set => base.Velocity = value;
        }
        
        /// <summary>
        /// Default constructor required by Godot
        /// </summary>
        public BaseBattleEntity()
        {
        }
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            
            // Find components if not set
            if (EntitySprite == null)
            {
                EntitySprite = GetNodeOrNull<AnimatedSprite2D>("EntitySprite");
            }

            if (TrailComponent == null)
            {
                TrailComponent = GetNodeOrNull<Node2D>("TrailComponent");
            }
            
            // Initialize from EntityData if available
            if (EntityData != null)
            {
                _maxHealth = EntityData.MaxHealth;
                currentHealth = _maxHealth;
            }
            else
            {
                Logger.Warning($"{Name} has no EntityData assigned.");
                // Initialize health with default values
                currentHealth = _maxHealth;
            }
            
            // Register with the BattleEntityTracker
            _battleEntityTracker.RegisterEntity(this);
        }
        
        /// <summary>
        /// Applies damage to the entity.
        /// </summary>
        /// <param name="amount">The amount of damage to apply.</param>
        /// <param name="source">The source of the damage, if any.</param>
        /// <returns>The actual amount of damage applied.</returns>
        public virtual float ApplyDamage(float amount, IBattleEntity source = null)
        {
            if (amount <= 0)
                return 0;
            
            float oldHealth = CurrentHealth;
            CurrentHealth -= amount;
            float actualDamage = oldHealth - CurrentHealth;
            
            // Trigger damaged event
            Damaged?.Invoke(this, actualDamage, source);
            
            return actualDamage;
        }
        
        /// <summary>
        /// Applies healing to the entity.
        /// </summary>
        /// <param name="amount">The amount of healing to apply.</param>
        /// <returns>The actual amount of healing applied.</returns>
        public virtual float ApplyHealing(float amount)
        {
            if (amount <= 0)
                return 0;
            
            float oldHealth = CurrentHealth;
            CurrentHealth += amount;
            float actualHealing = CurrentHealth - oldHealth;
            
            return actualHealing;
        }
        
        /// <summary>
        /// Changes the entity's allegiance.
        /// </summary>
        /// <param name="newAllegiance">The new allegiance to assign.</param>
        public virtual void ChangeAllegiance(Allegiance newAllegiance)
        {
            if (_currentAllegiance == newAllegiance)
                return;
            
            Allegiance oldAllegiance = _currentAllegiance;
            _currentAllegiance = newAllegiance;
            
            // Trigger allegiance changed event
            AllegianceChanged?.Invoke(this, oldAllegiance, newAllegiance);
        }
        
        /// <summary>
        /// Checks if this entity is hostile to another entity.
        /// </summary>
        /// <param name="other">The other entity to check against.</param>
        /// <returns>True if the entities are hostile to each other, false otherwise.</returns>
        public virtual bool IsHostileTo(IBattleEntity other)
        {
            if (other == null)
                return false;
            
            // If allegiance manager is available, use it to check relationship
            if (_allegianceManager != null)
            {
                return _allegianceManager.AreHostile(CurrentAllegiance, other.CurrentAllegiance);
            }
            
            // Default behavior if allegiance manager is not available
            return CurrentAllegiance != other.CurrentAllegiance;
        }
        
        /// <summary>
        /// Destroys the entity, removing it from the battle.
        /// </summary>
        public virtual void Destroy()
        {
            // Trigger destroyed event
            Destroyed?.Invoke(this);
            
            // Queue the node for deletion
            QueueFree();
        }
        
        /// <summary>
        /// Called when the node is about to be removed from the scene tree.
        /// </summary>
        public override void _ExitTree()
        {
            // Unregister from the BattleEntityTracker
            _battleEntityTracker.UnregisterEntity(this);
            base._ExitTree();
        }
        
        /// <summary>
        /// Sets the maximum health for this entity.
        /// </summary>
        /// <param name="maxHealth">The new maximum health value.</param>
        protected void SetMaxHealth(float maxHealth)
        {
            _maxHealth = Mathf.Max(1, maxHealth); // Ensure max health is at least 1
            CurrentHealth = Mathf.Min(CurrentHealth, _maxHealth); // Adjust current health if needed
        }
    }
}
