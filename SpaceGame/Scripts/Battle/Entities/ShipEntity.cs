using System;
using System.Collections.Generic;
using Godot;
using SpaceGame.Debug;
using SpaceGame.Scripts.Managers;
using SpaceGame.Scripts.Battle.Movement;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle.Interfaces;
using SpaceGame.Scripts.Ships; // Added for ShipWeaponController
using SpaceGame.Battle.AI;
using SpaceGame.Scripts.Battle.Components;
using SpaceGame.Scripts.Events; // Added for ShipBehaviorController

namespace SpaceGame.Scripts.Battle.Entities
{
    /// <summary>
    /// Implementation for ship entities in the game world.
    /// Integrates ship-specific data, stats, and visual components.
    /// </summary>
    public partial class ShipEntity : BaseBattleEntity, IDamageable
    {
        /// <summary>
        /// The ship-specific data for this entity.
        /// </summary>
        public new ShipEntityData EntityData 
        {
            get => base.EntityData as ShipEntityData;
            protected set => base.EntityData = value;
        }
        
        /// <summary>
        /// Current shield strength of the ship.
        /// </summary>
        public float CurrentShields { get; set; }
        
        /// <summary>
        /// Maximum shield strength of the ship.
        /// </summary>
        public float MaxShields { get; set; }
        
        /// <summary>
        /// Shield regeneration rate per second.
        /// </summary>
        public float ShieldRegenRate { get; set; }
        
        /// <summary>
        /// Time in seconds since the ship last took damage.
        /// </summary>
        private float _timeSinceLastDamage = float.MaxValue;
        
        /// <summary>
        /// Delay in seconds before shields start regenerating after taking damage.
        /// </summary>
        private float _shieldRegenDelay = 3.0f;
        
        /// <summary>
        /// Whether this ship is dead (destroyed).
        /// </summary>
        public bool IsDead { get; set; }
        
        /// <summary>
        /// Event raised when this ship is damaged.
        /// </summary>
        public event EventHandler<DamageInfo> Damaged;
        
        /// <summary>
        /// Event raised when this ship is destroyed.
        /// </summary>
        public event EventHandler<DamageInfo> Destroyed;
        
        /// <summary>
        /// The movement controller for this ship.
        /// </summary>
        [Export]
        public BattleEntityMovementController MovementController { get; private set; }
        
        /// <summary>
        /// The thruster component for this ship.
        /// </summary>
        [Export]
        public ThrusterComponent Thruster { get; set; }
        
        /// <summary>
        /// The collision shape for this ship.
        /// </summary>
        [Export]
        public CollisionShape2D CollisionShape { get; set; }
        
        /// <summary>
        /// The container for weapon mounts.
        /// </summary>
        [Export]
        public Node WeaponMountsContainer { get; set; }
        
        // Dictionary of weapon mounts by mount point ID
        private Dictionary<string, WeaponMount> _weaponMounts = new Dictionary<string, WeaponMount>();
        
        // Reference to the ship weapon controller
        [Export]
        private ShipWeaponController _shipWeaponController;
        
        private bool _isPlayerControlled = false;

        public bool IsPlayerControlled => _isPlayerControlled;
        
        // The ID of this entity in the battle entity registry, if any
        private string _battleEntityRegistryId;
        
        /// <summary>
        /// Event raised when a weapon is attached to this ship.
        /// </summary>
        public static event EventHandler<WeaponAttachedEventArgs> WeaponAttached;
        
        /// <summary>
        /// Default constructor required by Godot
        /// </summary>
        public ShipEntity()
        {
        }

        public override void _EnterTree()
        {
            PlayerControlComponent.PlayerControlGained += OnPlayerControlGained;
        }

        /// <summary>
        /// Sets the registry ID for this entity.
        /// </summary>
        /// <param name="id">The registry ID to set.</param>
        public void SetRegistryId(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.Warning($"{Name}: Attempted to set empty registry ID.");
                return;
            }
            
            _battleEntityRegistryId = id;
            Logger.Debug($"{Name}: Set registry ID to '{id}'.");
        }
        
        /// <summary>
        /// Gets the registry ID for this entity.
        /// </summary>
        /// <returns>The registry ID, or null if not set.</returns>
        public string GetRegistryId()
        {
            return _battleEntityRegistryId;
        }
        
        /// <summary>
        /// Initializes the weapon mounts for this ship.
        /// </summary>
        private void InitializeWeaponMounts()
        {
            if (WeaponMountsContainer == null)
            {
                Logger.Debug($"{Name}: WeaponMountsContainer is null, skipping weapon mount initialization.");
                return;
            }
            
            _weaponMounts.Clear();
            
            // Find all weapon mounts in the container
            foreach (Node child in WeaponMountsContainer.GetChildren())
            {
                if (child is WeaponMount mount)
                {
                    string mountId = mount.MountId;
                    if (!string.IsNullOrEmpty(mountId))
                    {
                        _weaponMounts[mountId] = mount;
                    }
                    else
                    {
                        Logger.Warning($"{Name}: Found weapon mount with empty ID, skipping.");
                    }
                }
            }
            
            Logger.Debug($"Initialized {_weaponMounts.Count} weapon mounts for {Name}");
        }
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            DebugRegistry.RegisterObject($"{Name} ({Id})", this, "Ships");
            
            // Get the movement controller
            if (MovementController == null)
            {
                Logger.Error($"{Name}: MovementController node not found. Ship will not be able to move.");
            }
            
            // Get the thruster component
            if (Thruster == null)
            {
                Logger.Error($"{Name}: Thruster node not found. Thrusters will not show properly.");
            }
            
            // Get the collision shape
            if (CollisionShape == null)
            {
                Logger.Error($"{Name}: CollisionShape2D node not found. Collisions will not be detected.");
            }
            
            // Get the weapon mounts container
            if (WeaponMountsContainer == null)
            {
                Logger.Error($"{Name}: WeaponMountsContainer node not found. Weapons will not be able to fire.");
            }
            
            // Initialize weapon mounts
            InitializeWeaponMounts();
            
            // Find the ship weapon controller
            if (_shipWeaponController == null)
            {
                Logger.Debug($"{Name}: ShipWeaponController not found. Ship will not be able to fire weapons.");
            }
            
            // Initialize with default data if none is set
            if (EntityData == null)
            {
                Logger.Debug($"{Name}: EntityData is null in _Ready, creating default. Consider assigning EntityData in the scene or via Initialize().");
                EntityData = new ShipEntityData();
            }
            
            // Apply entity data
            ApplyEntityData();
            
            // Apply property overrides if we have a registry ID
            if (!string.IsNullOrEmpty(_battleEntityRegistryId))
            {
                try
                {
                    var overrideManager = OverrideManager.Instance;
                    if (overrideManager == null)
                    {
                        Logger.Warning($"{Name} ({_battleEntityRegistryId}): OverrideManager instance not available.");
                    }

                    overrideManager?.ApplyGlobalOverrides(this, _battleEntityRegistryId);
                }
                catch (Exception ex)
                {
                    Logger.Error($"{Name} ({_battleEntityRegistryId}): Error applying property overrides: {ex.Message}");
                }
            }
        }

        private void ApplyEntityData()
        {
            if (EntityData == null)
                return;
            
            // Update core properties from base entity data
            SetMaxHealth(EntityData.MaxHealth);
            
            // Initialize shields
            MaxShields = EntityData.MaxShields;
            CurrentShields = MaxShields;
            ShieldRegenRate = EntityData.ShieldRegenRate;
            
            MovementController?.UpdateFromEntityData(EntityData);
            _shipWeaponController?.UpdateEntityData(EntityData);
        }
        
        public override void _PhysicsProcess(double delta)
        {
            base._PhysicsProcess(delta);
            
            if (IsDead)
                return;
                
            // Update shield regeneration
            _timeSinceLastDamage += (float)delta;
            
            if (_timeSinceLastDamage > _shieldRegenDelay && CurrentShields < MaxShields && ShieldRegenRate > 0)
            {
                CurrentShields = Mathf.Min(MaxShields, CurrentShields + ShieldRegenRate * (float)delta);
            }
        }
        
        /// <summary>
        /// Sets the entity data and initializes the entity.
        /// </summary>
        /// <param name="data">The entity data to use.</param>
        public void SetEntityData(ShipEntityData data)
        {
            EntityData = data;
            ApplyEntityData();
        }
        
        /// <summary>
        /// Gets a weapon mount by its ID.
        /// </summary>
        /// <param name="mountId">The ID of the mount to get.</param>
        /// <returns>The weapon mount, or null if not found.</returns>
        public WeaponMount GetWeaponMount(string mountId)
        {
            if (string.IsNullOrEmpty(mountId))
            {
                Logger.Warning($"{Name}: Attempted to get weapon mount with empty ID.");
                return null;
            }
            
            if (_weaponMounts.TryGetValue(mountId, out WeaponMount mount))
            {
                return mount;
            }
            
            Logger.Warning($"{Name}: Weapon mount '{mountId}' not found.");
            return null;
        }
        
        /// <summary>
        /// Event handler for when a weapon is attached to this ship.
        /// </summary>
        /// <param name="weapon">The weapon that was attached.</param>
        /// <param name="mountPoint">The mount point the weapon was attached to.</param>
        public void OnWeaponAttached(Node weapon, string mountPoint)
        {
            Logger.Debug($"Weapon {weapon.Name} attached to mount {mountPoint} on {Name}");
            
            // Raise the event
            WeaponAttached?.Invoke(this, new WeaponAttachedEventArgs(weapon, mountPoint));
        }
        
        /// <summary>
        /// Fires the ship's primary weapons, if any.
        /// </summary>
        /// <returns>True if at least one weapon was fired, false otherwise.</returns>
        public bool FirePrimaryWeapons()
        {
            if (_shipWeaponController == null)
            {
                Logger.Warning($"{Name}: Cannot fire primary weapons - ShipWeaponController is null.");
                return false;
            }
            
            // Delegate to the weapon controller
            return _shipWeaponController.FireAllWeapons();
        }
        
        /// <summary>
        /// Called when the node is about to be removed from the scene tree.
        /// </summary>
        public override void _ExitTree()
        {
            // Unregister from the debug registry
            DebugRegistry.UnregisterObject($"{Name} ({Id})");
            
            // Unsubscribe from battle events
            BattleEvents.ShipDestroyed -= OnShipDestroyed;
            PlayerControlComponent.PlayerControlGained -= OnPlayerControlGained;
            
            base._ExitTree();
        }

        private void OnPlayerControlGained(object? sender, PlayerControlEventArgs args)
        {
            _isPlayerControlled = args.Entity == this;
        }
        
        /// <summary>
        /// Applies damage to this ship.
        /// </summary>
        /// <param name="amount">The amount of damage to apply.</param>
        /// <param name="source">The entity that caused the damage, if any.</param>
        public void ApplyDamage(float amount, BaseBattleEntity source)
        {
            if (IsDead)
                return;
                
            // Create damage info
            var damageInfo = new DamageInfo
            {
                Amount = amount,
                Source = source,
                DamageType = DamageType.Kinetic, // Default to kinetic if not specified
                ImpactPoint = GlobalPosition
            };
            
            // Apply damage using the damage system
            DamageSystem.ApplyDamageToShip(this, damageInfo);
            
            // Reset shield regen timer
            _timeSinceLastDamage = 0;
            
            // Raise damaged event
            Damaged?.Invoke(this, damageInfo);
        }
        
        /// <summary>
        /// Handles the destruction of this ship.
        /// </summary>
        /// <param name="damageInfo">Information about the damage that destroyed the ship.</param>
        public void HandleDestruction(DamageInfo damageInfo)
        {
            if (IsDead)
                return;
                
            IsDead = true;
            
            // Raise destroyed event
            Destroyed?.Invoke(this, damageInfo);
            
            // Visual effects for destruction
            // TODO: Add explosion effects
            
            // Queue for removal
            QueueFree();
        }
        
        /// <summary>
        /// Event handler for when a ship is destroyed.
        /// </summary>
        private void OnShipDestroyed(object sender, ShipDestroyedEventArgs e)
        {
            // If we're the ship that was destroyed, handle our destruction
            if (e.Ship == this)
            {
                HandleDestruction(e.DamageInfo);
            }
            
            // If we're targeting the destroyed ship, clear our target
            var behaviorController = GetNodeOrNull<ShipBehaviorController>("BehaviorController");
            if (behaviorController != null)
            {
                var targetingBehavior = behaviorController.GetTargetingBehavior();
                if (targetingBehavior != null && targetingBehavior.CurrentTarget == e.Ship)
                {
                    Logger.Debug($"{Name}: Target {e.Ship.Name} was destroyed, clearing target");
                    targetingBehavior.CurrentTarget = null;
                }
            }
        }
    }
    
    /// <summary>
    /// Event arguments for when a weapon is attached to a ship.
    /// </summary>
    public class WeaponAttachedEventArgs : EventArgs
    {
        /// <summary>
        /// The weapon that was attached.
        /// </summary>
        public Node Weapon { get; }
        
        /// <summary>
        /// The mount point the weapon was attached to.
        /// </summary>
        public string MountPoint { get; }
        
        /// <summary>
        /// Creates a new instance of the event arguments.
        /// </summary>
        /// <param name="weapon">The weapon that was attached.</param>
        /// <param name="mountPoint">The mount point the weapon was attached to.</param>
        public WeaponAttachedEventArgs(Node weapon, string mountPoint)
        {
            Weapon = weapon;
            MountPoint = mountPoint;
        }
    }
}
