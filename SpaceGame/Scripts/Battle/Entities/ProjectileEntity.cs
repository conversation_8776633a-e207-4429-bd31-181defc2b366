using Godot;
using SpaceGame.Debug;
using SpaceGame.Scripts.Battle.Interfaces;
using SpaceGame.Scripts.Events;

namespace SpaceGame.Scripts.Battle.Entities
{
    /// <summary>
    /// Implementation for projectile entities in the game world.
    /// Manages projectile-specific behavior like lifetime, movement, and impact logic.
    /// </summary>
    public partial class ProjectileEntity : BaseBattleEntity
    {
        /// <summary>
        /// The projectile-specific data for this entity.
        /// </summary>
        public new ProjectileEntityData EntityData 
        {
            get => base.EntityData as ProjectileEntityData;
            protected set => base.EntityData = value;
        }
        
        /// <summary>
        /// The collision shape for this projectile.
        /// </summary>
        [Export]
        public CollisionShape2D CollisionShape { get; set; }
        
        /// <summary>
        /// Optional particles for thruster/trail effects.
        /// </summary>
        [Export]
        public GpuParticles2D TrailParticles { get; set; }
        
        /// <summary>
        /// The entity that fired this projectile.
        /// </summary>
        public BaseBattleEntity Source { get; private set; }
        
        /// <summary>
        /// Area2D used for hit detection.
        /// </summary>
        [Export]
        public Area2D HitboxArea { get; set; }
        
        /// <summary>
        /// Optional hit effect particles.
        /// </summary>
        [Export]
        public PackedScene HitEffectScene { get; set; }
        
        /// <summary>
        /// Whether this projectile can damage friendly entities.
        /// </summary>
        [Export]
        public bool AllowFriendlyFire { get; set; } = false;
        
        // Lifetime tracking
        private float _remainingLifetime;
        
        // Movement
        private Vector2 _velocity = Vector2.Zero;
        
        /// <summary>
        /// The normalized direction the projectile is moving in.
        /// </summary>
        public Vector2 Direction { get; private set; } = Vector2.Right;
        
        /// <summary>
        /// Default constructor required by Godot
        /// </summary>
        public ProjectileEntity()
        {
        }
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// </summary>
        public override void _Ready()
        {
            base._Ready();
            
            DebugRegistry.RegisterObject($"{Name} ({Id})" , this, "Projectiles");
            
            if (CollisionShape == null)
            {
                CollisionShape = GetNodeOrNull<CollisionShape2D>("CollisionShape2D");
                if (CollisionShape == null)
                {
                    Logger.Warning($"{Name}: CollisionShape2D not found. Creating a default one.");
                    CollisionShape = new CollisionShape2D();
                    CollisionShape.Shape = new CircleShape2D() { Radius = 5.0f }; // Default small radius
                    AddChild(CollisionShape);
                }
            }
            
            if (HitboxArea == null)
            {
                HitboxArea = GetNodeOrNull<Area2D>("HitboxArea");
                if (HitboxArea == null)
                {
                    Logger.Warning($"{Name}: HitboxArea not found. Creating a default one.");
                    HitboxArea = new Area2D();
                    HitboxArea.Name = "HitboxArea";
                    AddChild(HitboxArea);
                    
                    // Create a collision shape for the hitbox if needed
                    var hitboxShape = new CollisionShape2D();
                    hitboxShape.Shape = new CircleShape2D() { Radius = 10.0f }; // Slightly larger than the physical collision
                    HitboxArea.AddChild(hitboxShape);
                }
            }
            
            // Ensure the hitbox has the proper collision layer and mask
            if (HitboxArea != null)
            {
                // Set up collision layers appropriate for projectiles
                // This would depend on your game's layer setup
                // HitboxArea.CollisionLayer = 4; // Example: Layer 3 for projectiles
                // HitboxArea.CollisionMask = 3; // Example: Layers 1 & 2 for ships
                
                HitboxArea.BodyEntered += OnHitboxAreaBodyEntered;
                HitboxArea.AreaEntered += OnHitboxAreaAreaEntered;
            }
            else
            {
                Logger.Error($"{Name}: Failed to create or find HitboxArea. Projectile will not detect collisions.");
            }
            
            if (TrailParticles == null)
            {
                TrailParticles = GetNodeOrNull<GpuParticles2D>("TrailParticles");
            }
            
            // Initialize with default data if none is set
            if (EntityData == null)
            {
                Logger.Debug($"{Name}: EntityData is null in _Ready, creating default. Consider assigning EntityData in the scene or via Initialize().");
                EntityData = new ProjectileEntityData();
            }
        }
        
        /// <summary>
        /// Called every physics frame to update projectile position and check lifetime.
        /// </summary>
        /// <param name="delta">Time since last frame in seconds.</param>
        public override void _PhysicsProcess(double delta)
        {
            base._PhysicsProcess(delta);
            
            float deltaf = (float)delta;
            
            // Update lifetime
            _remainingLifetime -= deltaf;
            if (_remainingLifetime <= 0)
            {
                // Lifetime expired, destroy projectile
                Destroy();
                return;
            }
            
            // Process all behavior nodes from the Behaviors node
            var behaviorsNode = GetNodeOrNull<Node>("Behaviors");
            if (behaviorsNode != null)
            {
                foreach (var child in behaviorsNode.GetChildren())
                {
                    if (child is Behaviors.ProjectileBehavior behavior)
                    {
                        behavior.UpdateBehavior(this, delta);
                    }
                }
            }
            
            // Move projectile using CharacterBody2D physics
            Velocity = _velocity;
            MoveAndSlide();
        }
        
        /// <summary>
        /// Initialize the projectile with specific data and source entity.
        /// </summary>
        /// <param name="data">The projectile data.</param>
        /// <param name="source">The entity that fired this projectile.</param>
        public void Initialize(ProjectileEntityData data, BaseBattleEntity source)
        {
            EntityData = data;
            Source = source;
            
            if (source != null)
            {
                // Inherit allegiance from source
                CurrentAllegiance = source.CurrentAllegiance;
            }
            
            _remainingLifetime = data.Lifetime;
            
            // Start particles if they exist
            if (TrailParticles != null)
            {
                TrailParticles.Emitting = true;
            }
        }
        
        /// <summary>
        /// Set the velocity directly (usually only needed for special projectiles).
        /// </summary>
        /// <param name="velocity">The new velocity vector.</param>
        public void SetVelocity(Vector2 velocity)
        {
            _velocity = velocity;
            
            // Update direction if velocity is non-zero
            if (_velocity.LengthSquared() > 0.001f)
            {
                Direction = _velocity.Normalized();
                Rotation = _velocity.Angle();
            }
        }
        
        /// <summary>
        /// Sets the direction of the projectile and updates velocity accordingly.
        /// </summary>
        /// <param name="direction">The new direction (will be normalized).</param>
        public void SetDirection(Vector2 direction)
        {
            if (direction.LengthSquared() > 0.001f)
            {
                Direction = direction.Normalized();
                _velocity = Direction * _velocity.Length();
                Rotation = Direction.Angle();
            }
        }
        
        /// <summary>
        /// Gets the current speed of the projectile.
        /// </summary>
        /// <returns>The current speed (magnitude of velocity).</returns>
        public float GetSpeed()
        {
            return _velocity.Length();
        }
        
        /// <summary>
        /// Sets the speed of the projectile while maintaining its direction.
        /// </summary>
        /// <param name="speed">The new speed value.</param>
        public void SetSpeed(float speed)
        {
            _velocity = Direction * speed;
        }
        
        private void OnHitboxAreaBodyEntered(Node2D body)
        {
            HandleCollision(body);
        }
        
        private void OnHitboxAreaAreaEntered(Area2D area)
        {
            // Check if the area belongs to a battle entity
            var areaOwner = area.GetParent();
            if (areaOwner is Node2D node2D)
            {
                HandleCollision(node2D);
            }
        }
        
        private void HandleCollision(Node2D collidedNode)
        {
            if (EntityData == null) return; // Should not happen if initialized

            // Prevent self-collision or collision with source if specified
            if (collidedNode == Source || collidedNode == this) // `collidedNode == this` might be redundant depending on collision layers/masks
            {
                return;
            }

            var targetBattleEntity = collidedNode as BaseBattleEntity;
            var damageableTarget = collidedNode as IDamageable;

            if (targetBattleEntity != null)
            {
                // Check allegiance if it's a battle entity
                if (!IsHostileTo(targetBattleEntity) && !AllowFriendlyFire)
                {
                    Logger.Debug($"{Name}: Skipping friendly target {targetBattleEntity.Name}");
                    return;
                }
            }
            
            // Create damage info for the hit
            var damageInfo = new DamageInfo
            {
                Amount = EntityData.BaseDamage,
                Source = Source,
                DamageType = EntityData.DamageType,
                ImpactPoint = GlobalPosition,
                ImpactNormal = (targetBattleEntity != null) ? 
                    (targetBattleEntity.GlobalPosition - GlobalPosition).Normalized() : 
                    Vector2.Up
            };
            
            // Apply damage if target is damageable
            if (damageableTarget != null)
            {
                Logger.Debug($"{Name} damaging {collidedNode.Name} for {damageInfo.Amount} ({damageInfo.DamageType})");
                damageableTarget.ApplyDamage(damageInfo.Amount, Source);
                
                // Fire damage event if it's a ship
                if (targetBattleEntity is ShipEntity shipEntity)
                {
                    BattleEvents.FireShipDamaged(shipEntity, damageInfo);
                }
            }
            else
            {
                Logger.Debug($"{Name} hit {collidedNode.Name} but it is not damageable");
            }
            
            // Spawn hit effect
            SpawnHitEffect(collidedNode.GlobalPosition);
            
            // Apply area effects if configured
            if (EntityData.AreaEffectRadius > 0 && EntityData.AreaEffectType != ProjectileAreaEffectType.None)
            {
                ApplyAreaEffect();
            }
            
            // Destroy the projectile on impact
            Destroy();
        }
        
        /// <summary>
        /// Spawns a hit effect at the specified position.
        /// </summary>
        /// <param name="position">The position to spawn the hit effect at.</param>
        private void SpawnHitEffect(Vector2 position)
        {
            // Use the projectile's hit effect if available, otherwise fall back to the entity data's impact effect
            PackedScene effectScene = HitEffectScene ?? EntityData?.ImpactEffectScene;
            
            if (effectScene != null)
            {
                var hitEffect = effectScene.Instantiate<Node2D>();
                GetParent()?.AddChild(hitEffect);
                hitEffect.GlobalPosition = position;
                
                // If it's a particle system, make sure it's emitting
                if (hitEffect is GpuParticles2D particles)
                {
                    particles.Emitting = true;
                    
                    // Auto-free after emission
                    var timer = new Timer();
                    hitEffect.AddChild(timer);
                    timer.WaitTime = particles.Lifetime;
                    timer.OneShot = true;
                    timer.Timeout += () => hitEffect.QueueFree();
                    timer.Start();
                }
            }
        }
        
        /// <summary>
        /// Destroys the projectile, potentially triggering visual/audio effects.
        /// </summary>
        public override void Destroy()
        {
            // Disable collision detection to prevent multiple hits during destruction
            if (HitboxArea != null)
            {
                HitboxArea.Monitoring = false;
                HitboxArea.Monitorable = false;
            }
            
            if (CollisionShape != null)
            {
                CollisionShape.Disabled = true;
            }
            
            // Stop any trail particles but keep them alive to finish their effect
            if (TrailParticles != null)
            {
                TrailParticles.Emitting = false;
            }
            
            // Spawn impact effect if not already spawned by hit handler
            if (EntityData?.ImpactEffectScene != null)
            {
                var impactEffect = EntityData.ImpactEffectScene.Instantiate<Node2D>();
                GetParent()?.AddChild(impactEffect);
                impactEffect.GlobalPosition = GlobalPosition;
                impactEffect.GlobalRotation = GlobalRotation;
                
                // If it's a particle system, make sure it auto-destroys after emission
                if (impactEffect is GpuParticles2D particles)
                {
                    particles.Emitting = true;
                    
                    // Auto-free after emission
                    var timer = new Timer();
                    impactEffect.AddChild(timer);
                    timer.WaitTime = particles.Lifetime;
                    timer.OneShot = true;
                    timer.Timeout += () => impactEffect.QueueFree();
                    timer.Start();
                }
            }

            QueueFree();
        }

        public override void _ExitTree()
        {
            DebugRegistry.UnregisterObject($"{Name} ({Id})");
        }

        private void ApplyAreaEffect()
        {
            if (EntityData == null || EntityData.AreaEffectRadius <= 0 || EntityData.AreaEffectType == ProjectileAreaEffectType.None)
            {
                return;
            }

            var spaceState = GetWorld2D().DirectSpaceState;
            var query = new PhysicsShapeQueryParameters2D
            {
                Shape = new CircleShape2D { Radius = EntityData.AreaEffectRadius },
                Transform = GlobalTransform, // Query centered at the projectile's impact position
                CollideWithAreas = true, // Adjust as needed
                CollideWithBodies = true  // Adjust as needed
                                        // CollisionMask can be set if you have specific layers for damageable entities
            };

            var results = spaceState.IntersectShape(query);

            foreach (var result in results)
            {
                if (result.TryGetValue("collider", out var colliderVariant) && colliderVariant.Obj is Node2D collidedNode)
                {
                    if (collidedNode == this || collidedNode == Source) continue;

                    var targetBattleEntity = collidedNode as BaseBattleEntity;
                    var damageableTarget = collidedNode as IDamageable;

                    switch (EntityData.AreaEffectType)
                    {
                        case ProjectileAreaEffectType.RadialDamage:
                            if (damageableTarget != null)
                            {
                                bool applyDamage = true;
                                if (targetBattleEntity != null && !IsHostileTo(targetBattleEntity))
                                {
                                    applyDamage = false; // Don't damage friendlies in AoE
                                }

                                if (applyDamage)
                                {
                                    // Simple linear falloff example
                                    float distance = GlobalPosition.DistanceTo(collidedNode.GlobalPosition);
                                    float damageMultiplier = Mathf.Clamp(1.0f - (distance / EntityData.AreaEffectRadius), 0.0f, 1.0f);
                                    float areaDamage = EntityData.BaseDamage * damageMultiplier; // Or a separate AreaDamage property
                                    
                                    Logger.Debug($"{Name} AOE damaging {collidedNode.Name} for {areaDamage} (falloff: {damageMultiplier:P0}).");
                                    damageableTarget.ApplyDamage(areaDamage, Source);
                                }
                            }
                            break;

                        case ProjectileAreaEffectType.Stun:
                            // TODO: Implement stun logic if target has a Stunnable component/interface
                            Logger.Debug($"{Name} AOE stunning {collidedNode.Name}.");
                            break;

                        case ProjectileAreaEffectType.Pushback:
                            // TODO: Implement pushback logic if target has a Movable component/interface
                            Logger.Debug($"{Name} AOE pushing back {collidedNode.Name}.");
                            break;
                    }
                }
            }
        }
    }
}
