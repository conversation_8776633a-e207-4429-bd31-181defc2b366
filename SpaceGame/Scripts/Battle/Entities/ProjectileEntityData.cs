using Godot;
using System;
using System.Text.Json;
using SpaceGame.Scripts.Battle.Data;
using SpaceGame.Scripts.Battle;
using SpaceGame.Utility.Interfaces;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Battle.Entities
{
    public enum ProjectileType
    {
        Bullet,
        Missile,
        LaserBeam // Example, add more as needed
    }

    public enum ProjectileDestructionBehavior
    {
        DestroyOnImpact,      // Projectile is destroyed immediately upon hitting something.
        DestroyOnImpactWithTarget, // Projectile is destroyed only if it impacts a valid target (hostile, damageable).
        PassThroughAndDestroyOnLifetimeEnd, // Projectile passes through objects and is destroyed when lifetime ends.
        ExplodeOnImpact,      // Projectile explodes, potentially causing area damage, then is destroyed.
        PierceAndContinue    // Projectile damages target and continues, perhaps with reduced damage or limited pierces.
    }

    public enum ProjectileAreaEffectType
    {
        None,            // No area effect
        RadialDamage,    // Applies damage in a radius, possibly with falloff
        Stun,            // Applies a stun effect in a radius
        Pushback         // Applies a pushback force in a radius
    }

    [GlobalClass]
    public partial class ProjectileEntityData : BaseBattleEntityData, IJsonPopulatable
    {
        [Export]
        public float Speed { get; set; } = 600.0f;

        [Export]
        public float Lifetime { get; set; } = 2.0f; // In seconds

        [Export]
        public float BaseDamage { get; set; } = 10.0f;

        [Export]
        public ProjectileType ProjectileType { get; set; } = ProjectileType.Bullet;

        [Export]
        public float AreaEffectRadius { get; set; } = 0.0f; // Radius for area effect damage, 0 means no area effect.

        [Export]
        public ProjectileAreaEffectType AreaEffectType { get; set; } = ProjectileAreaEffectType.None;

        [Export]
        public ProjectileDestructionBehavior DestructionBehavior { get; set; } = ProjectileDestructionBehavior.DestroyOnImpact;
        
        /// <summary>
        /// The type of damage this projectile deals.
        /// </summary>
        [Export]
        public DamageType DamageType { get; set; } = DamageType.Kinetic;

        /// <summary>
        /// Optional scene to instantiate on impact (e.g., explosion effect).
        /// </summary>
        [Export]
        public PackedScene? ImpactEffectScene { get; set; }

        public ProjectileEntityData()
        {
            // Initialize BaseType for projectiles
            BaseType = BaseEntityType.Projectile;
        }

        public ProjectileEntityData(ProjectileEntityData other) : base(other)
        {
            if (other == null) return;

            Speed = other.Speed;
            Lifetime = other.Lifetime;
            BaseDamage = other.BaseDamage;
            ProjectileType = other.ProjectileType;
            AreaEffectRadius = other.AreaEffectRadius;
            AreaEffectType = other.AreaEffectType;
            DestructionBehavior = other.DestructionBehavior;
            DamageType = other.DamageType;
            ImpactEffectScene = other.ImpactEffectScene;
        }

        public override BaseBattleEntityData Clone(bool generateNewId = true)
        {
            var clone = new ProjectileEntityData(this);
            if (generateNewId)
            {
                clone.Id = System.Guid.NewGuid().ToString();
            }
            return clone;
        }
        
        /// <summary>
        /// Populates this projectile entity's properties from JSON data.
        /// </summary>
        /// <param name="jsonOverrides">The JSON element containing property overrides</param>
        /// <param name="serializerOptions">Options for JSON serialization/deserialization</param>
        public void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)
        {
            try
            {
                // Handle simple properties
                foreach (var property in jsonOverrides.EnumerateObject())
                {
                    string propertyName = property.Name;
                    JsonElement propertyValue = property.Value;
                    
                    string propNameLower = propertyName.ToLowerInvariant();
                    
                    // Using nameof for property names to make them refactoring-safe
                    
                    // Base class properties
                    if (propNameLower == nameof(Id).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String)
                            Id = propertyValue.GetString();
                    }
                    else if (propNameLower == nameof(Name).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String)
                            Name = propertyValue.GetString();
                    }
                    else if (propNameLower == nameof(BaseType).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            BaseType = JsonSerializer.Deserialize<BaseEntityType>(propertyValue.GetRawText(), serializerOptions);
                    }
                    
                    // ProjectileEntityData specific properties
                    else if (propNameLower == nameof(Speed).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            Speed = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(Lifetime).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            Lifetime = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(BaseDamage).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            BaseDamage = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(ProjectileType).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            ProjectileType = JsonSerializer.Deserialize<ProjectileType>(propertyValue.GetRawText(), serializerOptions);
                    }
                    else if (propNameLower == nameof(AreaEffectRadius).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.Number)
                            AreaEffectRadius = propertyValue.GetSingle();
                    }
                    else if (propNameLower == nameof(AreaEffectType).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            AreaEffectType = JsonSerializer.Deserialize<ProjectileAreaEffectType>(propertyValue.GetRawText(), serializerOptions);
                    }
                    else if (propNameLower == nameof(DestructionBehavior).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            DestructionBehavior = JsonSerializer.Deserialize<ProjectileDestructionBehavior>(propertyValue.GetRawText(), serializerOptions);
                    }
                    else if (propNameLower == nameof(DamageType).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String || propertyValue.ValueKind == JsonValueKind.Number)
                            DamageType = JsonSerializer.Deserialize<DamageType>(propertyValue.GetRawText(), serializerOptions);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error populating ProjectileEntityData from JSON: {ex.Message}", ex);
            }
        }
    }
}
