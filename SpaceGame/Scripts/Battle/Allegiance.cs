namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Defines the different allegiance types for battle entities.
/// </summary>
public enum Allegiance
{
    /// <summary>
    /// Represents forces controlled by the player.
    /// </summary>
    PlayerForces = 0,
    
    /// <summary>
    /// Represents forces hostile to the player.
    /// </summary>
    EnemyForces = 1,
    
    /// <summary>
    /// Represents forces that are neutral to both player and enemy.
    /// </summary>
    NeutralForces = 2
}
