using System.Collections.Generic;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Battle;

/// <summary>
/// Manages relationships between different allegiances in the battle system.
/// Provides methods to check if allegiances are friendly, hostile, or neutral to each other.
/// </summary>
public class AllegianceManager
{
    
    private static AllegianceManager? _instance;
        
    /// <summary>
    /// Gets the singleton instance of the GameManagerFacade.
    /// </summary>
    public static AllegianceManager Instance
    {
        get
        {
            if (_instance == null)
            {
                Logger.Info("AllegianceManager: Creating singleton instance");
                _instance = new AllegianceManager();
            }
            return _instance;
        }
    }

    /// <summary>
    /// Defines the possible relationship types between allegiances.
    /// </summary>
    public enum RelationshipType
    {
        Friendly,
        Neutral,
        Hostile
    }
    
    // Dictionary to store relationships between allegiances
    private readonly Dictionary<(Allegiance, Allegiance), RelationshipType> relationships = new();
    
    // Event for relationship changes
    public delegate void RelationshipChangedEventHandler(Allegiance first, Allegiance second, RelationshipType newRelationship);
    public static event RelationshipChangedEventHandler RelationshipChanged;

    public AllegianceManager()
    {
        InitializeDefaultRelationships();
    }
    
    /// <summary>
    /// Initializes the default relationships between allegiances.
    /// </summary>
    private void InitializeDefaultRelationships()
    {
        // Player forces are friendly to themselves
        SetRelationship(Allegiance.PlayerForces, Allegiance.PlayerForces, RelationshipType.Friendly);
        
        // Enemy forces are friendly to themselves
        SetRelationship(Allegiance.EnemyForces, Allegiance.EnemyForces, RelationshipType.Friendly);
        
        // Neutral forces are friendly to themselves
        SetRelationship(Allegiance.NeutralForces, Allegiance.NeutralForces, RelationshipType.Friendly);
        
        // Player and enemy forces are hostile to each other
        SetRelationship(Allegiance.PlayerForces, Allegiance.EnemyForces, RelationshipType.Hostile);
        
        // Player and neutral forces are neutral to each other
        SetRelationship(Allegiance.PlayerForces, Allegiance.NeutralForces, RelationshipType.Neutral);
        
        // Enemy and neutral forces are neutral to each other
        SetRelationship(Allegiance.EnemyForces, Allegiance.NeutralForces, RelationshipType.Neutral);
    }
    
    /// <summary>
    /// Sets the relationship between two allegiances.
    /// </summary>
    /// <param name="first">The first allegiance.</param>
    /// <param name="second">The second allegiance.</param>
    /// <param name="relationship">The relationship type to set.</param>
    public void SetRelationship(Allegiance first, Allegiance second, RelationshipType relationship)
    {
        // Store relationship in both directions to ensure consistency
        relationships[(first, second)] = relationship;
        relationships[(second, first)] = relationship;
        
        // Notify listeners about the relationship change
        RelationshipChanged?.Invoke(first, second, relationship);
    }
    
    /// <summary>
    /// Gets the relationship between two allegiances.
    /// </summary>
    /// <param name="first">The first allegiance.</param>
    /// <param name="second">The second allegiance.</param>
    /// <returns>The relationship type between the allegiances.</returns>
    public RelationshipType GetRelationship(Allegiance first, Allegiance second)
    {
        // Check if the relationship is defined
        if (relationships.TryGetValue((first, second), out RelationshipType relationship))
        {
            return relationship;
        }
        
        // Default to neutral if not defined
        return RelationshipType.Neutral;
    }
    
    /// <summary>
    /// Checks if two allegiances are friendly to each other.
    /// </summary>
    /// <param name="first">The first allegiance.</param>
    /// <param name="second">The second allegiance.</param>
    /// <returns>True if the allegiances are friendly, false otherwise.</returns>
    public bool AreFriendly(Allegiance first, Allegiance second)
    {
        return GetRelationship(first, second) == RelationshipType.Friendly;
    }
    
    /// <summary>
    /// Checks if two allegiances are hostile to each other.
    /// </summary>
    /// <param name="first">The first allegiance.</param>
    /// <param name="second">The second allegiance.</param>
    /// <returns>True if the allegiances are hostile, false otherwise.</returns>
    public bool AreHostile(Allegiance first, Allegiance second)
    {
        return GetRelationship(first, second) == RelationshipType.Hostile;
    }
    
    /// <summary>
    /// Checks if two allegiances are neutral to each other.
    /// </summary>
    /// <param name="first">The first allegiance.</param>
    /// <param name="second">The second allegiance.</param>
    /// <returns>True if the allegiances are neutral, false otherwise.</returns>
    public bool AreNeutral(Allegiance first, Allegiance second)
    {
        return GetRelationship(first, second) == RelationshipType.Neutral;
    }
}
