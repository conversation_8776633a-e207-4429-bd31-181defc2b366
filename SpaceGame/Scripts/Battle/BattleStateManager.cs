using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;

namespace SpaceGame.Scripts.Battle
{
    public class BattleStateManager
    {
        private static BattleStateManager? _instance;
        public static BattleStateManager Instance => _instance ??= new BattleStateManager();
        
        /// <summary>
        /// The current active battle state.
        /// </summary>
        private BattleState? _currentBattleState;

        private BattleStateManager()
        {
            Logger.Debug("BattleStateManager ctor");
            
            // Subscribe to battle events
            BattleEvents.BattleStarted += OnBattleStarted;
            BattleEvents.BattleCompleted += OnBattleCompleted;
            
            Logger.Info("BattleStateManager initialized");
        }

        /// <summary>
        /// Called when a battle is started.
        /// </summary>
        /// <param name="config">The battle configuration.</param>
        private void OnBattleStarted(BattleConfig config)
        {
            Logger.Debug("Battle started");
            // Create a new battle state if one doesn't exist
            if (_currentBattleState == null)
            {
                _currentBattleState = new BattleState();
            }
            
            Logger.Debug("Battle started, state initialized");
        }
        
        /// <summary>
        /// Called when a battle is completed.
        /// </summary>
        /// <param name="playerVictory">Whether the player won the battle.</param>
        private void OnBattleCompleted(bool playerVictory)
        {
            Logger.Debug("Battle completed");
            if (_currentBattleState != null)
            {
                // Update battle state with result
                _currentBattleState.IsCompleted = true;
                _currentBattleState.PlayerVictory = playerVictory;
                
                Logger.Debug($"Battle completed, player victory: {playerVictory}");
            }
        }
        
        
        /// <summary>
        /// Gets the current battle state.
        /// </summary>
        /// <returns>The current battle state.</returns>
        public BattleState? GetCurrentBattleState()
        {
            return _currentBattleState;
        }
        
        /// <summary>
        /// Sets the current battle state.
        /// </summary>
        /// <param name="state">The battle state to set.</param>
        public void SetCurrentBattleState(BattleState state)
        {
            _currentBattleState = state;
        }
    }
}
