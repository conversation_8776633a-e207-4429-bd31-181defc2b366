namespace SpaceGame.Scripts.Battle.Interfaces
{
    /// <summary>
    /// Interface for entities that can take damage.
    /// </summary>
    public interface IDamageable
    {
        /// <summary>
        /// Applies damage to this entity.
        /// </summary>
        /// <param name="amount">The amount of damage to apply.</param>
        /// <param name="damageSource">The entity that caused the damage (optional).</param>
        void ApplyDamage(float amount, Entities.BaseBattleEntity? damageSource = null);
    }
}