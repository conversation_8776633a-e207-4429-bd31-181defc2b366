using Godot;
using System;
using System.Collections.Generic;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Battle.Controllers;
using SpaceGame.Scripts.Battle.Components;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.Battle.Registry;
using System.Linq;

namespace SpaceGame.Scripts.Battle
{
    /// <summary>
    /// Main scene for space battles. Acts as a container for battle elements and manages boundaries.
    /// State and entity spawning are handled by BattleManager and BattleCoordinator.
    /// </summary>
    public partial class SpaceBattleScene : Node2D
    {
        /// <summary>
        /// Signal emitted when the player requests to exit the battle (e.g., via a UI button).
        /// </summary>
        [Signal] public delegate void ExitRequestedEventHandler();

        /// <summary>
        /// The width of the battle area.
        /// </summary>
        [Export]
        public float BattleAreaWidth { get; set; } = 4000.0f;

        /// <summary>
        /// The height of the battle area.
        /// </summary>
        [Export]
        public float BattleAreaHeight { get; set; } = 3000.0f;

        /// <summary>
        /// Whether to show the battle area boundaries.
        /// </summary>
        [Export]
        public bool ShowBoundaries { get; set; } = true;

        /// <summary>
        /// The color of the battle area boundaries.
        /// </summary>
        [Export]
        public Color BoundaryColor { get; set; } = new Color(1.0f, 0.0f, 0.0f, 0.5f);

        /// <summary>
        /// The camera controller for the battle scene. Should be assigned externally or found.
        /// </summary>
        [Export]
        public Camera2D? BattleCamera { get; set; } // Made nullable

        /// <summary>
        /// The container for all battle entities. Should be assigned externally or found.
        /// </summary>
        [Export]
        public Node2D? EntitiesContainer { get; set; } // Made nullable

        private BattleEntityTracker _entityTracker => BattleEntityTracker.Instance;
        
        /// <summary>
        /// Called when the node enters the scene tree for the first time.
        /// Simplified: Primarily ensures essential nodes are present.
        /// </summary>
        public override void _Ready()
        {
            Logger.Info("SpaceBattleScene Ready.");

            // Ensure essential nodes exist, but don't initialize the battle logic here.
            if (BattleCamera == null)
            {
                BattleCamera = GetNodeOrNull<Camera2D>("Camera2D");
                if (BattleCamera == null) Logger.Warning("BattleCamera not found in SpaceBattleScene.");
            }

            if (EntitiesContainer == null)
            {
                EntitiesContainer = GetNodeOrNull<Node2D>("Entities");
                if (EntitiesContainer == null) Logger.Error("EntitiesContainer not found in SpaceBattleScene!");
            }

            // Request redraw if boundaries are shown
            if (ShowBoundaries)
            {
                QueueRedraw();
            }
        }

        /// <summary>
        /// Called every frame. Simplified: Only handles camera updates if needed.
        /// </summary>
        /// <param name="delta">The elapsed time since the previous frame.</param>
        public override void _Process(double delta)
        {
            // Process input for entity switching
            EntityControlManager.ProcessInput();
            
            // Camera following logic might be better placed elsewhere (e.g., a dedicated CameraController script)
            // or driven by the BattleManager/PlayerEntity if needed.
            // For now, keep simple camera update if a player ship exists (found dynamically).
            var playerShip = _entityTracker.GetPlayerShip();
            if (playerShip != null && BattleCamera != null)
            {
                UpdateCameraPosition(playerShip.Position);
            }
        }

        /// <summary>
        /// Called when the node is about to be drawn.
        /// </summary>
        public override void _Draw()
        {
            // Draw battle area boundaries if enabled
            if (ShowBoundaries)
            {
                DrawBattleAreaBoundaries();
            }
        }

        /// <summary>
        /// Draws the battle area boundaries.
        /// </summary>
        private void DrawBattleAreaBoundaries()
        {
            // Calculate the rectangle for the battle area
            Vector2 topLeft = new Vector2(-BattleAreaWidth / 2, -BattleAreaHeight / 2);
            Vector2 size = new Vector2(BattleAreaWidth, BattleAreaHeight);
            Rect2 battleArea = new Rect2(topLeft, size);

            // Draw the rectangle
            DrawRect(battleArea, BoundaryColor, false);
        }

        /// <summary>
        /// Registers an entity with the battle scene and adds it to the EntitiesContainer.
        /// Also registers player entities with the EntityControlManager.
        /// </summary>
        /// <param name="entity">The entity to register.</param>
        public void RegisterEntity(BaseBattleEntity entity)
        {
            if (entity == null)
            {
                Logger.Warning("Attempted to register null entity in SpaceBattleScene.");
                return;
            }
            
            // Add to the entities container if not already there
            if (EntitiesContainer != null)
            {
                var nodeParent = entity.GetParent();
                if (nodeParent == null)
                {
                    EntitiesContainer.AddChild(entity, true);
                } else if (entity.GetParent() != EntitiesContainer)
                {
                    entity.Reparent(EntitiesContainer);
                }
            }
            
            Logger.Debug($"Registered entity {entity.Name} with SpaceBattleScene.");
        }

        /// <summary>
        /// Unregisters an entity from the battle scene.
        /// Note: This usually happens when the entity is destroyed/removed from the tree.
        /// </summary>
        /// <param name="entity">The entity to unregister.</param>
        public void UnregisterEntity(BaseBattleEntity entity)
        {
            if (entity == null)
            {
                return;
            }
            
            // If this is a player-controlled entity, unregister it from the EntityControlManager
            if (entity.CurrentAllegiance == Allegiance.PlayerForces)
            {
                if (entity is ShipEntity playerShip)
                {
                    // Unregister from the control manager
                    EntityControlManager.UnregisterControllableEntity(playerShip);
                }
            }
            
            // Note: No need to unregister from BattleEntityTracker here
            // Entities will unregister themselves in their _ExitTree method
        }

        /// <summary>
        /// Updates the camera position. Can be called externally or by _Process.
        /// </summary>
        private void UpdateCameraPosition(Vector2 targetPosition)
        {
            // Smoothly move the camera to follow the target position
            // Consider adding smoothing (Lerp) if desired
            if (BattleCamera != null)
            {
                BattleCamera.Position = targetPosition;
            }
        }

        /// <summary>
        /// Checks if a position is within the battle area boundaries.
        /// </summary>
        /// <param name="position">The position to check.</param>
        /// <returns>True if the position is within the boundaries, false otherwise.</returns>
        public bool IsWithinBoundaries(Vector2 position)
        {
            float halfWidth = BattleAreaWidth / 2;
            float halfHeight = BattleAreaHeight / 2;

            return position.X >= -halfWidth && position.X <= halfWidth &&
                   position.Y >= -halfHeight && position.Y <= halfHeight;
        }

        /// <summary>
        /// Constrains a position to be within the battle area boundaries.
        /// </summary>
        /// <param name="position">The position to constrain.</param>
        /// <returns>The constrained position.</returns>
        public Vector2 ConstrainToBoundaries(Vector2 position)
        {
            float halfWidth = BattleAreaWidth / 2;
            float halfHeight = BattleAreaHeight / 2;

            return new Vector2(
                Mathf.Clamp(position.X, -halfWidth, halfWidth),
                Mathf.Clamp(position.Y, -halfHeight, halfHeight)
            );
        }

        /// <summary>
        /// Called by UI or other systems to signal the desire to exit the battle.
        /// Emits the ExitRequested signal.
        /// </summary>
        public void RequestExitBattle()
        {
            Logger.Info("Exit requested from SpaceBattleScene.");
            EmitSignal(SignalName.ExitRequested);

            // Removed direct call to GameStateManager
            // The BattleCoordinator should listen to ExitRequested and handle the transition.
        }
    }
}
