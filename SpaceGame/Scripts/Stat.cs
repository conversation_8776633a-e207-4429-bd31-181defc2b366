namespace SpaceGame.Scripts;

public enum Stat
{
    // --- Ship Core Stats (Indices 0-9) ---
    MaxHealth = 0,
    Armor = 1,
    Shield = 2,          // Base shield HP provided by hull/generator
    ShieldRegen = 3,     // Base shield regen
    Speed = 4,
    TurnRate = 5,
    MaxEnergy = 6,       // Base energy pool
    EnergyRegen = 7,     // Base energy regen
    SensorRange = 8,     // Base detection range
    CargoCapacity = 9,   // Example

    // --- Weapon Stats (Indices 10-14) ---
    WeaponDamagePerShot = 10,
    WeaponFireRate = 11,
    WeaponRange = 12,
    WeaponProjectileSpeed = 13,
    WeaponAccuracy = 14,
    // Add other weapon stats like ArmorPenetration, ShieldDamageMult...

    // --- System Stats (Indices 15-27) ---
    SystemCloakEnergyDrain = 15, // Energy cost/sec for cloak
    SystemCloakStealthValue = 16,// Effectiveness of cloak (e.g., 0-1 detection chance mod)
    SystemCloakSpeedModifier = 17,// Speed multiplier while cloaked
    SystemShieldBoostAmount = 18, // Flat or % shield healed by booster
    SystemShieldBoostCost = 19,   // Energy cost of shield boost activation
    SystemShieldBoostCooldown = 20,// Cooldown after using booster
    SystemSensorBoostRange = 21,  // Additive/Multiplicative range bonus from sensor array
    SystemSensorBoostCost = 22,   // Energy cost for sensor boost
    SystemRepairRate = 23,       // HP/sec repaired by drones
    SystemRepairEnergyCost = 24, // Energy cost for repairs
    SystemTractorStrength = 25,  // Force applied by tractor beam
    SystemTractorRange = 26,     // Range of tractor beam
    SystemTractorCost = 27       // Energy cost for tractor
    // Add other system-specific stats...
}