using System;
using System.Collections.Generic;
using Godot;
using SpaceGame.Helpers;

namespace SpaceGame.Scripts.Serialization.Blueprint;

/// <summary>
/// Singleton manager for loading and caching blueprint objects.
/// </summary>
public class BlueprintManager
{
    private const string CommanderBlueprintPath = "res://SpaceGame/Data/Commanders";
    private const string BonusBlueprintPath = "res://SpaceGame/Data/Bonuses";
    private const string SystemsBlueprintPath = "res://SpaceGame/Data/Systems";
    
    private static BlueprintManager? _instance;
    
    /// <summary>
    /// Gets the singleton instance of the BlueprintManager.
    /// </summary>
    public static BlueprintManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new BlueprintManager();
                Logger.Info("BlueprintManager singleton created");
            }
            return _instance;
        }
    }
    
    // Cache of loaded blueprints by type and ID
    private readonly Dictionary<Type, Dictionary<string, IBlueprint>> _blueprintCache = new();
    
    // Private constructor to enforce singleton pattern
    private BlueprintManager()
    {
    }
    
    public List<CommanderBlueprint> GetCommanderBlueprints()
    {
        return LoadBlueprints<CommanderBlueprint>(CommanderBlueprintPath);
    }
    
    public List<SystemBlueprint> GetSystemBlueprints()
    {
        return LoadBlueprints<SystemBlueprint>(SystemsBlueprintPath);
    }

    private List<string> GetBlueprintPaths(string directoryPath)
    {            
        var blueprintPaths = new List<string>();
        var dir = DirAccess.Open(directoryPath);
        if (dir != null)
        {
            dir.ListDirBegin();
            string fileName = dir.GetNext();
                
            while (!string.IsNullOrEmpty(fileName))
            {
                if (!dir.CurrentIsDir() && fileName.EndsWith(".tres"))
                {
                    string fullPath = $"{directoryPath}/{fileName}";
                    blueprintPaths.Add(fullPath);
                }
                fileName = dir.GetNext();
            }
                
            dir.ListDirEnd();
        }
        
        return blueprintPaths;
    }

    private List<T> LoadBlueprints<T>(string path) where T : Resource, IBlueprint
    {
        var blueprints = new List<T>();
        var blueprintPaths = GetBlueprintPaths(path);
        foreach (var blueprintPath in blueprintPaths)
        {
            var blueprint = LoadBlueprint<T>(blueprintPath, true);
            if (blueprint != null)
            {
                blueprints.Add(blueprint);
            }
        }

        return blueprints;
    }
    
    /// <summary>
    /// Loads a blueprint from a resource path.
    /// </summary>
    /// <typeparam name="T">The type of blueprint to load.</typeparam>
    /// <param name="resourcePath">The path to the resource.</param>
    /// <param name="useCache">Whether to use the cache. If true, the blueprint will be cached for future use.</param>
    /// <returns>The loaded blueprint, or null if loading failed.</returns>
    public T? LoadBlueprint<T>(string resourcePath, bool useCache = true) where T : Resource, IBlueprint
    {
        var type = typeof(T);
        
        // Check if the blueprint is already in the cache
        if (useCache && _blueprintCache.TryGetValue(type, out var typeCache))
        {
            if (typeCache.TryGetValue(resourcePath, out var cachedBlueprint))
            {
                return (T)cachedBlueprint;
            }
        }
        
        // Load the blueprint from the resource
        try
        {
            if (!ResourceLoader.Exists(resourcePath))
            {
                Logger.Error($"Blueprint resource does not exist: {resourcePath}");
                return null;
            }
            
            Logger.Debug($"Blueprint resource exists: {resourcePath}. Attempting to load for type {typeof(T).Name}");
            var blueprint = ResourceLoader.Load<T>(resourcePath, cacheMode: ResourceLoader.CacheMode.IgnoreDeep);
            if (blueprint == null)
            {
                Logger.Error($"Failed to load blueprint resource: {resourcePath}");
                return null;
            }
            
            if (string.IsNullOrEmpty(blueprint.BlueprintId))
            {
                blueprint.BlueprintId = resourcePath;
            }
            
            // Add to cache if requested
            if (useCache)
            {
                CacheBlueprint(blueprint);
            }
            
            return blueprint;
        }
        catch (Exception ex)
        {
            Logger.Error($"Error loading blueprint {resourcePath}: {ex.Message}", ex);
            return null;
        }
    }
    
    /// <summary>
    /// Caches a blueprint for future use.
    /// </summary>
    /// <param name="blueprint">The blueprint to cache.</param>
    private void CacheBlueprint(IBlueprint blueprint)
    {
        var type = blueprint.GetType();
        
        // Ensure the type dictionary exists
        if (!_blueprintCache.TryGetValue(type, out var typeCache))
        {
            typeCache = new Dictionary<string, IBlueprint>();
            _blueprintCache[type] = typeCache;
        }
        
        // Add or update the blueprint in the cache
        typeCache[blueprint.BlueprintId] = blueprint;
        Logger.Debug($"Cached blueprint {blueprint.BlueprintId} of type {type.Name}");
    }
    
    /// <summary>
    /// Gets a blueprint from the cache by ID and type.
    /// </summary>
    /// <typeparam name="T">The type of blueprint to get.</typeparam>
    /// <param name="blueprintId">The ID of the blueprint.</param>
    /// <returns>The cached blueprint, or null if not found.</returns>
    public T GetBlueprint<T>(string blueprintId) where T : IBlueprint
    {
        var type = typeof(T);
        
        // Check if the blueprint is in the cache
        if (_blueprintCache.TryGetValue(type, out var typeCache))
        {
            if (typeCache.TryGetValue(blueprintId, out var blueprint))
            {
                return (T)blueprint;
            }
        }
        
        Logger.Warning($"Blueprint {blueprintId} of type {type.Name} not found in cache");
        return default;
    }
    
    /// <summary>
    /// Clears the blueprint cache.
    /// </summary>
    public void ClearCache()
    {
        _blueprintCache.Clear();
        Logger.Info("Blueprint cache cleared");
    }
    
    /// <summary>
    /// Resets the BlueprintManager state for a new game or when loading a game.
    /// </summary>
    public void Reset()
    {
        ClearCache();
        Logger.Info("BlueprintManager reset");
    }
}