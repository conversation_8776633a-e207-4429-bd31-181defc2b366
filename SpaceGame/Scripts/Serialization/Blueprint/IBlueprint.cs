using Godot;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts.Serialization.Blueprint;

/// <summary>
/// Interface for all blueprint objects.
/// Blueprints are Resource-based definitions that define the base properties of game objects.
/// They are loaded from files and used to create runtime instances (IGameData objects).
/// </summary>
public interface IBlueprint
{
    /// <summary>
    /// Gets the unique identifier for this blueprint.
    /// Typically matches the resource path or a specific ID field.
    /// </summary>
    string BlueprintId { get; set; }
    
    /// <summary>
    /// Gets the display name of this blueprint.
    /// </summary>
    string DisplayName { get; }
    
    /// <summary>
    /// Creates a new data instance from this blueprint.
    /// </summary>
    /// <returns>A new instance of GameDataBase created from this blueprint.</returns>
    GameDataBase CreateInstance();
} 