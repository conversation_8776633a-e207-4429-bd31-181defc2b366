using System;
using SpaceGame.Scripts.Serialization.Blueprint;
using SpaceGame.CodeGeneration;
    
namespace SpaceGame.Scripts;

using Godot;

public class SystemData : GameDataBase
{
    // These properties are now inherited from GameDataBase
    // and don't need to be redeclared

    public string SystemName { get; set; } = "Default System";
    public SystemType Type { get; set; } = SystemType.None;

    // Base stats provided by this specific system module
    public SystemStats BaseStats { get; set; } = new SystemStats();

    public SystemData() : base()
    {
        // Default constructor required for serialization
    }

    public SystemData(SystemBlueprint blueprint) : base(blueprint.BlueprintId, blueprint.SystemName)
    {
        SystemName = blueprint.SystemName;
        Type = blueprint.Type;
        BaseStats = new SystemStats(blueprint.BaseStats);
    }
}