namespace SpaceGame.Scripts;

using Godot;
using System;
using System.Collections.Generic; // For List in ToString
using Serialization.Blueprint;
using SpaceGame.CodeGeneration;

// Container for stats related to non-weapon systems/equipment.
// Not a Resource itself, but a data container used by SystemData and FleetShipInstance.

public class SystemStats : GameDataBase
{
    // These properties are now inherited from GameDataBase
    // and don't need to be redeclared

    // --- Cloaking ---
    public float CloakEnergyDrain { get; set; } = 0f;
    public float CloakStealthValue { get; set; } = 0f; // 0 = No cloak effect
    public float CloakSpeedModifier { get; set; } = 1.0f; // 1.0 = No speed change

    // --- Shield Booster ---
    public float ShieldBoostAmount { get; set; } = 0f; // Amount healed
    public float ShieldBoostCost { get; set; } = 0f; // Energy cost
    public float ShieldBoostCooldown { get; set; } = 0f; // Cooldown in seconds

    // --- Sensor Array ---
    public float SensorBoostRange { get; set; } = 0f; // Added range
    public float SensorBoostCost { get; set; } = 0f; // Energy cost

    // --- Repair Drones ---
    public float RepairRate { get; set; } = 0f; // HP/sec
    public float RepairEnergyCost { get; set; } = 0f; // Energy cost/sec

    // --- Tractor Beam ---
    public float TractorStrength { get; set; } = 0f;
    public float TractorRange { get; set; } = 0f;
    public float TractorCost { get; set; } = 0f;

    // Add other system stats fields here...

    public SystemStats() : base()
    {
        // Default constructor for serialization
    }

    // Copy constructor - IMPORTANT: Copy ALL fields
    public SystemStats(SystemStats other) : base()
    {
        if (other == null) return; // Safety check

        // Copy base class properties
        BlueprintId = other.BlueprintId;
        DisplayName = other.DisplayName;

        CloakEnergyDrain = other.CloakEnergyDrain;
        CloakStealthValue = other.CloakStealthValue;
        CloakSpeedModifier = other.CloakSpeedModifier;
        ShieldBoostAmount = other.ShieldBoostAmount;
        ShieldBoostCost = other.ShieldBoostCost;
        ShieldBoostCooldown = other.ShieldBoostCooldown;
        SensorBoostRange = other.SensorBoostRange;
        SensorBoostCost = other.SensorBoostCost;
        RepairRate = other.RepairRate;
        RepairEnergyCost = other.RepairEnergyCost;
        TractorStrength = other.TractorStrength;
        TractorRange = other.TractorRange;
        TractorCost = other.TractorCost;
        // Copy other stats...
    }

    // Copy constructor - IMPORTANT: Copy ALL fields
    public SystemStats(SystemStatsBlueprint other) : base()
    {
        if (other == null) return; // Safety check

        // SystemStatsBlueprint doesn't implement IBlueprint, so we don't need to set BlueprintId or DisplayName
        // We just generate a new ID for this stats object

        CloakEnergyDrain = other.CloakEnergyDrain;
        CloakStealthValue = other.CloakStealthValue;
        CloakSpeedModifier = other.CloakSpeedModifier;
        ShieldBoostAmount = other.ShieldBoostAmount;
        ShieldBoostCost = other.ShieldBoostCost;
        ShieldBoostCooldown = other.ShieldBoostCooldown;
        SensorBoostRange = other.SensorBoostRange;
        SensorBoostCost = other.SensorBoostCost;
        RepairRate = other.RepairRate;
        RepairEnergyCost = other.RepairEnergyCost;
        TractorStrength = other.TractorStrength;
        TractorRange = other.TractorRange;
        TractorCost = other.TractorCost;
        // Copy other stats...
    }

    // String representation showing only non-default values for clarity
    public override string ToString()
    {
        var parts = new List<string>();
        if (CloakStealthValue > 0) parts.Add($"Cloak(Drain:{CloakEnergyDrain:F1},Val:{CloakStealthValue:F2},Spd:{CloakSpeedModifier:F2})");
        if (ShieldBoostAmount > 0) parts.Add($"ShldBoost(Amt:{ShieldBoostAmount:F0},Cost:{ShieldBoostCost:F0},CD:{ShieldBoostCooldown:F1})");
        if (SensorBoostRange != 0) parts.Add($"SensBoost(Rng:{SensorBoostRange:+0;-#},Cost:{SensorBoostCost:F0})"); // Show +/- for range mod
        if (RepairRate > 0) parts.Add($"Repair(Rate:{RepairRate:F1},Cost:{RepairEnergyCost:F1})");
        if (TractorStrength > 0) parts.Add($"Tractor(Str:{TractorStrength:F0},Rng:{TractorRange:F0},Cost:{TractorCost:F0})");

        if (parts.Count == 0) return "Standard"; // Indicate no special system stats
        return string.Join(", ", parts);
    }
}