using Godot;
using System;
using System.Text.Json;
using SpaceGame.Helpers;
using SpaceGame.Utility.Interfaces;

namespace SpaceGame.Scripts.Ships.Data
{
    /// <summary>
    /// Resource defining a weapon slot on a ship, specifying which weapon to mount and where.
    /// </summary>
    [GlobalClass]
    public partial class WeaponSlotData : Resource, IJsonPopulatable
    {
        /// <summary>
        /// Gets or sets the ID of the mount point on the ship where this weapon should be attached.
        /// Corresponds to the Name of a WeaponMount node in the ship scene.
        /// </summary>
        [Export]
        public string MountPointId { get; set; } = "";

        /// <summary>
        /// Gets or sets the ID of the weapon to spawn and attach to this mount point.
        /// This ID is used to look up the weapon definition in the BattleEntityRegistry.
        /// </summary>
        [Export]
        public string WeaponId { get; set; } = "";

        /// <summary>
        /// Gets or sets whether the visual elements of the weapon should be displayed.
        /// Can be used for internal/hidden weapons or debugging.
        /// </summary>
        [Export]
        public bool DisplayVisuals { get; set; } = true;

        /// <summary>
        /// Default constructor.
        /// </summary>
        public WeaponSlotData()
        {
        }
        
        /// <summary>
        /// Creates a duplicate of this weapon slot data.
        /// </summary>
        /// <returns>A new instance with the same properties.</returns>
        public WeaponSlotData Duplicate()
        {
            return new WeaponSlotData
            {
                MountPointId = MountPointId,
                WeaponId = WeaponId,
                DisplayVisuals = DisplayVisuals
            };
        }
        
        /// <summary>
        /// Populates this weapon slot's properties from JSON data.
        /// </summary>
        /// <param name="jsonOverrides">The JSON element containing property overrides</param>
        /// <param name="serializerOptions">Options for JSON serialization/deserialization</param>
        public void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)
        {
            try
            {
                // Handle simple properties
                foreach (var property in jsonOverrides.EnumerateObject())
                {
                    string propertyName = property.Name;
                    JsonElement propertyValue = property.Value;
                    
                    string propNameLower = propertyName.ToLowerInvariant();
                    
                    // Using nameof for property names to make them refactoring-safe
                    if (propNameLower == nameof(MountPointId).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String)
                            MountPointId = propertyValue.GetString();
                    }
                    else if (propNameLower == nameof(WeaponId).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.String)
                            WeaponId = propertyValue.GetString();
                    }
                    else if (propNameLower == nameof(DisplayVisuals).ToLowerInvariant())
                    {
                        if (propertyValue.ValueKind == JsonValueKind.True || propertyValue.ValueKind == JsonValueKind.False)
                            DisplayVisuals = propertyValue.GetBoolean();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Error populating WeaponSlotData from JSON: {ex.Message}", ex);
            }
        }
    }
}
