using Godot;
using SpaceGame.Battle.Registry;
using SpaceGame.Scripts.Battle;          // For WeaponData
using SpaceGame.Scripts.Battle.Data;     // For ShipEntityData, WeaponSlotData
using SpaceGame.Scripts.Battle.Entities; // For BaseBattleEntity, ProjectileEntity
using SpaceGame.Scripts.Managers;
using SpaceGame.Scripts.Ships.Data;      // Potentially for other ship-related data
using System;
using System.Collections.Generic;

namespace SpaceGame.Scripts.Ships
{
    /// <summary>
    /// Controls the weapons attached to a ship.
    /// Manages spawning, attachment and firing of weapons based on the ship's weapon slots configuration.
    /// </summary>
    [GlobalClass]
    public partial class ShipWeaponController : Node
    {
        /// <summary>
        /// The ship entity data that contains weapon slot information.
        /// </summary>
        [Export]
        public ShipEntityData? ShipEntityData { get; private set; }
        
        /// <summary>
        /// Flag to determine whether weapons should be initialized in _Ready().
        /// </summary>
        [Export]
        public bool InitializeWeaponsOnReady { get; set; } = true;
        
        private BattleEntityFactory _battleEntityFactory => BattleEntityFactory.Instance;
        private BattleEntityRegistry _battleEntityRegistry => BattleEntityRegistry.Instance;
        
        /// <summary>
        /// Dictionary of weapon mounts by mount ID.
        /// </summary>
        private readonly Dictionary<string, WeaponMount> _weaponMounts = new();
        
        /// <summary>
        /// Dictionary of active weapons by mount ID.
        /// </summary>
        private readonly Dictionary<string, Node> _activeWeapons = new();

        /// <summary>
        /// Dictionary of WeaponData for mounted weapons by mount ID.
        /// </summary>
        private readonly Dictionary<string, WeaponData> _mountedWeaponData = new();

        /// <summary>
        /// Dictionary to track weapon cooldown end times by mount ID.
        /// Stores timestamp from Time.GetTicksMsec().
        /// </summary>
        private readonly Dictionary<string, ulong> _weaponCooldownEndTimes = new();
        
        public override void _Ready()
        {
            // Initialize weapons if requested
            if (InitializeWeaponsOnReady)
            {
                Logger.Debug("ShipWeaponController: Initializing weapons on ready.");
                InitializeWeapons();
            }
        }
        
        public void UpdateEntityData(ShipEntityData data)
        {
            Logger.Debug($"ShipWeaponController: Updating entity data: {data}");
            ShipEntityData = data;
            InitializeWeapons();
        }
        
        /// <summary>
        /// Initializes weapons for this ship based on ShipEntityData.WeaponSlots.
        /// </summary>
        public void InitializeWeapons()
        {
            if (ShipEntityData == null)
            {
                Logger.Error("ShipWeaponController: Cannot initialize weapons - ShipEntityData is null.");
                return;
            }
            
            if (_battleEntityFactory == null || _battleEntityRegistry == null)
            {
                Logger.Error("ShipWeaponController: Cannot initialize weapons - required services are not available.");
                return;
            }
            
            // Find all weapon mounts in the ship
            var allMounts = FindChildrenByType<WeaponMount>(GetParent());
            
            Logger.Debug($"ShipWeaponController: Found {allMounts.Count} weapon mounts in the ship.");
            
            // Map mounts by their IDs for quick lookup
            _weaponMounts.Clear();
            foreach (var mount in allMounts)
            {
                _weaponMounts[mount.MountId] = mount;
            }
            
            // Process each weapon slot defined in the ship data
            foreach (var slot in ShipEntityData.WeaponSlots)
            {
                MountWeapon(slot);
            }
            
            Logger.Debug($"ShipWeaponController: Initialized {_activeWeapons.Count} weapons out of {ShipEntityData.WeaponSlots.Count} slots defined in ship data.");
        }
        
        /// <summary>
        /// Mounts a weapon based on a weapon slot definition.
        /// </summary>
        /// <param name="slot">The weapon slot data.</param>
        /// <returns>True if the weapon was successfully mounted, false otherwise.</returns>
        public bool MountWeapon(WeaponSlotData slot)
        {
            if (slot == null)
            {
                Logger.Error("ShipWeaponController: Cannot mount weapon - WeaponSlotData is null.");
                return false;
            }
            
            // Find the mount point
            if (!_weaponMounts.TryGetValue(slot.MountPointId, out WeaponMount mount))
            {
                Logger.Error($"ShipWeaponController: Mount point '{slot.MountPointId}' not found on ship '{ShipEntityData?.Name}' Existing mounts: {string.Join(", ", _weaponMounts.Keys)}.");
                return false;
            }
            
            // If there's already a weapon attached to this mount, detach it first
            if (mount.HasWeaponAttached())
            {
                var detachedWeapon = mount.DetachWeapon();
                if (detachedWeapon != null)
                {
                    detachedWeapon.QueueFree();
                    _activeWeapons.Remove(slot.MountPointId);
                }
            }
            
            // Spawn the weapon
            Node? weaponNode = _battleEntityFactory.SpawnWeapon(slot.WeaponId);
            if (weaponNode == null)
            {
                Logger.Error($"ShipWeaponController: Failed to spawn weapon with ID '{slot.WeaponId}'.");
                return false;
            }
            
            // Get the weapon data for attaching
            WeaponData? weaponData = _battleEntityRegistry.GetWeaponData(slot.WeaponId);
            if (weaponData == null)
            {
                Logger.Error($"ShipWeaponController: Failed to get weapon data for ID '{slot.WeaponId}'.");
                weaponNode.QueueFree();
                return false;
            }
            
            _mountedWeaponData[slot.MountPointId] = weaponData; // Store WeaponData

            // Attach the weapon to the mount
            bool success = mount.AttachWeapon(weaponData, weaponNode, slot.DisplayVisuals);
            if (!success)
            {
                Logger.Error($"ShipWeaponController: Failed to attach weapon '{slot.WeaponId}' to mount '{slot.MountPointId}'.");
                weaponNode.QueueFree();
                return false;
            }
            
            // Apply property overrides if available
            ApplyWeaponPropertyOverrides(weaponNode, slot.WeaponId);
            
            // Store the active weapon
            _activeWeapons[slot.MountPointId] = weaponNode;
            
            Logger.Debug($"ShipWeaponController: Successfully mounted weapon '{slot.WeaponId}' on mount '{slot.MountPointId}'. Visual display: {slot.DisplayVisuals}");
            return true;
        }
        
        /// <summary>
        /// Fires the weapon on the specified mount ID if it's ready.
        /// </summary>
        /// <param name="mountId">The ID of the weapon mount to fire.</param>
        /// <returns>True if the weapon was successfully fired, false otherwise.</returns>
        public bool FireWeapon(string mountId)
        {
            if (string.IsNullOrEmpty(mountId))
            {
                Logger.Warning("ShipWeaponController: Mount ID is null or empty. Cannot fire weapon.");
                return false;
            }

            // Check cooldown
            if (_weaponCooldownEndTimes.TryGetValue(mountId, out ulong readyTime) && Time.GetTicksMsec() < readyTime)
            {
                return false; // Weapon is on cooldown
            }

            // Retrieve WeaponMount
            if (!_weaponMounts.TryGetValue(mountId, out WeaponMount weaponMount))
            {
                Logger.Error($"ShipWeaponController: WeaponMount with ID '{mountId}' not found.");
                return false;
            }

            // Retrieve WeaponData
            if (!_mountedWeaponData.TryGetValue(mountId, out WeaponData? storedWeaponData))
            {
                Logger.Error($"ShipWeaponController: WeaponData for mount ID '{mountId}' not found.");
                return false;
            }

            BaseBattleEntity? ownerShip = GetParent<BaseBattleEntity>();
            if (ownerShip == null)
            {
                Logger.Error("ShipWeaponController: Could not get owner ship (BaseBattleEntity parent). Cannot fire weapon.");
                return false;
            }

            // Determine spawn transform (position and rotation)
            // Assumes WeaponMount has GetMuzzleGlobalTransform() or MuzzlePoint.GlobalTransform can be accessed.
            // If MuzzlePoint is not defined on WeaponMount, it might default to weaponMount.GlobalTransform.
            Transform2D spawnTransform = weaponMount.GetMuzzleGlobalTransform();
            ProjectileEntity? projectile = _battleEntityFactory.SpawnProjectileFromWeaponData(
                storedWeaponData, 
                ownerShip, 
                spawnTransform,
                this
            );

            if (projectile != null)
            {
                // Set cooldown
                if (storedWeaponData.FireRate > 0)
                {
                    _weaponCooldownEndTimes[mountId] = Time.GetTicksMsec() + (ulong)((1.0f / storedWeaponData.FireRate) * 1000.0f);
                }
                else
                {
                    _weaponCooldownEndTimes[mountId] = ulong.MaxValue; // Effectively makes it not fire again if fire rate is 0 or negative
                }
                // Logger.Debug($"ShipWeaponController: Fired weapon on mount '{mountId}'. Next ready at {(_weaponCooldownEndTimes[mountId] / 1000.0f):F2}s.");
                return true;
            }
            
            // Logger.Warning($"ShipWeaponController: Failed to spawn projectile for weapon on mount '{mountId}'.");
            return false;
        }
        
        /// <summary>
        /// Applies property overrides to a weapon.
        /// </summary>
        /// <param name="weaponNode">The weapon node to apply overrides to.</param>
        /// <param name="weaponId">The ID of the weapon.</param>
        private void ApplyWeaponPropertyOverrides(Node weaponNode, string weaponId)
        {
            if (weaponNode == null || string.IsNullOrEmpty(weaponId))
            {
                Logger.Warning("ShipWeaponController: Cannot apply property overrides. Weapon node is null or weapon ID is empty.");
                return;
            }
            
            // Get the overrides for this weapon from the registry or configuration system
            // This assumes we're using the PropertyOverrideService to apply the overrides
            // Get the first GodotObject to apply overrides to (typically the script attached to the root node)
            GodotObject targetScript = null;
            if (weaponNode is GodotObject godotObj)
            {
                targetScript = godotObj;
            }
            else
            {
                // If the node itself isn't a GodotObject (which should never happen), log a warning
                Logger.Warning($"ShipWeaponController: Cannot apply property overrides for weapon '{weaponId}'. Node is not a GodotObject.");
                return;
            }
            
            try
            {
                // Use the OverrideManager to apply overrides
                // The category "WeaponType" is used to distinguish weapon overrides in the override system
                if (OverrideManager.Instance != null)
                {
                    // Apply global overrides using the weapon ID as the entity type ID
                    OverrideManager.Instance.ApplyGlobalOverrides(targetScript, weaponId);
                    Logger.Debug($"ShipWeaponController: Applied global overrides to weapon '{weaponId}'.");
                }
                else
                {
                    Logger.Warning("ShipWeaponController: OverrideManager.Instance is null. Cannot apply overrides.");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"ShipWeaponController: Error applying property overrides to weapon '{weaponId}'. {ex.Message}");
            }
        }
        
        /// <summary>
        /// Helper method to find all children of a specific type in a node hierarchy.
        /// </summary>
        /// <typeparam name="T">The type of nodes to find.</typeparam>
        /// <param name="root">The root node to start searching from.</param>
        /// <returns>A list of nodes of the specified type.</returns>
        private List<T> FindChildrenByType<T>(Node root) where T : class
        {
            List<T> result = new List<T>();
            
            void RecursiveFind(Node node)
            {
                foreach (Node child in node.GetChildren())
                {
                    if (child is T typedChild)
                    {
                        result.Add(typedChild);
                    }
                    
                    RecursiveFind(child);
                }
            }
            
            RecursiveFind(root);
            return result;
        }
        
        /// <summary>
        /// Fires all weapons on the ship.
        /// </summary>
        /// <returns>True if at least one weapon was fired, false otherwise.</returns>
        public bool FireAllWeapons()
        {
            if (_activeWeapons.Count == 0)
            {
                Logger.Debug("ShipWeaponController: No active weapons to fire.");
                return false;
            }
            
            bool anyFired = false;
            ulong currentTime = Time.GetTicksMsec();
            
            // Create a copy of the keys to avoid collection modification issues
            var mountIds = new List<string>(_activeWeapons.Keys);
            
            foreach (var mountId in mountIds)
            {
                // Check if the weapon is on cooldown
                if (_weaponCooldownEndTimes.TryGetValue(mountId, out ulong cooldownEndTime) && currentTime < cooldownEndTime)
                {
                    // Weapon is on cooldown, skip it
                    continue;
                }
                
                // Get the weapon data
                if (!_mountedWeaponData.TryGetValue(mountId, out WeaponData weaponData))
                {
                    continue;
                }
                
                // Fire the weapon
                Logger.Debug($"ShipWeaponController: Firing weapon on mount {mountId}.");
                FireWeapon(mountId);
                
                float cooldownSeconds = weaponData.FireRate > 0 ? 1.0f / weaponData.FireRate : 1.0f; // Default cooldown
                _weaponCooldownEndTimes[mountId] = currentTime + (ulong)(cooldownSeconds * 1000);
                
                anyFired = true;
            }
            
            return anyFired;
        }
    }
}
