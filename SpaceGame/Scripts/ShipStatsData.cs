using System;
using SpaceGame.Scripts.Serialization.Blueprint;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts;

/// <summary>
/// Serializable data class for ship stats.
/// This is a plain C# class (not a Resource) that can be serialized to/from JSON.
/// </summary>
public class ShipStatsData : GameDataBase
{
    /// <summary>
    /// Gets or sets the unique identifier for this ship stats instance.
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Gets or sets the blueprint reference ID.
    /// </summary>
    public string BlueprintId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the display name of this ship stats instance.
    /// </summary>
    public string DisplayName { get; set; } = "Ship Stats";

    // Core Stats
    public float MaxHealth { get; set; } = 100f;
    public float Armor { get; set; } = 5f;
    public float Shield { get; set; } = 50f; // Base shield value from ship's generator
    public float ShieldRegen { get; set; } = 2f; // Base shield regen
    public float Speed { get; set; } = 100f;
    public float TurnRate { get; set; } = 0.5f;
    public float MaxEnergy { get; set; } = 100f;
    public float EnergyRegen { get; set; } = 5f;
    public float SensorRange { get; set; } = 1000f; // Base detection range
    public float CargoCapacity { get; set; } = 0f;

    /// <summary>
    /// Default constructor.
    /// </summary>
    public ShipStatsData() { }

    /// <summary>
    /// Constructor that creates a new instance from a blueprint.
    /// </summary>
    /// <param name="blueprint">The blueprint to create from.</param>
    public ShipStatsData(ShipStatsBlueprint blueprint)
        : base(blueprint?.BlueprintId, blueprint?.DisplayName)
    {
        if (blueprint == null) return;

        // Copy core stats
        MaxHealth = blueprint.MaxHealth;
        Armor = blueprint.Armor;
        Shield = blueprint.Shield;
        ShieldRegen = blueprint.ShieldRegen;
        Speed = blueprint.Speed;
        TurnRate = blueprint.TurnRate;
        MaxEnergy = blueprint.MaxEnergy;
        EnergyRegen = blueprint.EnergyRegen;
        SensorRange = blueprint.SensorRange;
        CargoCapacity = blueprint.CargoCapacity;
    }

    /// <summary>
    /// Constructor that creates a copy from another ShipStatsData.
    /// </summary>
    /// <param name="other">The ShipStatsData to copy from.</param>
    public ShipStatsData(ShipStatsData other)
        : base(other?.BlueprintId, other?.DisplayName)
    {
        if (other == null) return;

        // Copy core stats
        MaxHealth = other.MaxHealth;
        Armor = other.Armor;
        Shield = other.Shield;
        ShieldRegen = other.ShieldRegen;
        Speed = other.Speed;
        TurnRate = other.TurnRate;
        MaxEnergy = other.MaxEnergy;
        EnergyRegen = other.EnergyRegen;
        SensorRange = other.SensorRange;
        CargoCapacity = other.CargoCapacity;
    }

    /// <summary>
    /// String representation for debugging.
    /// </summary>
    public override string ToString()
    {
        return $"ShipStatsData[{Id}]: HP:{MaxHealth:F0}, Armor:{Armor:F1}, Shield:{Shield:F0}/{ShieldRegen:F1}, Spd:{Speed:F0}, Turn:{TurnRate:F2}, Energy:{MaxEnergy:F0}/{EnergyRegen:F1}, Sensor:{SensorRange:F0}";
    }
} 