using System;
using SpaceGame.Scripts.Serialization.Blueprint;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts;

using Godot;

// Resource blueprint for a non-weapon system/equipment module.
// Note: Using custom SystemData implementation, not using code generation
public partial class SystemBlueprint : Resource, IBlueprint
{
    public string BlueprintId { get; set; } = Guid.NewGuid().ToString();
    public string DisplayName => SystemName;

    [Export] public string SystemName { get; set; } = "Default System";
    [Export] public SystemType Type { get; set; } = SystemType.None;

    // Base stats provided by this specific system module
    [Export] public SystemStatsBlueprint BaseStats { get; set; } = new();

    // Optional: Visuals, UI Icon, Description
    // [Export] public Texture2D Icon { get; set; }
    // [Export] public string Description { get; set; } = "";

    public SystemBlueprint() { } // Required for Godot Resources
    
    public GameDataBase CreateInstance()
    {
        SystemData data = new SystemData(this);
        return (GameDataBase)data;
    }
}