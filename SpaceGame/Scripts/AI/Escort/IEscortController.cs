using Godot;
using SpaceGame.Scripts.AI.StateMachine;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.AI.Escort
{
    /// <summary>
    /// Interface for controllers that manage escort behavior state machines.
    /// </summary>
    public interface IEscortController : IStateMachineController
    {
        /// <summary>
        /// Called when the escorted entity is lost or destroyed.
        /// </summary>
        void HandleEscortTargetLost();
        
        /// <summary>
        /// Updates information about threats to the escorted entity.
        /// </summary>
        /// <param name="threatEntity">The entity posing a threat.</param>
        /// <param name="isUnderAttack">Whether the escorted entity is under attack.</param>
        void SetThreatInfo(Node2D threatEntity, bool isUnderAttack);
        
        /// <summary>
        /// Updates information about the escorted entity.
        /// </summary>
        /// <param name="entity">The escorted entity.</param>
        /// <param name="offset">The offset from the escorted entity's position.</param>
        void SetEscortTarget(ShipEntity entity, Vector2 offset = default!);
    }
}
