using Godot;
using SpaceGame.Scripts.AI.StateMachine;
using SpaceGame.Scripts.AI.Escort.States;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;

namespace SpaceGame.Scripts.AI.Escort
{
    public partial class EscortStateMachine : StateMachine<IEscortController, EscortStateKey, EscortStateMachine, ShipEntity>
    {
        public ShipEntity? EscortedEntity { get; private set; }
        public Vector2 OffsetFromEscortedEntity { get; private set; }
        public Node2D CurrentThreat { get; private set; }
        public bool IsTargetUnderAttack { get; private set; }
        
        public EscortStateMachine() { }

        // Override Initialize to accept the ShipEntity this state machine controls (selfEntity)
        public override void Initialize(IEscortController ownerController, ShipEntity selfEntity)
        {
            base.Initialize(ownerController, selfEntity);
            // EscortStateMachine-specific initialization can go here if needed
            // Logger.Debug($"{GetType().Name} initialized for {selfEntity.Name}");
        }
        
        protected override EscortStateKey InitialStateKey => EscortStateKey.Wandering;
        
        protected override void RegisterStates()
        {
            AddState(EscortStateKey.Wandering, new WanderingState());
            AddState(EscortStateKey.Following, new FollowingState());
            AddState(EscortStateKey.Protecting, new ProtectingState());
            // Additional states would be registered here
        }
        
        public void SetEscortTarget(ShipEntity targetEntity, Vector2 offset = default!)
        {
            EscortedEntity = targetEntity;
            OffsetFromEscortedEntity = offset;
            Logger.Debug($"{Name}: Escort target set to {targetEntity?.Name ?? "None"}");
            
            // If we're in Wandering state and now have a target, transition to Following
            if (GetCurrentStateKey() == EscortStateKey.Wandering && targetEntity != null)
            {
                EnterState(EscortStateKey.Following);
            }
        }
        
        public void SetThreatInfo(Node2D threatEntity, bool isUnderAttack)
        {
            CurrentThreat = threatEntity;
            IsTargetUnderAttack = isUnderAttack;
            
            // If target is under attack and we're following, consider switching to protecting
            if (isUnderAttack && GetCurrentStateKey() == EscortStateKey.Following)
            {
                // The state's GetNextStateKey will handle this transition in the next update
            }
        }
        
        // Example method called by a state
        public void NotifyEscortTargetLost()
        {
            OwnerController.HandleEscortTargetLost();
        }
    }
}
