using Godot;
using SpaceGame.Scripts.AI.StateMachine;

namespace SpaceGame.Scripts.AI.Escort.States
{
    public partial class FollowingState : StateBase<EscortStateMachine, EscortStateKey>
    {
        private const float DISTANCE_THRESHOLD = 200.0f;
        private const float ANGLE_THRESHOLD_DEGREES = 5.0f; // Allowable deviation for starting movement
        
        private Node2D? _targetEntity;
        
        public FollowingState() {}
        
        public override void Enter()
        {
            _targetEntity = Context.EscortedEntity;
            Logger.Debug($"Entered Following state. Target: {_targetEntity?.Name ?? "None"}");
        }
        
        public override void Exit()
        {
            Context.ControlledNode.MovementController.StopMovement(); // Stop following movement when exiting
            Context.ControlledNode.MovementController.StopRotation(); // Ensure rotation also stops
            Logger.Debug("Exited Following state");
        }
        
        public override void Update(double delta)
        {
            if (Context.EscortedEntity == null)
            {
                Context.NotifyEscortTargetLost();
                return;
            }
            
            // Update target reference in case it changed
            _targetEntity = Context.EscortedEntity;
        }
        
        public override void PhysicsUpdate(double delta)
        {
            if (_targetEntity == null) return;
            
            var selfEntity = Context.ControlledNode;
            var movementController = selfEntity.MovementController;
            var targetPosition = _targetEntity.GlobalPosition + Context.OffsetFromEscortedEntity;
            
            Vector2 directionToTargetVector = (targetPosition - selfEntity.GlobalPosition);

            // If very close to the target, stop active maneuvering.
            if (directionToTargetVector.LengthSquared() < 1.0f) // Using a small threshold (e.g., 1 unit squared)
            {
                movementController.StopMovement();
                // Optionally, could also call StopRotation() if it should hold current orientation when idle at point
                return;
            }

            directionToTargetVector = directionToTargetVector.Normalized();
            
            // Calculate the entity's current forward direction
            // Vector2.Up is used because 0 degrees GlobalRotation means the entity faces 'up'.
            Vector2 currentEntityForward = Vector2.Up.Rotated(selfEntity.GlobalRotation);
            
            // Calculate the relative angle needed to face the target
            float relativeAngleRadians = currentEntityForward.AngleTo(directionToTargetVector);
            float relativeAngleDegrees = Mathf.RadToDeg(relativeAngleRadians);

            // Use the existing currentAngleDegrees and targetAngleDegrees logic for angleDifference
            // This is just for the threshold check, RotateBy will handle the actual rotation calculation.
            float targetAngleForThresholdCheck = Mathf.RadToDeg(directionToTargetVector.Angle());
            float currentAngleDegrees = selfEntity.GlobalRotationDegrees;
            float angleDifference = Mathf.Wrap(targetAngleForThresholdCheck - (currentAngleDegrees - 90.0f), -180f, 180f); // Adjust currentAngle for check

            if (Mathf.Abs(angleDifference) > ANGLE_THRESHOLD_DEGREES)
            {
                // Not facing target: prioritize rotation
                movementController.RotateBy(relativeAngleDegrees);
                movementController.StopMovement();
            }
            else
            {
                // Facing target: prioritize movement
                movementController.MoveTo(targetPosition);
            }
        }
        
        public override EscortStateKey GetNextStateKey()
        {
            // If target is lost, transition to Wandering
            if (_targetEntity == null || Context.EscortedEntity == null)
            {
                return EscortStateKey.Wandering;
            }
            
            // If target is under attack and we're close enough, switch to Protecting
            if (Context.IsTargetUnderAttack && 
                (_targetEntity.GlobalPosition - Context.GetParent<Node2D>().GlobalPosition).Length() < DISTANCE_THRESHOLD)
            {
                return EscortStateKey.Protecting;
            }
            
            // Stay in current state
            return Context.GetCurrentStateKey();
        }
    }
}
