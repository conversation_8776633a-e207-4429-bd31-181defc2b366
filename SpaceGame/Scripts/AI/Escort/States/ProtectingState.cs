using Godot;
using SpaceGame.Scripts.AI.StateMachine;

namespace SpaceGame.Scripts.AI.Escort.States
{
    public partial class ProtectingState : StateBase<EscortStateMachine, EscortStateKey>
    {
        private const float PROTECTION_RADIUS = 250.0f;
        private const float RETURN_THRESHOLD = 400.0f;
        
        private Node2D _targetEntity;
        private Node2D _threatEntity;
        private float _protectionTimer;
        private const float MAX_PROTECTION_TIME = 15.0f; // Max time to stay in protection mode
        
        public ProtectingState() { }
        
        public override void Enter()
        {
            _targetEntity = Context.EscortedEntity;
            _threatEntity = Context.CurrentThreat;
            _protectionTimer = 0;
            Logger.Debug($"Entered Protecting state. Target: {_targetEntity?.Name ?? "None"}, Threat: {_threatEntity?.Name ?? "None"}");
        }
        
        public override void Exit()
        {
            _threatEntity = null;
            Context.ControlledNode.MovementController.FullStop(); // Stop all movement and rotation when exiting
            Logger.Debug("Exited Protecting state");
        }
        
        public override void Update(double delta)
        {
            if (Context.EscortedEntity == null)
            {
                Context.NotifyEscortTargetLost();
                return;
            }
            
            // Update references
            _targetEntity = Context.EscortedEntity;
            _threatEntity = Context.CurrentThreat;
            
            // Increment protection timer
            _protectionTimer += (float)delta;
        }
        
        public override void PhysicsUpdate(double delta)
        {
            var selfEntity = Context.ControlledNode;
            // If we have a threat, position between threat and escorted entity
            if (_threatEntity != null && _targetEntity != null)
            {
                var threatToTarget = _targetEntity.GlobalPosition - _threatEntity.GlobalPosition;
                var interceptPosition = _threatEntity.GlobalPosition + threatToTarget.Normalized() * PROTECTION_RADIUS;
                
                selfEntity.MovementController.MoveTo(interceptPosition);
                
                // Face the threat
                var directionToThreat = _threatEntity.GlobalPosition - selfEntity.GlobalPosition;
                if (directionToThreat.LengthSquared() > 0.01f)
                {
                    selfEntity.MovementController.RotateTowards(directionToThreat.Angle());
                }
                
                // Simulate combat actions here (would call weapon systems, etc.)
                // For now just logging
                if ((_threatEntity.GlobalPosition - selfEntity.GlobalPosition).Length() < PROTECTION_RADIUS)
                {
                    Logger.Debug($"Protecting {_targetEntity.Name} from {_threatEntity.Name}");
                }
            }
            // If no threat but we have a target, stay close to target
            else if (_targetEntity != null)
            {
                var directionToEscorted = _targetEntity.GlobalPosition - selfEntity.GlobalPosition;
                var distance = directionToEscorted.Length();
                
                if (distance > PROTECTION_RADIUS)
                {
                    selfEntity.MovementController.MoveTo(_targetEntity.GlobalPosition);
                }
                else
                {
                    // If close enough, stop active movement towards the escorted entity
                    // but still face it.
                    selfEntity.MovementController.StopMovement(); 
                }
                
                // Face the escorted entity
                if (directionToEscorted.LengthSquared() > 0.01f)
                {
                    selfEntity.MovementController.RotateTowards(directionToEscorted.Angle());
                }
            }
        }
        
        public override EscortStateKey GetNextStateKey()
        {
            // If target is lost, transition to Wandering
            if (_targetEntity == null || Context.EscortedEntity == null)
            {
                return EscortStateKey.Wandering;
            }
            
            // If threat is gone or protection time exceeded, return to Following
            if (_threatEntity == null || Context.CurrentThreat == null || _protectionTimer > MAX_PROTECTION_TIME)
            {
                return EscortStateKey.Following;
            }
            
            // If we're too far from the escorted entity, return to it
            var selfEntity = Context.GetParent<Node2D>();
            if (selfEntity != null && _targetEntity != null)
            {
                var distanceToTarget = (_targetEntity.GlobalPosition - selfEntity.GlobalPosition).Length();
                if (distanceToTarget > RETURN_THRESHOLD)
                {
                    return EscortStateKey.Returning;
                }
            }
            
            // Stay in current state
            return Context.GetCurrentStateKey();
        }
    }
}
