using Godot;
using SpaceGame.Scripts.AI.StateMachine;
using System;

namespace SpaceGame.Scripts.AI.Escort.States
{
    public partial class WanderingState : StateBase<EscortStateMachine, EscortStateKey>
    {
        private const float TARGET_CHANGE_TIME = 3.0f;
        private const float WANDER_RADIUS = 300.0f;
        
        private Vector2 _targetPosition;
        private float _wanderTimer;
        private RandomNumberGenerator _rng = new RandomNumberGenerator();
        
        public WanderingState() { }
        
        public override void Enter()
        {
            _rng.Randomize();
            _wanderTimer = 0;
            SetNewWanderTarget();
            Context.ControlledNode.MovementController.MoveTo(_targetPosition);
            Logger.Debug("Entered Wandering state");
        }
        
        public override void Exit()
        {
            Context.ControlledNode.MovementController.StopMovement(); // Stop any wandering movement when exiting
            Logger.Debug("Exited Wandering state");
        }
        
        public override void Update(double delta)
        {
            _wanderTimer += (float)delta;
            
            // Change wander target periodically
            if (_wanderTimer >= TARGET_CHANGE_TIME)
            {
                SetNewWanderTarget();
                Context.ControlledNode.MovementController.MoveTo(_targetPosition);
                _wanderTimer = 0;
            }
            
            // Check if a new escort target has been assigned
            if (Context.EscortedEntity != null)
            {
                // The GetNextStateKey method will handle the transition
            }
        }
        
        public override void PhysicsUpdate(double delta)
        {
            // Movement is now handled by the BattleEntityMovementController via MoveTo() commands.
            // No direct position manipulation needed here.
        }
        
        public override EscortStateKey GetNextStateKey()
        {
            // If we have a valid escort target, transition to Following
            if (Context.EscortedEntity != null)
            {
                return EscortStateKey.Following;
            }
            
            // Stay in wandering state
            return Context.GetCurrentStateKey();
        }
        
        private void SetNewWanderTarget()
        {
            var selfEntity = Context.GetParent<Node2D>();
            if (selfEntity == null) return;
            
            // Generate a random position within the wander radius
            var randomAngle = _rng.RandfRange(0, Mathf.Pi * 2);
            var randomDistance = _rng.RandfRange(0, WANDER_RADIUS);
            var offset = new Vector2(
                Mathf.Cos(randomAngle) * randomDistance,
                Mathf.Sin(randomAngle) * randomDistance
            );
            
            _targetPosition = selfEntity.GlobalPosition + offset;
            Logger.Debug($"New wander target set: {_targetPosition}");
        }
    }
}
