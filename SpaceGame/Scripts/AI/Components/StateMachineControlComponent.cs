using Godot;
using System;
using System.Collections.Generic;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Battle.Entities;
using SpaceGame.Scripts.AI.Escort;

namespace SpaceGame.Scripts.AI.Components
{
    [GlobalClass]
    public partial class StateMachineControlComponent : EntityControlComponent, IEscortController
    {
        private Dictionary<Type, Node> _registeredStateMachines = new Dictionary<Type, Node>();
        private HashSet<Type> _activeStateMachines = new HashSet<Type>();
        
        public StateMachineControlComponent()
        {
            // State machines will be registered by behavior components in their _Ready methods
        }

        public override void _Ready()
        {
            base._Ready();

            if (_entity == null) 
            {
                Logger.Error($"{Name}: Entity is null in _Ready. StateMachineControlComponent requires a valid BattleEntity.");
                SetProcess(false);
                SetPhysicsProcess(false);
                return;
            }
        }

        protected override void ProcessControl(double delta)
        {
            // State machines are now Godot Nodes that process automatically
            // No need to manually update them here
        }

        public T? GetStateMachine<T>() where T : Node
        {
            var type = typeof(T);
            if (_registeredStateMachines.TryGetValue(type, out var stateMachine))
            {
                return (T)stateMachine;
            }
            return null;
        }

        public void RegisterStateMachine<T>(T stateMachineInstance) where T : Node
        {
            if (stateMachineInstance == null)
            {
                Logger.Error($"{Name}: Attempted to register a null state machine.");
                return;
            }
            var type = typeof(T);
            
            if (_registeredStateMachines.ContainsKey(type))
            {
                Logger.Warning($"{Name}: State machine of type {type.Name} already registered. Replacing.");
                var oldStateMachine = _registeredStateMachines[type];
                if (oldStateMachine.IsInsideTree())
                {
                    RemoveChild(oldStateMachine);
                }
            }
            
            _registeredStateMachines[type] = stateMachineInstance;
            AddChild(stateMachineInstance);
            stateMachineInstance.Owner = Owner;
            Logger.Debug($"{Name}: Registered state machine: {type.Name} ({stateMachineInstance.Name})");
        }

        public void ActivateStateMachine<T>() where T : Node
        {
            var type = typeof(T);
            if (!_registeredStateMachines.TryGetValue(type, out var stateMachine))
            {
                Logger.Warning($"{Name}: Cannot activate state machine of type {type.Name} - not registered.");
                return;
            }
            
            // Add to active set
            _activeStateMachines.Add(type);
            Logger.Debug($"{Name}: Activated state machine: {type.Name}");
        }
        
        public void DeactivateStateMachine<T>() where T : Node
        {
            var type = typeof(T);
            if (!_registeredStateMachines.ContainsKey(type))
            {
                Logger.Warning($"{Name}: Cannot deactivate state machine of type {type.Name} - not registered.");
                return;
            }
            
            // Remove from active set
            _activeStateMachines.Remove(type);
            Logger.Debug($"{Name}: Deactivated state machine: {type.Name}");
        }
        
        public bool IsStateMachineActive<T>() where T : Node
        {
            return _activeStateMachines.Contains(typeof(T));
        }
        
        #region IStateMachineController Implementation
        
        public void HandleStateTransition(Enum previousState, Enum newState)
        {
            Logger.Debug($"{Name}: State transition from {previousState} to {newState}");
        }
        
        #endregion
        
        #region IEscortController Implementation
        
        private ShipEntity _escortTarget;
        private Node2D _currentThreat;
        private bool _isTargetUnderAttack;
        
        public void HandleEscortTargetLost()
        {
            Logger.Info($"{Name} ({GetParent().Name}): Escort target {_escortTarget?.Name} lost/destroyed. Deciding next action.");
            _escortTarget = null;
        }
        
        public void SetThreatInfo(Node2D threatEntity, bool isUnderAttack)
        {
            _currentThreat = threatEntity;
            _isTargetUnderAttack = isUnderAttack;
            Logger.Debug($"{Name}: Threat info updated. Threat: {threatEntity?.Name ?? "None"}, Under attack: {isUnderAttack}");
        }
        
        public void SetEscortTarget(ShipEntity targetEntity, Vector2 offset)
        {
            _escortTarget = targetEntity;
            Logger.Debug($"{Name}: Escort target set to {targetEntity.Name ?? "None"}");
            
            // Activate the escort state machine if it exists and isn't already active
            var escortStateMachine = GetStateMachine<EscortStateMachine>();
            if (escortStateMachine != null && !IsStateMachineActive<EscortStateMachine>())
            {
                escortStateMachine.SetEscortTarget(targetEntity, offset);
                ActivateStateMachine<EscortStateMachine>();
            }
        }
        
        #endregion
    }
}
