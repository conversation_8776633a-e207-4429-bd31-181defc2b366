using System;

namespace SpaceGame.Scripts.AI.StateMachine
{
    /// <summary>
    /// Base interface for all state machine controllers.
    /// </summary>
    public interface IStateMachineController
    {
        /// <summary>
        /// Called when a state machine transitions from one state to another.
        /// </summary>
        /// <param name="previousState">The previous state.</param>
        /// <param name="newState">The new state.</param>
        void HandleStateTransition(Enum previousState, Enum newState);
    }
}
