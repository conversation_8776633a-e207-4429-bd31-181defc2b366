using Godot;
using System;
using System.Collections.Generic;

namespace SpaceGame.Scripts.AI.StateMachine
{
    public abstract partial class StateMachine<TOwnerController, TStateKey, TSelfContext, TControlledNode> : Node
        where TStateKey : Enum
        where TOwnerController : IStateMachineController // Interface for state machine controllers
        where TControlledNode : Node // The node type this state machine directly controls
        where TSelfContext : StateMachine<TOwnerController, TStateKey, TSelfContext, TControlledNode> // Self-referential context for states
    {
        protected TOwnerController OwnerController { get; private set; }
        public TControlledNode ControlledNode { get; private set; } // The node instance this state machine controls
        protected StateBase<TSelfContext, TStateKey>? CurrentState { get; private set; }
        protected Dictionary<TStateKey, StateBase<TSelfContext, TStateKey>> States { get; } = new Dictionary<TStateKey, StateBase<TSelfContext, TStateKey>>();
        private TStateKey _currentStateKey;

        public StateMachine() { }

        public virtual void Initialize(TOwnerController ownerController, TControlledNode controlledNode)
        {
            OwnerController = ownerController;
            ControlledNode = controlledNode;
            RegisterStates();
            if (States.Count == 0)
            {
                Logger.Warning($"{GetType().Name}: No states registered.");
                return;
            }
            EnterState(InitialStateKey);
        }

        protected abstract void RegisterStates();
        protected abstract TStateKey InitialStateKey { get; }

        public virtual void EnterState(TStateKey newStateKey)
        {
            if (!States.TryGetValue(newStateKey, out var stateInstance))
            {
                Logger.Error($"{GetType().Name}: State {newStateKey} not found in registered states.");
                return;
            }

            var previousState = _currentStateKey;
            CurrentState?.Exit();
            CurrentState = stateInstance;
            _currentStateKey = newStateKey;
            CurrentState.Enter();
            Logger.Debug($"{GetType().Name} entered state {newStateKey}");
            
            // Notify the controller about the state transition
            OwnerController.HandleStateTransition(previousState, newStateKey);
        }

        public override void _Process(double delta)
        {
            if (CurrentState == null) return;

            CurrentState.Update(delta);
            TStateKey nextStateKey = CurrentState.GetNextStateKey();
            if (!nextStateKey.Equals(_currentStateKey))
            {
                EnterState(nextStateKey);
            }
        }

        public override void _PhysicsProcess(double delta)
        {
            if (CurrentState == null) return;
            CurrentState.PhysicsUpdate(delta);
        }

        public TStateKey GetCurrentStateKey()
        {
            return _currentStateKey;
        }
        
        // Helper to add states during RegisterStates
        protected void AddState(TStateKey key, StateBase<TSelfContext, TStateKey> stateInstance)
        {
            stateInstance.InitializeState((TSelfContext)this);
            AddChild(stateInstance); // Add state as child for Godot lifecycle management if needed
            stateInstance.Owner = Owner;
            States[key] = stateInstance;
        }
    }
}
