using Godot;
using System;

namespace SpaceGame.Scripts.AI.StateMachine
{
    public abstract partial class StateBase<TStateMachineContext, TStateKey> : Node
        where TStateKey : Enum
        where TStateMachineContext : Node // Assuming context is at least a Node
    {
        protected TStateMachineContext Context { get; private set; }

        public StateBase()
        {
            Name = GetType().Name;
        }
        
        public virtual void InitializeState(TStateMachineContext context)
        {
            Context = context;
        }

        public abstract void Enter();
        public abstract void Exit();
        public abstract void Update(double delta);
        public abstract void PhysicsUpdate(double delta);
        public abstract TStateKey GetNextStateKey();
    }
}
