using SpaceGame.Scripts.Serialization.Blueprint;

namespace SpaceGame.Scripts;

using Godot;
using System;
using SpaceGame.CodeGeneration;

// Defines stats for a single weapon.
// Not a Resource itself, but a data container used by WeaponData and FleetShipInstance.
public class WeaponStats : GameDataBase
{
    // Base Stats
    public float DamagePerShot { get; set; } = 10f;
    public float FireRate { get; set; } = 1.0f; // Shots per second
    public float Range { get; set; } = 800f;
    public float ProjectileSpeed { get; set; } = 1000f;
    public float Accuracy { get; set; } = 1.0f; // 1.0 = perfect

    public WeaponStats()
    {
    }

    // Copy constructor
    public WeaponStats(WeaponStats other)
        : base(other.BlueprintId, other.DisplayName)
    {
        DamagePerShot = other.DamagePerShot;
        FireRate = other.FireRate;
        Range = other.Range;
        ProjectileSpeed = other.ProjectileSpeed;
        Accuracy = other.Accuracy;
    }

    // Copy constructor
    public WeaponStats(WeaponStatsBlueprint other) 
        : base(other.GetType().Name, other.GetType().Name)
    {
        DamagePerShot = other.DamagePerShot;
        FireRate = other.FireRate;
        Range = other.Range;
        ProjectileSpeed = other.ProjectileSpeed;
        Accuracy = other.Accuracy;
    }

    // Helper to calculate Damage Per Second
    public float CalculateDPS()
    {
        return DamagePerShot * FireRate * Accuracy; // Include accuracy in DPS calculation
    }

    // String representation for debugging
    public override string ToString()
    {
        return $"Dmg:{DamagePerShot:F1}, RoF:{FireRate:F2}, Rng:{Range:F0}, Acc:{Accuracy:P0}, DPS:{CalculateDPS():F1}";
    }
}