using SpaceGame.CodeGeneration;
using SpaceGame.Scripts.Serialization.Blueprint;

namespace SpaceGame.Scripts;

using Godot;
using System;
using System.Collections.Generic; // Needed for Dictionary access

[GlobalClass]
public partial class StatModifierBlueprint : Resource, IBlueprint
{
    [Export] public Stat StatToModify { get; set; }
    [Export] public ModifierType ModType { get; set; }
    [Export] public float Value { get; set; }

    // --- Targeting Criteria (Ship) ---
    [Export] public bool ApplyToAllShips { get; set; } = false;
    [Export] public ShipType TargetShipType { get; set; } // Ignored if ApplyToAllShips is true

    // --- Targeting Criteria (Weapon - only relevant if StatToModify is a Weapon stat) ---
    [Export(PropertyHint.PlaceholderText, "Leave empty to target by type or all")]
    public string TargetWeaponSlotID { get; set; } = ""; // Targets a specific weapon mount (e.g., "MainCannon")
    [Export] public WeaponType TargetWeaponType { get; set; } // Targets all weapons of this type on the ship
    [Export] public bool ApplyToAllWeapons { get; set; } = false; // Applies to *all* weapons if SlotID and Type aren't specific

    // --- Targeting Criteria (System - only relevant if StatToModify is a System stat) ---
    [Export(PropertyHint.PlaceholderText, "Leave empty to target by type or all")]
    public string TargetSystemSlotID { get; set; } = ""; // e.g., "Utility1"
    [Export] public SystemType TargetSystemType { get; set; } // e.g., CloakingDevice
    [Export] public bool ApplyToAllSystems { get; set; } = false; // Affects all fitted systems

    public StatModifierBlueprint() { } // Required for Godot Resources

    public string BlueprintId { get; set; }
    public string DisplayName { get; }
    public GameDataBase CreateInstance()
    {
        return new StatModifierData(this);
    }
}