namespace SpaceGame.Scripts;

/// <summary>
/// Represents the diplomatic relationship level between factions.
/// </summary>
public enum RelationshipLevel
{
    /// <summary>
    /// Hostile relationship - will attack on sight.
    /// </summary>
    Hostile,
    
    /// <summary>
    /// Unfriendly relationship - not immediately hostile but distrustful.
    /// </summary>
    Unfriendly,
    
    /// <summary>
    /// Neutral relationship - neither friendly nor hostile.
    /// </summary>
    Neutral,
    
    /// <summary>
    /// Friendly relationship - willing to trade and cooperate.
    /// </summary>
    Friendly,
    
    /// <summary>
    /// Allied relationship - will defend each other.
    /// </summary>
    Allied
}