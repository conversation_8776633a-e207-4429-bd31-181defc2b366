
using System;
using SpaceGame.CodeGeneration;

namespace SpaceGame.Scripts
{
    /// <summary>
    /// Data class for StatModifierBlueprint. Auto-generated from StatModifierBlueprint.
    /// </summary>
    public class StatModifierData : GameDataBase
    {
        /// <summary>
        /// Gets or sets the StatToModify.
        /// </summary>
        public SpaceGame.Scripts.Stat StatToModify { get; set; }

        /// <summary>
        /// Gets or sets the ModType.
        /// </summary>
        public SpaceGame.Scripts.ModifierType ModType { get; set; }

        /// <summary>
        /// Gets or sets the Value.
        /// </summary>
        public float Value { get; set; }

        /// <summary>
        /// Gets or sets the ApplyToAllShips.
        /// </summary>
        public bool ApplyToAllShips { get; set; }

        /// <summary>
        /// Gets or sets the TargetShipType.
        /// </summary>
        public SpaceGame.Scripts.ShipType TargetShipType { get; set; }

        /// <summary>
        /// Gets or sets the TargetWeaponSlotID.
        /// </summary>
        public string TargetWeaponSlotID { get; set; }

        /// <summary>
        /// Gets or sets the TargetWeaponType.
        /// </summary>
        public SpaceGame.Scripts.WeaponType TargetWeaponType { get; set; }

        /// <summary>
        /// Gets or sets the ApplyToAllWeapons.
        /// </summary>
        public bool ApplyToAllWeapons { get; set; }

        /// <summary>
        /// Gets or sets the TargetSystemSlotID.
        /// </summary>
        public string TargetSystemSlotID { get; set; }

        /// <summary>
        /// Gets or sets the TargetSystemType.
        /// </summary>
        public SpaceGame.Scripts.SystemType TargetSystemType { get; set; }

        /// <summary>
        /// Gets or sets the ApplyToAllSystems.
        /// </summary>
        public bool ApplyToAllSystems { get; set; }

        /// <summary>
        /// Gets or sets the DisplayName.
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="StatModifierData"/> class.
        /// Default constructor for serialization.
        /// </summary>
        public StatModifierData() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="StatModifierData"/> class.
        /// </summary>
        /// <param name="blueprint">The StatModifierBlueprint blueprint.</param>
        public StatModifierData(StatModifierBlueprint blueprint)
        {
            StatToModify = blueprint.StatToModify;
            ModType = blueprint.ModType;
            Value = blueprint.Value;
            ApplyToAllShips = blueprint.ApplyToAllShips;
            TargetShipType = blueprint.TargetShipType;
            TargetWeaponSlotID = blueprint.TargetWeaponSlotID;
            TargetWeaponType = blueprint.TargetWeaponType;
            ApplyToAllWeapons = blueprint.ApplyToAllWeapons;
            TargetSystemSlotID = blueprint.TargetSystemSlotID;
            TargetSystemType = blueprint.TargetSystemType;
            ApplyToAllSystems = blueprint.ApplyToAllSystems;
            Id = Guid.NewGuid().ToString();
            BlueprintId = blueprint.BlueprintId;
            DisplayName = blueprint.DisplayName;
        }
    }
}
