[gd_scene load_steps=5 format=3 uid="uid://exagw4mu60xv"]

[ext_resource type="PackedScene" uid="uid://di5n85lsgqn7" path="res://SpaceGame/Assets/Factions/Federation/Scout/Scout.tscn" id="1_scout"]
[ext_resource type="Script" uid="uid://dqjqf1bc15i58" path="res://SpaceGame/Scripts/Debug/TrailTestController.cs" id="2_controller"]
[ext_resource type="Texture2D" uid="uid://cqvv01t5f3ecf" path="res://SpaceGame/Assets/Backgrounds/pixelart_starfield_diagonal_diffraction_spikes.png" id="3_background"]

[sub_resource type="LabelSettings" id="LabelSettings_instructions"]
font_color = Color(1, 1, 1, 0.8)
outline_size = 2
outline_color = Color(0, 0, 0, 0.8)

[node name="TrailTestScene" type="Node2D"]

[node name="Background" type="TextureRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 1920.0
offset_bottom = 1080.0
texture = ExtResource("3_background")
stretch_mode = 1

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(960, 540)
zoom = Vector2(0.8, 0.8)

[node name="TestShip" parent="." instance=ExtResource("1_scout")]
position = Vector2(960, 540)

[node name="TrailTestController" type="Node" parent="." node_paths=PackedStringArray("TestShip")]
script = ExtResource("2_controller")
TestShip = NodePath("../TestShip")
TestSpeed = 400.0
TestRotationSpeed = 120.0

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Instructions" type="Label" parent="UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -120.0
offset_right = 400.0
offset_bottom = -20.0
grow_vertical = 0
text = "Trail Test Controls:
WASD - Move ship
Q/E - Rotate ship
T - Toggle trails
R - Clear trails"
label_settings = SubResource("LabelSettings_instructions")
