[gd_scene load_steps=5 format=3 uid="uid://dqa3vxvu3e1pw"]

[ext_resource type="Script" uid="uid://ll7ypxbbupa7" path="res://SpaceGame/Scripts/Battle/SpaceBattleScene.cs" id="1_8ybkm"]
[ext_resource type="Script" uid="uid://q3p8sfhduhiy" path="res://SpaceGame/Scripts/Battle/BattleCameraController.cs" id="3_urn4s"]
[ext_resource type="PackedScene" uid="uid://c6galwqdxvefu" path="res://SpaceGame/Scripts/Debug/DebugGrid.tscn" id="4_gwjh6"]
[ext_resource type="Texture2D" uid="uid://cqvv01t5f3ecf" path="res://SpaceGame/Assets/Backgrounds/pixelart_starfield_diagonal_diffraction_spikes.png" id="4_vk8ub"]

[node name="SpaceBattleScene" type="Node2D" node_paths=PackedStringArray("BattleCamera", "EntitiesContainer")]
script = ExtResource("1_8ybkm")
BattleAreaWidth = 5000.0
BattleAreaHeight = 5000.0
BattleCamera = NodePath("Camera2D")
EntitiesContainer = NodePath("BattleEntities")

[node name="ParallaxContainer" type="Node2D" parent="."]

[node name="DistantStarsParallax" type="Parallax2D" parent="ParallaxContainer"]
scroll_scale = Vector2(0.1, 0.1)
repeat_size = Vector2(320, 320)
repeat_times = 10

[node name="DistantStarsSprite" type="Sprite2D" parent="ParallaxContainer/DistantStarsParallax"]
texture = ExtResource("4_vk8ub")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(1.5, 1.5)
position_smoothing_enabled = true
rotation_smoothing_enabled = true

[node name="BoundaryVisualization" type="Node2D" parent="."]

[node name="TopBoundary" type="Line2D" parent="BoundaryVisualization"]
points = PackedVector2Array(-2000, -1500, 2000, -1500)
width = 2.0
default_color = Color(1, 0, 0, 0.5)

[node name="RightBoundary" type="Line2D" parent="BoundaryVisualization"]
points = PackedVector2Array(2000, -1500, 2000, 1500)
width = 2.0
default_color = Color(1, 0, 0, 0.5)

[node name="BottomBoundary" type="Line2D" parent="BoundaryVisualization"]
points = PackedVector2Array(2000, 1500, -2000, 1500)
width = 2.0
default_color = Color(1, 0, 0, 0.5)

[node name="LeftBoundary" type="Line2D" parent="BoundaryVisualization"]
points = PackedVector2Array(-2000, 1500, -2000, -1500)
width = 2.0
default_color = Color(1, 0, 0, 0.5)

[node name="DebugGrid" parent="." instance=ExtResource("4_gwjh6")]
GridColor = Color(1, 1, 1, 0.803922)

[node name="BattleEntities" type="Node2D" parent="."]

[node name="UI" type="CanvasLayer" parent="."]

[node name="BackButton" type="Button" parent="UI"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -160.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 60.0
grow_horizontal = 0
text = "Back to Universe"

[node name="BattleCameraController" type="Node" parent="." node_paths=PackedStringArray("Camera")]
script = ExtResource("3_urn4s")
Camera = NodePath("../Camera2D")

[connection signal="pressed" from="UI/BackButton" to="." method="ExitBattle"]
