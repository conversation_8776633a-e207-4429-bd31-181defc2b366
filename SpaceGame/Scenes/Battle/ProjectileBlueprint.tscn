[gd_scene load_steps=2 format=3 uid="uid://bmxvu8ucbb6wo"]

[ext_resource type="Script" uid="uid://b6ye2mve78i17" path="res://SpaceGame/Scripts/Battle/Entities/ProjectileEntity.cs" id="1_sal6g"]

[node name="ProjectileBaseScene" type="CharacterBody2D" node_paths=PackedStringArray("CollisionShape", "HitboxArea", "EntitySprite")]
script = ExtResource("1_sal6g")
CollisionShape = NodePath("HitboxArea/CollisionShape2D")
HitboxArea = NodePath("HitboxArea")
EntitySprite = NodePath("ProjectileSprite")

[node name="ProjectileSprite" type="AnimatedSprite2D" parent="."]
rotation = 1.5708

[node name="HitboxArea" type="Area2D" parent="."]
rotation = 1.5708
monitorable = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="HitboxArea"]

[node name="Behaviors" type="Node" parent="."]
