[gd_scene load_steps=6 format=3 uid="uid://k4nkf67sqkiq"]

[ext_resource type="Script" uid="uid://dqx1ddxlkifgl" path="res://SpaceGame/Scripts/Battle/BattleEntity.cs" id="1_nxm4j"]
[ext_resource type="Script" uid="uid://dvt2fktoc10e5" path="res://SpaceGame/Scripts/Battle/ThrusterComponent.cs" id="2_wqnrj"]
[ext_resource type="Script" uid="uid://78faoxod14rx" path="res://SpaceGame/Scripts/Battle/WeaponMount.cs" id="3_5ydvr"]

[sub_resource type="CircleShape2D" id="CircleShape2D_wtr2c"]
radius = 24.0

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_3ct86"]
particle_flag_disable_z = true
angle_min = 10.0
angle_max = 10.0
direction = Vector3(0, 1, 0)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector3(0, 98, 0)
scale_min = 2.0
scale_max = 2.0
color = Color(0.9, 0.5, 0.1, 1)

[node name="Scout" type="Node2D" node_paths=PackedStringArray("EntitySprite", "CollisionShape", "Thruster", "WeaponMountsContainer")]
script = ExtResource("1_nxm4j")
EntitySprite = NodePath("EntitySprite")
CollisionShape = NodePath("CollisionShape2D")
Thruster = NodePath("ThrusterComponent")
WeaponMountsContainer = NodePath("WeaponMounts")

[node name="EntitySprite" type="Sprite2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_wtr2c")

[node name="ThrusterComponent" type="Node2D" parent="." node_paths=PackedStringArray("ThrusterSprite", "ThrusterParticles")]
script = ExtResource("2_wqnrj")
ThrusterSprite = NodePath("ThrusterSprite")
ThrusterParticles = NodePath("ThrusterParticles")

[node name="ThrusterSprite" type="Sprite2D" parent="ThrusterComponent"]

[node name="ThrusterParticles" type="GPUParticles2D" parent="ThrusterComponent"]
process_material = SubResource("ParticleProcessMaterial_3ct86")

[node name="WeaponMounts" type="Node2D" parent="."]

[node name="FrontMount" type="Node2D" parent="WeaponMounts" node_paths=PackedStringArray("MountIndicator")]
position = Vector2(0, -30)
script = ExtResource("3_5ydvr")
MountId = "frontmount"
MountName = "FrontMount"
AllowedWeaponTypeFlags = 1
MountIndicator = NodePath("MountIndicator")

[node name="MountIndicator" type="Sprite2D" parent="WeaponMounts/FrontMount"]

[node name="RearMount" type="Node2D" parent="WeaponMounts" node_paths=PackedStringArray("MountIndicator")]
position = Vector2(0, 30)
script = ExtResource("3_5ydvr")
MountId = "rearmount"
MountName = "RearMount"
AllowedWeaponTypeFlags = 16
MountIndicator = NodePath("MountIndicator")

[node name="MountIndicator" type="Sprite2D" parent="WeaponMounts/RearMount"]
