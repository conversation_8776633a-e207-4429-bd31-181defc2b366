[gd_scene load_steps=9 format=3 uid="uid://00u8dmcuuurs"]

[ext_resource type="PackedScene" uid="uid://c8iqy5xnlxn4t" path="res://SpaceGame/Scenes/Battle/BattleEntityScene.tscn" id="1_0nyeg"]
[ext_resource type="Script" uid="uid://couk3xmoveic4" path="res://SpaceGame/Scripts/Battle/Entities/ShipEntity.cs" id="2_7no3g"]
[ext_resource type="Script" uid="uid://dvt2fktoc10e5" path="res://SpaceGame/Scripts/Battle/ThrusterComponent.cs" id="2_lfdfy"]
[ext_resource type="PackedScene" uid="uid://bx8k4n2v7qm1p" path="res://SpaceGame/Scenes/Battle/Components/ShipTrailComponent.tscn" id="3_trail"]
[ext_resource type="Script" uid="uid://df5g48wj3dr4n" path="res://SpaceGame/Scripts/Ships/ShipWeaponController.cs" id="4_bc04o"]

[sub_resource type="Gradient" id="Gradient_thruster"]
colors = PackedColorArray(1, 1, 1, 1, 0.7, 0.9, 1, 0.8, 0.4, 0.7, 1, 0.4, 0.1, 0.3, 0.8, 0.1, 0, 0, 0, 0)
offsets = PackedFloat32Array(0, 0.2, 0.6, 0.9, 1)

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_7no3g"]
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 2.0
direction = Vector3(0, 1, 0)
spread = 8.0
initial_velocity_min = 120.0
initial_velocity_max = 180.0
angular_velocity_min = 0.0
angular_velocity_max = 0.0
gravity = Vector3(0, 0, 0)
scale_min = 0.8
scale_max = 1.5
scale_over_velocity_min = 0.0
scale_over_velocity_max = 1.0
color = Color(0.9, 0.95, 1, 1)
color_ramp = SubResource("Gradient_thruster")

[sub_resource type="Gradient" id="Gradient_core"]
colors = PackedColorArray(1, 1, 1, 1, 0.9, 0.95, 1, 0.9, 0.6, 0.8, 1, 0.6, 0, 0, 0, 0)
offsets = PackedFloat32Array(0, 0.3, 0.7, 1)

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_core"]
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 1.0
direction = Vector3(0, 1, 0)
spread = 4.0
initial_velocity_min = 140.0
initial_velocity_max = 200.0
angular_velocity_min = 0.0
angular_velocity_max = 0.0
gravity = Vector3(0, 0, 0)
scale_min = 1.0
scale_max = 2.0
scale_over_velocity_min = 0.0
scale_over_velocity_max = 1.0
color = Color(1, 1, 1, 1)
color_ramp = SubResource("Gradient_core")

[node name="ShipEntity" node_paths=PackedStringArray("MovementController", "Thruster", "WeaponMountsContainer", "_shipWeaponController", "TrailComponent") instance=ExtResource("1_0nyeg")]
script = ExtResource("2_7no3g")
MovementController = NodePath("MovementController")
Thruster = NodePath("ThrusterComponent")
WeaponMountsContainer = NodePath("WeaponMounts")
_shipWeaponController = NodePath("ShipWeaponController")
TrailComponent = NodePath("TrailComponent")

[node name="ThrusterComponent" type="Node2D" parent="." index="0" node_paths=PackedStringArray("ThrusterParticles", "SecondaryThrusterParticles")]
script = ExtResource("2_lfdfy")
Mode = 1
ThrusterParticles = NodePath("ThrusterParticles")
SecondaryThrusterParticles = NodePath("ThrusterCore")
SecondaryThrusterThreshold = 0.3

[node name="ThrusterSprite" type="AnimatedSprite2D" parent="ThrusterComponent" index="0"]

[node name="ThrusterParticles" type="GPUParticles2D" parent="ThrusterComponent" index="1"]
position = Vector2(0, 15)
emitting = false
amount = 150
lifetime = 1.2
one_shot = false
preprocess = 0.0
speed_scale = 1.0
explosiveness = 0.0
randomness = 0.1
fixed_fps = 0
interpolate = true
fract_delta = true
visibility_rect = Rect2(-20, -10, 40, 80)
local_coords = false
draw_order = 0
trail_enabled = false
process_material = SubResource("ParticleProcessMaterial_7no3g")

[node name="ThrusterCore" type="GPUParticles2D" parent="ThrusterComponent" index="2"]
position = Vector2(0, 12)
emitting = false
amount = 80
lifetime = 1.0
one_shot = false
preprocess = 0.0
speed_scale = 1.0
explosiveness = 0.0
randomness = 0.05
fixed_fps = 0
interpolate = true
fract_delta = true
visibility_rect = Rect2(-15, -5, 30, 60)
local_coords = false
draw_order = 1
trail_enabled = false
process_material = SubResource("ParticleProcessMaterial_core")

[node name="ShieldSprite" type="AnimatedSprite2D" parent="." index="2"]

[node name="WeaponMounts" type="Node2D" parent="." index="4"]

[node name="TrailComponent" parent="." index="9" instance=ExtResource("3_trail")]

[node name="ShipWeaponController" type="Node" parent="." index="10"]
script = ExtResource("4_bc04o")
metadata/_custom_type_script = "uid://df5g48wj3dr4n"
