[gd_scene load_steps=6 format=3 uid="uid://00u8dmcuuurs"]

[ext_resource type="PackedScene" uid="uid://c8iqy5xnlxn4t" path="res://SpaceGame/Scenes/Battle/BattleEntityScene.tscn" id="1_0nyeg"]
[ext_resource type="Script" uid="uid://couk3xmoveic4" path="res://SpaceGame/Scripts/Battle/Entities/ShipEntity.cs" id="2_7no3g"]
[ext_resource type="Script" uid="uid://dvt2fktoc10e5" path="res://SpaceGame/Scripts/Battle/ThrusterComponent.cs" id="2_lfdfy"]
[ext_resource type="Script" uid="uid://df5g48wj3dr4n" path="res://SpaceGame/Scripts/Ships/ShipWeaponController.cs" id="4_bc04o"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_7no3g"]
particle_flag_disable_z = true
angle_min = 5.00001
angle_max = 5.00001
direction = Vector3(0, 1, 0)
spread = 20.0
initial_velocity_min = 50.0
initial_velocity_max = 100.0
gravity = Vector3(0, 0, 0)

[node name="ShipEntity" node_paths=PackedStringArray("MovementController", "Thruster", "WeaponMountsContainer", "_shipWeaponController") instance=ExtResource("1_0nyeg")]
script = ExtResource("2_7no3g")
MovementController = NodePath("MovementController")
Thruster = NodePath("ThrusterComponent")
WeaponMountsContainer = NodePath("WeaponMounts")
_shipWeaponController = NodePath("ShipWeaponController")

[node name="ThrusterComponent" type="Node2D" parent="." index="0" node_paths=PackedStringArray("ThrusterParticles")]
script = ExtResource("2_lfdfy")
ThrusterParticles = NodePath("ThrusterParticles")

[node name="ThrusterSprite" type="AnimatedSprite2D" parent="ThrusterComponent" index="0"]

[node name="ThrusterParticles" type="GPUParticles2D" parent="ThrusterComponent" index="1"]
visible = false
lifetime = 0.2
process_material = SubResource("ParticleProcessMaterial_7no3g")

[node name="ShieldSprite" type="AnimatedSprite2D" parent="." index="2"]

[node name="WeaponMounts" type="Node2D" parent="." index="4"]

[node name="ShipWeaponController" type="Node" parent="." index="9"]
script = ExtResource("4_bc04o")
metadata/_custom_type_script = "uid://df5g48wj3dr4n"
