[gd_scene load_steps=7 format=3 uid="uid://00u8dmcuuurs"]

[ext_resource type="PackedScene" uid="uid://c8iqy5xnlxn4t" path="res://SpaceGame/Scenes/Battle/BattleEntityScene.tscn" id="1_0nyeg"]
[ext_resource type="Script" uid="uid://couk3xmoveic4" path="res://SpaceGame/Scripts/Battle/Entities/ShipEntity.cs" id="2_7no3g"]
[ext_resource type="Script" uid="uid://dvt2fktoc10e5" path="res://SpaceGame/Scripts/Battle/ThrusterComponent.cs" id="2_lfdfy"]
[ext_resource type="PackedScene" uid="uid://bx8k4n2v7qm1p" path="res://SpaceGame/Scenes/Battle/Components/ShipTrailComponent.tscn" id="3_trail"]
[ext_resource type="Script" uid="uid://df5g48wj3dr4n" path="res://SpaceGame/Scripts/Ships/ShipWeaponController.cs" id="4_bc04o"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_7no3g"]
particle_flag_disable_z = true
direction = Vector3(0, 1, 0)
spread = 15.0
initial_velocity_min = 80.0
initial_velocity_max = 150.0
angular_velocity_min = -45.0
angular_velocity_max = 45.0
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 1.2
color = Color(0.8, 0.9, 1, 1)
color_ramp = SubResource("Gradient_thruster")

[sub_resource type="Gradient" id="Gradient_thruster"]
colors = PackedColorArray(1, 1, 1, 1, 0.8, 0.9, 1, 0.8, 0.4, 0.6, 1, 0.4, 0, 0, 0, 0)
offsets = PackedFloat32Array(0, 0.3, 0.7, 1)

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_secondary"]
particle_flag_disable_z = true
direction = Vector3(0, 1, 0)
spread = 25.0
initial_velocity_min = 120.0
initial_velocity_max = 200.0
angular_velocity_min = -60.0
angular_velocity_max = 60.0
gravity = Vector3(0, 0, 0)
scale_min = 0.8
scale_max = 1.5
color = Color(0.9, 0.95, 1, 0.9)
color_ramp = SubResource("Gradient_secondary")

[sub_resource type="Gradient" id="Gradient_secondary"]
colors = PackedColorArray(1, 1, 1, 0.9, 0.9, 0.95, 1, 0.7, 0.6, 0.8, 1, 0.3, 0, 0, 0, 0)
offsets = PackedFloat32Array(0, 0.2, 0.6, 1)

[node name="ShipEntity" node_paths=PackedStringArray("MovementController", "Thruster", "WeaponMountsContainer", "_shipWeaponController", "TrailComponent") instance=ExtResource("1_0nyeg")]
script = ExtResource("2_7no3g")
MovementController = NodePath("MovementController")
Thruster = NodePath("ThrusterComponent")
WeaponMountsContainer = NodePath("WeaponMounts")
_shipWeaponController = NodePath("ShipWeaponController")
TrailComponent = NodePath("TrailComponent")

[node name="ThrusterComponent" type="Node2D" parent="." index="0" node_paths=PackedStringArray("ThrusterParticles", "SecondaryThrusterParticles")]
script = ExtResource("2_lfdfy")
Mode = 1
ThrusterParticles = NodePath("ThrusterParticles")
SecondaryThrusterParticles = NodePath("SecondaryThrusterParticles")
SecondaryThrusterThreshold = 0.7

[node name="ThrusterSprite" type="AnimatedSprite2D" parent="ThrusterComponent" index="0"]

[node name="ThrusterParticles" type="GPUParticles2D" parent="ThrusterComponent" index="1"]
position = Vector2(0, 15)
emitting = false
amount = 100
lifetime = 0.8
one_shot = false
preprocess = 0.0
speed_scale = 1.0
explosiveness = 0.0
randomness = 0.2
visibility_rect = Rect2(-50, -50, 100, 100)
local_coords = false
draw_order = 0
process_material = SubResource("ParticleProcessMaterial_7no3g")

[node name="SecondaryThrusterParticles" type="GPUParticles2D" parent="ThrusterComponent" index="2"]
position = Vector2(0, 20)
emitting = false
amount = 50
lifetime = 1.2
one_shot = false
preprocess = 0.0
speed_scale = 1.0
explosiveness = 0.0
randomness = 0.3
visibility_rect = Rect2(-60, -60, 120, 120)
local_coords = false
draw_order = 0
process_material = SubResource("ParticleProcessMaterial_secondary")

[node name="ShieldSprite" type="AnimatedSprite2D" parent="." index="2"]

[node name="WeaponMounts" type="Node2D" parent="." index="4"]

[node name="TrailComponent" parent="." index="9" instance=ExtResource("3_trail")]

[node name="ShipWeaponController" type="Node" parent="." index="10"]
script = ExtResource("4_bc04o")
metadata/_custom_type_script = "uid://df5g48wj3dr4n"
