[gd_scene load_steps=6 format=3 uid="uid://00u8dmcuuurs"]

[ext_resource type="PackedScene" uid="uid://c8iqy5xnlxn4t" path="res://SpaceGame/Scenes/Battle/BattleEntityScene.tscn" id="1_0nyeg"]
[ext_resource type="Script" uid="uid://couk3xmoveic4" path="res://SpaceGame/Scripts/Battle/Entities/ShipEntity.cs" id="2_7no3g"]
[ext_resource type="Script" uid="uid://dvt2fktoc10e5" path="res://SpaceGame/Scripts/Battle/ThrusterComponent.cs" id="2_lfdfy"]
[ext_resource type="PackedScene" uid="uid://bx8k4n2v7qm1p" path="res://SpaceGame/Scenes/Battle/Components/ShipTrailComponent.tscn" id="3_trail"]
[ext_resource type="PackedScene" uid="uid://cx9k4n2v7flame" path="res://SpaceGame/Scenes/Battle/Components/ThrusterFlameComponent.tscn" id="4_flame"]
[ext_resource type="Script" uid="uid://df5g48wj3dr4n" path="res://SpaceGame/Scripts/Ships/ShipWeaponController.cs" id="5_bc04o"]

[node name="ShipEntity" node_paths=PackedStringArray("MovementController", "Thruster", "WeaponMountsContainer", "_shipWeaponController", "TrailComponent") instance=ExtResource("1_0nyeg")]
script = ExtResource("2_7no3g")
MovementController = NodePath("MovementController")
Thruster = NodePath("ThrusterComponent")
WeaponMountsContainer = NodePath("WeaponMounts")
_shipWeaponController = NodePath("ShipWeaponController")
TrailComponent = NodePath("TrailComponent")

[node name="ThrusterComponent" type="Node2D" parent="." index="0" node_paths=PackedStringArray("ThrusterFlame")]
script = ExtResource("2_lfdfy")
Mode = 2
ThrusterFlame = NodePath("ThrusterFlame")

[node name="ThrusterSprite" type="AnimatedSprite2D" parent="ThrusterComponent" index="0"]

[node name="ThrusterFlame" parent="ThrusterComponent" index="1" instance=ExtResource("4_flame")]
position = Vector2(0, 15)

[node name="ShieldSprite" type="AnimatedSprite2D" parent="." index="2"]

[node name="WeaponMounts" type="Node2D" parent="." index="4"]

[node name="TrailComponent" parent="." index="9" instance=ExtResource("3_trail")]

[node name="ShipWeaponController" type="Node" parent="." index="10"]
script = ExtResource("5_bc04o")
metadata/_custom_type_script = "uid://df5g48wj3dr4n"
