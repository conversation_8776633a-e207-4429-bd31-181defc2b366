[gd_scene load_steps=5 format=3 uid="uid://00u8dmcuuurs"]

[ext_resource type="PackedScene" uid="uid://c8iqy5xnlxn4t" path="res://SpaceGame/Scenes/Battle/BattleEntityScene.tscn" id="1_0nyeg"]
[ext_resource type="Script" uid="uid://couk3xmoveic4" path="res://SpaceGame/Scripts/Battle/Entities/ShipEntity.cs" id="2_7no3g"]
[ext_resource type="Script" uid="uid://dvt2fktoc10e5" path="res://SpaceGame/Scripts/Battle/ThrusterComponent.cs" id="2_lfdfy"]
[ext_resource type="PackedScene" uid="uid://bx8k4n2v7qm1p" path="res://SpaceGame/Scenes/Battle/Components/ShipTrailComponent.tscn" id="3_trail"]
[ext_resource type="Script" uid="uid://df5g48wj3dr4n" path="res://SpaceGame/Scripts/Ships/ShipWeaponController.cs" id="4_bc04o"]

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_7no3g"]
particle_flag_disable_z = true
direction = Vector3(0, 1, 0)
spread = 15.0
initial_velocity_min = 80.0
initial_velocity_max = 150.0
angular_velocity_min = -45.0
angular_velocity_max = 45.0
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 1.2
color = Color(0.8, 0.9, 1, 1)

[node name="ShipEntity" node_paths=PackedStringArray("MovementController", "Thruster", "WeaponMountsContainer", "_shipWeaponController", "TrailComponent") instance=ExtResource("1_0nyeg")]
script = ExtResource("2_7no3g")
MovementController = NodePath("MovementController")
Thruster = NodePath("ThrusterComponent")
WeaponMountsContainer = NodePath("WeaponMounts")
_shipWeaponController = NodePath("ShipWeaponController")
TrailComponent = NodePath("TrailComponent")

[node name="ThrusterComponent" type="Node2D" parent="." index="0" node_paths=PackedStringArray("ThrusterParticles")]
script = ExtResource("2_lfdfy")
Mode = 1
ThrusterParticles = NodePath("ThrusterParticles")

[node name="ThrusterSprite" type="AnimatedSprite2D" parent="ThrusterComponent" index="0"]

[node name="ThrusterParticles" type="GPUParticles2D" parent="ThrusterComponent" index="1"]
position = Vector2(0, 15)
emitting = false
amount = 100
lifetime = 0.8
process_material = SubResource("ParticleProcessMaterial_7no3g")

[node name="ShieldSprite" type="AnimatedSprite2D" parent="." index="2"]

[node name="WeaponMounts" type="Node2D" parent="." index="4"]

[node name="TrailComponent" parent="." index="9" instance=ExtResource("3_trail")]

[node name="ShipWeaponController" type="Node" parent="." index="10"]
script = ExtResource("4_bc04o")
metadata/_custom_type_script = "uid://df5g48wj3dr4n"
