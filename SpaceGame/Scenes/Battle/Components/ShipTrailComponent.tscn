[gd_scene load_steps=2 format=3 uid="uid://bx8k4n2v7qm1p"]

[ext_resource type="Script" path="res://SpaceGame/Scripts/Battle/Components/ShipTrailComponent.cs" id="1_trail"]

[node name="ShipTrailComponent" type="Node2D" node_paths=PackedStringArray("TrailLine")]
script = ExtResource("1_trail")
TrailLine = NodePath("TrailLine")
MaxTrailPoints = 50
MinPointDistance = 5.0
TrailWidth = 3.0
TrailColor = Color(0.3, 0.7, 1, 0.8)
FadeRate = 0.02
MinVelocityThreshold = 10.0
TrailEnabled = true

[node name="TrailLine" type="Line2D" parent="."]
width = 3.0
default_color = Color(0.3, 0.7, 1, 0.8)
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
antialiased = true
