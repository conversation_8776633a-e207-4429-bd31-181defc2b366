[gd_scene load_steps=2 format=3 uid="uid://cx9k4n2v7flame"]

[ext_resource type="Script" path="res://SpaceGame/Scripts/Battle/Components/ThrusterFlameComponent.cs" id="1_flame"]

[node name="ThrusterFlameComponent" type="Node2D" node_paths=PackedStringArray("FlamePolygon")]
script = ExtResource("1_flame")
FlamePolygon = NodePath("FlamePolygon")
MaxFlameLength = 30.0
FlameBaseWidth = 6.0
FlameCoreColor = Color(1, 1, 1, 0.9)
FlameEdgeColor = Color(0.3, 0.7, 1, 0.7)
FlickerIntensity = 0.08
FlickerSpeed = 12.0
FlameActive = false

[node name="FlamePolygon" type="Polygon2D" parent="."]
color = Color(1, 1, 1, 0.9)
polygon = PackedVector2Array(-3, 0, 3, 0, 1.8, 21, 0, 30, -1.8, 21)
