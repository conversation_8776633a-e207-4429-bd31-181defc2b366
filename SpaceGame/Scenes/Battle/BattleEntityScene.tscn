[gd_scene load_steps=6 format=3 uid="uid://c8iqy5xnlxn4t"]

[ext_resource type="Script" uid="uid://dke32dnj1mfcr" path="res://SpaceGame/Scripts/Battle/Components/PlayerControlComponent.cs" id="3_k2p4m"]
[ext_resource type="Script" uid="uid://bursav80bls8v" path="res://SpaceGame/Scripts/Battle/Movement/BattleEntityMovementController.cs" id="5_akdem"]
[ext_resource type="Script" uid="uid://ccf0lojsntc4v" path="res://SpaceGame/Scripts/AI/Components/StateMachineControlComponent.cs" id="5_u7dsg"]
[ext_resource type="PackedScene" uid="uid://bquc2n0u8swxd" path="res://SpaceGame/Scripts/Debug/VelocityDebugVisualizer.tscn" id="6_akdem"]

[sub_resource type="CircleShape2D" id="CircleShape2D_wtr2c"]
radius = 13.0

[node name="BattleEntity" type="CharacterBody2D"]
motion_mode = 1

[node name="EntitySprite" type="AnimatedSprite2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_wtr2c")

[node name="PlayerControlComponent" type="Node" parent="."]
script = ExtResource("3_k2p4m")

[node name="StateMachineControlComponent" type="Node" parent="."]
script = ExtResource("5_u7dsg")
metadata/_custom_type_script = "uid://ccf0lojsntc4v"

[node name="MovementController" type="Node" parent="."]
script = ExtResource("5_akdem")
metadata/_custom_type_script = "uid://bursav80bls8v"

[node name="VelocityDebugVisualizer" parent="." instance=ExtResource("6_akdem")]
