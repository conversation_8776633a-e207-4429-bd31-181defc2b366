using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Scripts;
using SpaceGame.Universe.Model;

namespace SpaceGame.State;

/// <summary>
/// Serializable data class for SaveState.
/// This is a plain C# class designed specifically for serialization to/from JSON.
/// It contains all data required to persist the game state to disk.
/// </summary>
public class SaveState
{
    /// <summary>
    /// Gets or sets the unique identifier for this save state.
    /// </summary>
    public string Id { get; set; }
    
    /// <summary>
    /// Gets or sets the version of this save state data format.
    /// </summary>
    public int DataVersion { get; set; } = 1;
    
    /// <summary>
    /// Gets or sets the timestamp when this save was created.
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
    
    /// <summary>
    /// Gets or sets the factions in the game.
    /// </summary>
    public Dictionary<string, Faction> Factions { get; set; } = new Dictionary<string, Faction>();
    
    /// <summary>
    /// Gets or sets the IDs of celestial bodies controlled by each faction.
    /// </summary>
    public Dictionary<string, List<string>> ControlledPlanetIds { get; set; } = new Dictionary<string, List<string>>();
    
    /// <summary>
    /// Gets or sets the player's meta progression data.
    /// </summary>
    public MetaProgressionData MetaProgress { get; set; }
    
    /// <summary>
    /// Gets or sets the current universe model.
    /// </summary>
    public UniverseModel CurrentUniverse { get; set; }
    
    public PlayerFleetComposition PlayerFleet { get; set; }
    
    /// <summary>
    /// Default constructor required for serialization.
    /// </summary>
    public SaveState() {}
    
    /// <summary>
    /// Creates a new SaveState with data from the provided GameState.
    /// </summary>
    /// <param name="gameState">The GameState to copy data from.</param>
    public SaveState(GameState gameState)
    {
        ArgumentNullException.ThrowIfNull(gameState);

        // Copy data from GameState
        Id = gameState.Id;
        DataVersion = gameState.DataVersion;

        var factions = gameState.Factions.ToDictionary(fState => fState.Value.Id, fState => fState.Value.Faction);
        Factions = factions;
        
        // Copy controlled planet IDs
        foreach (var kv in gameState.Factions)
        {
            var fid = kv.Key;
            var state = kv.Value;
            ControlledPlanetIds[fid] = state.ControlledPlanetsById.Keys.ToList();
        }
        
        MetaProgress = gameState.MetaProgress;
        CurrentUniverse = gameState.CurrentUniverse;
        Timestamp = DateTime.Now; // Always use current time for saves
    }
} 