using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts;
using SpaceGame.Scripts.Events;
using SpaceGame.Universe;
using SpaceGame.Universe.Model;

namespace SpaceGame.State;

public class FactionState: IDisposable
{
    private Faction _faction;
    public Faction Faction => _faction;
    
    public FactionId Id => _faction.Id;
    public FactionType Type => _faction.Type;
    public string Name => _faction.DisplayName;
    public string Description => _faction.Description;
    
    public bool IsPlayerFaction => _faction.IsPlayerFaction;
    public RelationshipLevel PlayerRelationship => _faction.PlayerRelationship;
    public Color Color => _faction.Color;
    
    public IReadOnlyList<CommanderData> Commanders => _faction.Commanders;
    
    private Dictionary<CelestialBodyId, CelestialBodyData> _controlledPlanets = new();
    private readonly ICelestialBodyResolver _bodyResolver;
    public IReadOnlySet<CelestialBodyId> ControlledPlanetIds => _faction.ControlledPlanetIds;
    public IReadOnlyList<CelestialBodyData> ControlledPlanets => _controlledPlanets.Values.ToList();
    public IReadOnlyDictionary<CelestialBodyId, CelestialBodyData> ControlledPlanetsById => _controlledPlanets;

    private string _debugString;

    public FactionState(Faction faction, ICelestialBodyResolver bodyResolver)
    {
        _faction = faction;
        _bodyResolver = bodyResolver;
        
        _debugString = $"{Name} ({Id.Substring(Id.Length - 8, 8)}) {(IsPlayerFaction ? "(Player)" : "(AI)")}";
        InitializeState();
        SubscribeToEvents();
    }
    
    private void SubscribeToEvents()
    {
        StaticFactionEvents.PlanetControlChanged += OnPlanetControlChanged;
    }

    private void OnPlanetControlChanged(CelestialBodyData bodyData, FactionId? previousFactionId, FactionId? newFactionId)
    {
        if (previousFactionId == Id) _controlledPlanets.Remove(bodyData.Id);
        if (newFactionId == Id) _controlledPlanets.Add(bodyData.Id, bodyData);
    }

    private void InitializeState()
    {
        ResolveControlledPlanets();
    }

    private void ResolveControlledPlanets()
    {
        foreach (var planetId in _faction.ControlledPlanetIds)
        {
            var bodyData = _bodyResolver.GetCelestialBodyDataById(planetId);
            if (bodyData == null)
            {
                Logger.Warning($"Unable to find planet with id {planetId}");
                continue;
            }
            _controlledPlanets.Add(planetId, bodyData);
        }
    }

    public void Dispose()
    {
        StaticFactionEvents.PlanetControlChanged -= OnPlanetControlChanged;
    }

    public void SetPlayerFaction(bool isPlayerFaction)
    {
        _faction.SetPlayerFaction(isPlayerFaction);
    }

    public bool ControlsPlanet(CelestialBodyId planetId)
    {
        return _controlledPlanets.ContainsKey(planetId);
    }

    public bool RemoveControlledPlanet(CelestialBodyId planetId)
    {
        _faction.RemoveControlledPlanet(planetId);
            
        if (_controlledPlanets.Remove(planetId, out var removedPlanet)) 
        {
            removedPlanet.SetFaction((Faction?)null);
            return true;
        }

        return false;
    }

    public bool AddControlledPlanet(CelestialBodyId celestialBodyId)
    {
        if (ControlsPlanet(celestialBodyId)) return true;
        
        var body = _bodyResolver.GetCelestialBodyDataById(celestialBodyId);
        if (body == null)
        {
            Logger.Warning($"Unable to find body with id {celestialBodyId}");
            return false;
        }
        return AddControlledPlanet(body);
    }
    
    public bool AddControlledPlanet(CelestialBodyData body)
    {
        body.SetFaction(this);
        _faction.AddControlledPlanet(body.Id);
        _controlledPlanets.Add(body.Id, body);
        return true;
    }

    public bool AddCommander(CommanderData commander)
    {
        return _faction.AddCommander(commander);
    }

    public CommanderData? FindCommanderById(CommanderId commanderId)
    {
        return _faction.GetCommanderById(commanderId);
    }

    public override string ToString()
    {
        return _debugString;
    }
}