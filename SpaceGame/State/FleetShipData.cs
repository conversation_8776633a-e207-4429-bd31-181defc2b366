using Godot;
using System.Collections.Generic;

namespace SpaceGame.State;

/// <summary>
/// Represents data for an individual ship within the player's fleet.
/// Intended for serialization (e.g., JSON).
/// </summary>
public class FleetShipData
{
    /// <summary>
    /// The ID used to look up the ship in the BattleEntityRegistry.
    /// </summary>
    public string EntityId { get; set; }

    /// <summary>
    /// Position relative to the flagship. Not applicable to the flagship itself.
    /// </summary>
    public Vector2 RelativePosition { get; set; }

    /// <summary>
    /// Stores property names and their overridden values for this ship.
    /// Uses 'object' for flexibility in JSON serialization; specific types might be used
    /// if known and strictly enforced, but 'object' allows for Variant-like behavior.
    /// </summary>
    public Dictionary<string, object> PropertyOverrides { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="FleetShipData"/> class.
    /// </summary>
    public FleetShipData()
    {
        PropertyOverrides = new Dictionary<string, object>();
    }
}
