using System.Collections.Generic;

namespace SpaceGame.State;

/// <summary>
/// Represents the player's entire fleet composition.
/// Intended for serialization (e.g., JSON) and to be part of GameState.
/// </summary>
public class PlayerFleetComposition
{
    /// <summary>
    /// Data for the player's flagship.
    /// </summary>
    public FleetShipData Flagship { get; set; }

    /// <summary>
    /// A list of data for other (escort) ships in the fleet.
    /// </summary>
    public List<FleetShipData> EscortShips { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="PlayerFleetComposition"/> class.
    /// </summary>
    public PlayerFleetComposition()
    {
        EscortShips = new List<FleetShipData>();
        // Flagship can be initialized here if a default state is desired,
        // or left null to be explicitly set later.
        // For now, let's initialize it to ensure it's not null by default.
        Flagship = new FleetShipData(); 
    }
}
