using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Scripts;
using SpaceGame.Universe.Model;
using R3;
using SpaceGame.Helpers;

namespace SpaceGame.State;

/// <summary>
/// Runtime data class for active GameState.
/// This class represents the current state of the game during runtime.
/// It provides efficient access to gameplay data and state.
/// </summary>
public partial class GameState
{
    /// <summary>
    /// Gets or sets the unique identifier for this game state.
    /// </summary>
    public string Id { get; set; }
    
    /// <summary>
    /// Gets or sets the version of this game state data format.
    /// This is primarily used for SaveState conversion.
    /// </summary>
    public int DataVersion { get; set; } = 1;
    
    /// <summary>
    /// Gets or sets the factions in the game.
    /// </summary>
    public Dictionary<FactionId, FactionState> Factions { get; set; } = new();

    /// <summary>
    /// Gets or sets the player's meta progression data.
    /// </summary>
    public MetaProgressionData MetaProgress { get; set; } = new();

    /// <summary>
    /// Gets or sets the player's fleet composition.
    /// </summary>
    public PlayerFleetComposition PlayerFleet { get; set; }

    /// <summary>
    /// Gets or sets the current universe model.
    /// </summary>
    public UniverseModel CurrentUniverse { get; set; }
    
    /// <summary>
    /// Default constructor required for serialization.
    /// </summary>
    public GameState()
    {
        Logger.Debug("Creating new GameState instance");
        PlayerFleet = new PlayerFleetComposition();
    }
    
    /// <summary>
    /// Creates a new GameState instance from a SaveState.
    /// </summary>
    /// <param name="saveState">The SaveState to convert from</param>
    /// <returns>A new GameState initialized with data from the SaveState</returns>
    public static GameState FromSaveState(SaveState saveState)
    {
        if (saveState == null)
        {
            throw new ArgumentNullException(nameof(saveState));
        }

        saveState.CurrentUniverse.RebuildCelestialBodiesFromCells();
        
        var gameState = new GameState
        {
            Id = saveState.Id,
            DataVersion = saveState.DataVersion,
            CurrentUniverse = saveState.CurrentUniverse,
            Factions = ConvertFactionsToFactionState(saveState.Factions, saveState.CurrentUniverse),
            MetaProgress = saveState.MetaProgress,
        };

        if (saveState.PlayerFleet != null) // Placeholder for future SaveState update
        {
            gameState.PlayerFleet = saveState.PlayerFleet;
        }

        // Ensure each FactionState reflects its Faction.ControlledPlanetIds
        foreach (var factionState in gameState.Factions.Values)
        {
            foreach (var pid in factionState.ControlledPlanetIds)
            {
                factionState.AddControlledPlanet(pid);
            }
        }

        return gameState;
    }

    private static Dictionary<FactionId,FactionState> ConvertFactionsToFactionState(Dictionary<FactionId,Faction> factions, ICelestialBodyResolver celestialBodyResolver)
    {
        var result = new Dictionary<FactionId,FactionState>();
        foreach (var faction in factions.Values)
        {
            result[faction.Id] = new FactionState(faction, celestialBodyResolver);
        }
        
        return result;
    }

    /// <summary>
    /// Converts this GameState to a SaveState for serialization.
    /// </summary>
    /// <returns>A new SaveState containing data from this GameState</returns>
    public SaveState ToSaveState()
    {
        return new SaveState(this);
    }
    
    /// <summary>
    /// Gets the player faction.
    /// </summary>
    /// <returns>The player faction, or null if not found.</returns>
    public FactionState? GetPlayerFaction()
    {
        return Factions.Values.FirstOrDefault(f => f.IsPlayerFaction);
    }
}