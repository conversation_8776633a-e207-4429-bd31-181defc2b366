[gd_resource type="Resource" script_class="EnemyBehaviorConfiguration" load_steps=2 format=3]

[ext_resource type="Script" path="res://SpaceGame/Battle/AI/EnemyBehaviorConfiguration.cs" id="1_qqsm3"]

[resource]
script = ExtResource("1_qqsm3")
BehaviorName = "Interceptor"
MovementBehaviorType = 0
TargetingBehaviorType = 0
WeaponBehaviorType = 0
ThrustStrength = 1.2
RotationSpeed = 2.5
OptimalDistance = 250.0
CircleDistance = 400.0
CircleSpeed = 1.0
ClockwiseCircling = true
DetectionRange = 900.0
TargetUpdateInterval = 0.8
FiringAngleThreshold = 0.15
OptimalFiringDistance = 300.0
DistanceTolerance = 150.0
BurstDuration = 2.0
CooldownDuration = 3.0
