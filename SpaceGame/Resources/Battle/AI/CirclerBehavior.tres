[gd_resource type="Resource" script_class="EnemyBehaviorConfiguration" load_steps=2 format=3]

[ext_resource type="Script" path="res://SpaceGame/Battle/AI/EnemyBehaviorConfiguration.cs" id="1_qqsm3"]

[resource]
script = ExtResource("1_qqsm3")
BehaviorName = "Circler"
MovementBehaviorType = 1
TargetingBehaviorType = 1
WeaponBehaviorType = 1
ThrustStrength = 0.8
RotationSpeed = 1.8
OptimalDistance = 300.0
CircleDistance = 500.0
CircleSpeed = 0.8
ClockwiseCircling = false
DetectionRange = 1000.0
TargetUpdateInterval = 1.5
FiringAngleThreshold = 0.2
OptimalFiringDistance = 500.0
DistanceTolerance = 200.0
BurstDuration = 3.0
CooldownDuration = 2.0
