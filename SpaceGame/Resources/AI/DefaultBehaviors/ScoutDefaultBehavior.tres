[gd_resource type="Resource" script_class="EnemyBehaviorConfiguration" load_steps=2 format=3 uid="uid://cclvtb167swbl"]

[ext_resource type="Script" uid="uid://ch1yu4rnxsmer" path="res://SpaceGame/Battle/AI/EnemyBehaviorConfiguration.cs" id="1_jcmfb"]

[resource]
script = ExtResource("1_jcmfb")
BehaviorName = "Scout Default"
MovementBehaviorType = 1
TargetingBehaviorType = 0
WeaponBehaviorType = 0
ThrustStrength = 1.5
RotationSpeed = 3.0
OptimalDistance = 250.0
CircleDistance = 500.0
CircleSpeed = 1.8
ClockwiseCircling = false
DetectionRange = 1200.0
TargetUpdateInterval = 0.5
FiringAngleThreshold = 0.2
OptimalFiringDistance = 600.0
DistanceTolerance = 250.0
BurstDuration = 1.5
CooldownDuration = 2.0
