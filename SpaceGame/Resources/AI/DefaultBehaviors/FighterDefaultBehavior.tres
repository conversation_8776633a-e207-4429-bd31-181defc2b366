[gd_resource type="Resource" script_class="EnemyBehaviorConfiguration" load_steps=2 format=3 uid="uid://ddma25ejviasu"]

[ext_resource type="Script" uid="uid://ch1yu4rnxsmer" path="res://SpaceGame/Battle/AI/EnemyBehaviorConfiguration.cs" id="1_jcmfb"]

[resource]
script = ExtResource("1_jcmfb")
BehaviorName = "Fighter Default"
MovementBehaviorType = 1
TargetingBehaviorType = 0
WeaponBehaviorType = 0
ThrustStrength = 1.2
RotationSpeed = 2.5
OptimalDistance = 250.0
CircleDistance = 100.0
CircleSpeed = 1.2
ClockwiseCircling = true
DetectionRange = 900.0
TargetUpdateInterval = 0.8
FiringAngleThreshold = 0.15
OptimalFiringDistance = 400.0
DistanceTolerance = 150.0
BurstDuration = 2.5
CooldownDuration = 1.5
