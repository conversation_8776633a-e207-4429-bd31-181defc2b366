using System;
using System.Collections.Generic;
using System.Globalization;
using Godot;

namespace SpaceGame.Helpers;

public class Logger
{
    public static readonly LogLevelEnum CurrentLogLevel = LogLevelEnum.Debug;

    private static String CurrentTime =>
        DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
    
    private static int CurrentThreadId => System.Environment.CurrentManagedThreadId;
    
    /// <summary>
    /// Event that is raised when a log message is emitted.
    /// Provides structured log data for filtering and display in debug UI.
    /// </summary>
    public static event EventHandler<LogEventArgs>? LogEvent;
    
    public static void Error(string message, Exception? e = null, int frameIndex = 3)
    {
        Print(LogLevelEnum.Error, message, frameIndex);
        if (e != null)
            GD.PrintErr(e.StackTrace);
    }

    public static void Warning(string message, int frameIndex = 3) =>
        Print(LogLevelEnum.Warning, message, frameIndex);

    public static void Info(string message, int frameIndex = 3) => Print(LogLevelEnum.Info, message, frameIndex);

    public static void Debug(string message, int frameIndex = 3) => Print(LogLevelEnum.Debug, message, frameIndex);

    public static void Print(LogLevelEnum logLevel, string message, int frameIndex = 3)
    {
        if (CurrentLogLevel > logLevel)
        {
            return;
        }
        
        var callerInfo = ReflectionHelper.GetCallerInfo(frameIndex);
        var messageToPrint = $"[{CurrentTime}] [Thread {CurrentThreadId}] [{logLevel}] [{callerInfo}]: {message}";
        GD.PrintRich(Colorize(logLevel, messageToPrint));
        
        // Emit the log event with structured data
        LogEvent?.Invoke(null, new LogEventArgs
        {
            Timestamp = DateTime.UtcNow,
            ThreadId = CurrentThreadId,
            LogLevel = logLevel,
            CallerInfo = callerInfo,
            Message = message
        });
    }

    private static string Colorize(LogLevelEnum logLevel, string message)
    {
        if (!_logColors.TryGetValue(logLevel, out var color))
        {
            return message;
        }
        
        return $"[color={color}]{message}[/color]";
    }

    private static readonly Dictionary<LogLevelEnum, string> _logColors = new()
    {
        { LogLevelEnum.Trace, "gray" },
        { LogLevelEnum.Debug, "green" },
        { LogLevelEnum.Info, "white" },
        { LogLevelEnum.Warning, "yellow" },
        { LogLevelEnum.Error, "red" }
    };

    public enum LogLevelEnum
    {
        Trace,
        Debug,
        Info,
        Warning,
        Error,
        Off
    }
    
    /// <summary>
    /// Event arguments for the LogEvent containing structured log data.
    /// </summary>
    public class LogEventArgs : EventArgs
    {
        public DateTime Timestamp { get; set; }
        public int ThreadId { get; set; }
        public LogLevelEnum LogLevel { get; set; }
        public CallerInfo CallerInfo { get; set; }
        public string Message { get; set; } = string.Empty;
        
        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [Thread {ThreadId}] [{LogLevel}] [{CallerInfo}]: {Message}";
        }
    }

    public static void PrintStackTrace()
    {
        var stackTrace = new System.Diagnostics.StackTrace(skipFrames: 1);
        GD.Print(stackTrace);
    }
}