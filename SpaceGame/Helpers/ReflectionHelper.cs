using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;

namespace SpaceGame.Helpers;

#nullable enable
public static class ReflectionHelper
{
    [MethodImpl(MethodImplOptions.NoInlining)]
    public static CallerInfo GetCallerInfo(int frameIndex = 3)
    {
        StackFrame callerFrame = new StackFrame(frameIndex);
        MethodBase? method = callerFrame.GetMethod();

        return new CallerInfo
        {
            TypeName = method?.DeclaringType?.Name ?? "UnknownType",
            MethodName = method?.Name ?? "UnknownMethod",
            LineNumber = callerFrame.GetFileLineNumber()
        };
    }
    
    /// <summary>
    /// Represents a member (property or field) that can be inspected and potentially modified.
    /// </summary>
    public struct InspectableMember
    {
        public string Name { get; set; }
        public Type Type { get; set; }
        public Type DeclaringType { get; set; } // The class that declared this member
        public Func<object?> GetValue { get; set; }
        public Action<object?>? SetValue { get; set; } // Null if read-only
        public bool IsReadOnly { get; set; }
        public bool IsCollection { get; set; }
        public bool IsGodotObject { get; set; }
        public bool IsEnum { get; set; }
        public bool IsPrimitive { get; set; }
        public bool IsString { get; set; }
        public bool IsOverride { get; set; } // Whether this member overrides a base class member
    }
    
    /// <summary>
    /// Gets a list of inspectable members (properties and fields) for an object.
    /// </summary>
    /// <param name="obj">The object to inspect</param>
    /// <param name="maxDepth">Maximum recursion depth for nested objects</param>
    /// <param name="currentDepth">Current recursion depth (used internally)</param>
    /// <param name="includeBaseMembers">Whether to include members from base classes</param>
    /// <returns>List of inspectable members</returns>
    public static List<InspectableMember> GetInspectableMembers(object? obj, int maxDepth = 3, int currentDepth = 0, bool includeBaseMembers = true)
    {
        var result = new List<InspectableMember>();
        
        if (obj == null || currentDepth > maxDepth)
        {
            return result;
        }
        
        Type type = obj.GetType();
        var processedMembers = new HashSet<string>(); // Track processed members to avoid duplicates
        
        // Process the type hierarchy from most derived to base
        var typeHierarchy = new List<Type>();
        Type? currentType = type;
        
        while (currentType != null)
        {
            typeHierarchy.Add(currentType);
            currentType = currentType.BaseType;
        }
        
        // Process each type in the hierarchy
        foreach (var declaringType in typeHierarchy)
        {
            // Get public properties declared at this level
            foreach (PropertyInfo property in declaringType.GetProperties(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly))
            {
                try
                {
                    if (property.GetIndexParameters().Length > 0)
                    {
                        // Skip indexed properties
                        continue;
                    }
                    
                    // Skip if we've already processed this property (from a derived class)
                    if (processedMembers.Contains(property.Name))
                    {
                        continue;
                    }
                    
                    processedMembers.Add(property.Name);
                    
                    // Check if this property overrides a base class property
                    bool isOverride = false;
                    var baseType = declaringType.BaseType;
                    while (baseType != null)
                    {
                        var baseProperty = baseType.GetProperty(property.Name, BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly);
                        if (baseProperty != null)
                        {
                            isOverride = true;
                            break;
                        }
                        baseType = baseType.BaseType;
                    }
                    
                    var member = new InspectableMember
                    {
                        Name = property.Name,
                        Type = property.PropertyType,
                        DeclaringType = declaringType,
                        GetValue = () => property.GetValue(obj),
                        IsReadOnly = !property.CanWrite,
                        IsCollection = typeof(IEnumerable<>).IsAssignableFrom(property.PropertyType) && 
                                      property.PropertyType != typeof(string),
                        IsGodotObject = typeof(Godot.GodotObject).IsAssignableFrom(property.PropertyType),
                        IsEnum = property.PropertyType.IsEnum,
                        IsPrimitive = property.PropertyType.IsPrimitive || 
                                     property.PropertyType == typeof(decimal),
                        IsString = property.PropertyType == typeof(string),
                        IsOverride = isOverride
                    };
                    
                    if (property.CanWrite)
                    {
                        member.SetValue = value => property.SetValue(obj, value);
                    }
                    
                    result.Add(member);
                }
                catch (Exception ex)
                {
                    Logger.Warning($"Error inspecting property {property.Name}: {ex.Message}");
                }
            }
            
            // Get public fields declared at this level
            foreach (FieldInfo field in declaringType.GetFields(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly))
            {
                try
                {
                    // Skip if we've already processed this field (from a derived class)
                    if (processedMembers.Contains(field.Name))
                    {
                        continue;
                    }
                    
                    processedMembers.Add(field.Name);
                    
                    var member = new InspectableMember
                    {
                        Name = field.Name,
                        Type = field.FieldType,
                        DeclaringType = declaringType,
                        GetValue = () => field.GetValue(obj),
                        IsReadOnly = field.IsInitOnly || field.IsLiteral,
                        IsCollection = typeof(IEnumerable<>).IsAssignableFrom(field.FieldType) && 
                                      field.FieldType != typeof(string),
                        IsGodotObject = typeof(Godot.GodotObject).IsAssignableFrom(field.FieldType),
                        IsEnum = field.FieldType.IsEnum,
                        IsPrimitive = field.FieldType.IsPrimitive || 
                                     field.FieldType == typeof(decimal),
                        IsString = field.FieldType == typeof(string),
                        IsOverride = false // Fields can't be overridden in C#
                    };
                    
                    if (!field.IsInitOnly && !field.IsLiteral)
                    {
                        member.SetValue = value => field.SetValue(obj, value);
                    }
                    
                    result.Add(member);
                }
                catch (Exception ex)
                {
                    Logger.Warning($"Error inspecting field {field.Name}: {ex.Message}");
                }
            }
            
            // If we don't want base class members, break after processing the most derived type
            if (!includeBaseMembers && declaringType == type)
            {
                break;
            }
        }
        
        return result;
    }
    
    /// <summary>
    /// Attempts to set a value on a member of an object, with type conversion if necessary.
    /// </summary>
    /// <param name="member">The member to set</param>
    /// <param name="newValue">The new value to set</param>
    /// <returns>True if successful, false otherwise</returns>
    public static bool TrySetMemberValue(InspectableMember member, object? newValue)
    {
        if (member.IsReadOnly || member.SetValue == null)
        {
            Logger.Warning($"Cannot set value for read-only member {member.Name}");
            return false;
        }
        
        try
        {
            // If the new value is null and the target type is a value type, we can't set it
            if (newValue == null && member.Type.IsValueType)
            {
                Logger.Warning($"Cannot set null to value type {member.Type.Name}");
                return false;
            }
            
            // If the types match, set directly
            if (newValue == null || member.Type.IsAssignableFrom(newValue.GetType()))
            {
                member.SetValue(newValue);
                return true;
            }
            
            // Try to convert the value
            if (newValue is string stringValue)
            {
                // Handle string to other type conversions
                object? convertedValue = null;
                
                if (member.Type == typeof(int) && int.TryParse(stringValue, out int intValue))
                {
                    convertedValue = intValue;
                }
                else if (member.Type == typeof(float) && float.TryParse(stringValue, out float floatValue))
                {
                    convertedValue = floatValue;
                }
                else if (member.Type == typeof(double) && double.TryParse(stringValue, out double doubleValue))
                {
                    convertedValue = doubleValue;
                }
                else if (member.Type == typeof(bool) && bool.TryParse(stringValue, out bool boolValue))
                {
                    convertedValue = boolValue;
                }
                else if (member.Type.IsEnum)
                {
                    try
                    {
                        convertedValue = Enum.Parse(member.Type, stringValue);
                    }
                    catch
                    {
                        Logger.Warning($"Failed to parse enum value '{stringValue}' for type {member.Type.Name}");
                        return false;
                    }
                }
                
                if (convertedValue != null)
                {
                    member.SetValue(convertedValue);
                    return true;
                }
            }
            
            // Try using Convert class as a fallback
            try
            {
                object convertedValue = Convert.ChangeType(newValue, member.Type);
                member.SetValue(convertedValue);
                return true;
            }
            catch
            {
                Logger.Warning($"Failed to convert value from {newValue.GetType().Name} to {member.Type.Name}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Warning($"Error setting value for member {member.Name}: {ex.Message}");
            return false;
        }
    }
}