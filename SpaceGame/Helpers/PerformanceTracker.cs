using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Helpers;

namespace SpaceGame.Debugging;

/// <summary>
/// A lightweight performance tracking utility for measuring execution time of code sections.
/// Designed for performance optimization of generation algorithms and heavy computational tasks.
/// </summary>
public class PerformanceTracker
{
    // Store start and end timestamps for each timer
    private readonly Dictionary<string, DateTime> _startTimes = new();
    private readonly Dictionary<string, TimeSpan> _totalTimes = new();
    
    // Dictionary to store call counts by timer name
    private readonly Dictionary<string, int> _callCounts = new();
    
    // Track currently running timers
    private readonly HashSet<string> _runningTimers = new();
    
    /// <summary>
    /// Starts timing a section of code with the given name.
    /// </summary>
    /// <param name="timerName">Name of the timer/code section</param>
    public void StartTimer(string timerName)
    {
        // Record start time
        _startTimes[timerName] = DateTime.Now;
        _runningTimers.Add(timerName);
        
        // Initialize if this is the first call
        if (!_totalTimes.ContainsKey(timerName))
        {
            _totalTimes[timerName] = TimeSpan.Zero;
            _callCounts[timerName] = 0;
        }
    }
    
    /// <summary>
    /// Stops timing a section of code and adds the elapsed time to the total.
    /// </summary>
    /// <param name="timerName">Name of the timer/code section</param>
    public void StopTimer(string timerName)
    {
        if (!_runningTimers.Contains(timerName))
        {
            Logger.Warning($"Attempted to stop timer '{timerName}' that was not started");
            return;
        }
        
        // Remove from running timers
        _runningTimers.Remove(timerName);
        
        // Get start time
        if (!_startTimes.TryGetValue(timerName, out var startTime))
        {
            Logger.Warning($"Start time not found for timer '{timerName}'");
            return;
        }
        
        // Calculate elapsed time
        TimeSpan elapsed = DateTime.Now - startTime;
        
        // For nested timers (like TotalGeneration containing other timers),
        // we don't want to double-count the time, so we just record the actual wall-clock time
        if (timerName == "TotalGeneration")
        {
            _totalTimes[timerName] = elapsed;
        }
        else
        {
            // Add to total time for this timer
            _totalTimes[timerName] += elapsed;
        }
        
        // Increment call count
        _callCounts[timerName]++;
    }
    
    /// <summary>
    /// Gets the elapsed time in milliseconds for a specific timer.
    /// </summary>
    /// <param name="timerName">Name of the timer</param>
    /// <returns>Elapsed time in milliseconds, or 0 if the timer doesn't exist</returns>
    public double GetElapsedTime(string timerName)
    {
        if (!_totalTimes.TryGetValue(timerName, out var elapsed))
        {
            return 0;
        }
        
        return elapsed.TotalMilliseconds;
    }
    
    /// <summary>
    /// Gets all timers and their elapsed times in milliseconds.
    /// </summary>
    /// <returns>Dictionary mapping timer names to elapsed times in milliseconds</returns>
    public Dictionary<string, double> GetAllTimers()
    {
        var result = new Dictionary<string, double>();
        
        foreach (var kvp in _totalTimes)
        {
            result[kvp.Key] = kvp.Value.TotalMilliseconds;
        }
        
        return result;
    }
    
    /// <summary>
    /// Gets all timers with their call counts and average execution times.
    /// </summary>
    /// <returns>Dictionary mapping timer names to detailed performance metrics</returns>
    public Dictionary<string, PerformanceMetric> GetDetailedMetrics()
    {
        var result = new Dictionary<string, PerformanceMetric>();
        
        foreach (var timerName in _totalTimes.Keys)
        {
            var totalMs = GetElapsedTime(timerName);
            var callCount = _callCounts[timerName];
            var avgTimeMs = callCount > 0 ? totalMs / callCount : 0;
            
            result[timerName] = new PerformanceMetric(
                totalTimeMs: totalMs,
                callCount: callCount,
                avgTimeMs: avgTimeMs
            );
        }
        
        return result;
    }
    
    /// <summary>
    /// Resets all timers and call counts.
    /// </summary>
    public void Reset()
    {
        _startTimes.Clear();
        _totalTimes.Clear();
        _callCounts.Clear();
        _runningTimers.Clear();
    }
    
    /// <summary>
    /// Logs performance metrics to the console.
    /// </summary>
    public void LogMetrics()
    {
        Logger.Info("Performance Metrics:");
        
        // Sort by total time (descending)
        var sortedMetrics = GetDetailedMetrics()
            .OrderByDescending(m => m.Value.TotalTimeMs)
            .ToList();
            
        foreach (var kvp in sortedMetrics)
        {
            var metric = kvp.Value;
            Logger.Info($"  {kvp.Key}: {metric.TotalTimeMs:F2}ms total, {metric.CallCount} calls, {metric.AvgTimeMs:F2}ms avg");
        }
        
        // Calculate and log the sum of non-TotalGeneration timers to check for inconsistencies
        double sumOfComponents = sortedMetrics
            .Where(m => m.Key != "TotalGeneration")
            .Sum(m => m.Value.TotalTimeMs);
            
        if (_totalTimes.ContainsKey("TotalGeneration"))
        {
            double totalGenTime = _totalTimes["TotalGeneration"].TotalMilliseconds;
            double difference = Math.Abs(totalGenTime - sumOfComponents);
            double percentageDiff = (difference / totalGenTime) * 100;
            
            Logger.Info($"Sum of all component timers: {sumOfComponents:F2}ms");
            Logger.Info($"Difference from total: {difference:F2}ms ({percentageDiff:F2}%)");
            
            // Log warning if there's a significant discrepancy
            if (percentageDiff > 10)
            {
                Logger.Warning($"Significant timing discrepancy detected. This may indicate overlapping timers or untracked operations.");
            }
        }
    }
}

/// <summary>
/// Struct containing detailed performance metrics for a timer.
/// </summary>
public struct PerformanceMetric
{
    /// <summary>
    /// Total execution time in milliseconds
    /// </summary>
    public double TotalTimeMs { get; }
    
    /// <summary>
    /// Number of times the timer was started and stopped
    /// </summary>
    public int CallCount { get; }
    
    /// <summary>
    /// Average execution time in milliseconds per call
    /// </summary>
    public double AvgTimeMs { get; }
    
    public PerformanceMetric(double totalTimeMs, int callCount, double avgTimeMs)
    {
        TotalTimeMs = totalTimeMs;
        CallCount = callCount;
        AvgTimeMs = avgTimeMs;
    }
} 