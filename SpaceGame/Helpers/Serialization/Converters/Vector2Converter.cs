using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using <PERSON>ot;

namespace SpaceGame.Helpers.Serialization.Converters;

/// <summary>
/// JSON converter for Godot's Vector2 type
/// </summary>
public class Vector2Converter : JsonConverter<Vector2>
{
    /// <inheritdoc />
    public override Vector2 Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType != JsonTokenType.StartObject)
        {
            throw new JsonException("Expected start of object");
        }
        
        float x = 0, y = 0;
        
        while (reader.Read())
        {
            if (reader.TokenType == JsonTokenType.EndObject)
            {
                return new Vector2(x, y);
            }
            
            if (reader.TokenType != JsonTokenType.PropertyName)
            {
                throw new JsonException("Expected property name");
            }
            
            string propertyName = reader.GetString();
            reader.Read();
            
            switch (propertyName)
            {
                case "x":
                    x = reader.GetSingle();
                    break;
                case "y":
                    y = reader.GetSingle();
                    break;
                default:
                    throw new JsonException($"Unexpected property: {propertyName}");
            }
        }
        
        throw new JsonException("Expected end of object");
    }
    
    /// <inheritdoc />
    public override void Write(Utf8JsonWriter writer, Vector2 value, JsonSerializerOptions options)
    {
        writer.WriteStartObject();
        writer.WriteNumber("x", value.X);
        writer.WriteNumber("y", value.Y);
        writer.WriteEndObject();
    }
} 