using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SpaceGame.Helpers.Serialization.Converters;

/// <summary>
/// JSON converter for handling polymorphic types during serialization and deserialization.
/// This allows proper handling of inheritance and interfaces.
/// </summary>
/// <typeparam name="TBase">The base type or interface to handle</typeparam>
public class PolymorphicConverter<TBase> : JsonConverter<TBase> where TBase : class
{
    private readonly Dictionary<string, Type> _typeMap;
    private readonly Dictionary<Type, string> _typeToNameMap;
    private readonly JsonSerializerOptions _baseOptions;
    
    /// <summary>
    /// Creates a new instance of the PolymorphicConverter
    /// </summary>
    /// <param name="typeMappings">Optional manual type mappings (type name to Type)</param>
    /// <param name="baseOptions">Base JSON serializer options to use for nested serialization</param>
    public PolymorphicConverter(Dictionary<string, Type>? typeMappings = null, JsonSerializerOptions? baseOptions = null)
    {
        _typeMap = typeMappings ?? new Dictionary<string, Type>();
        _typeToNameMap = _typeMap.ToDictionary(kvp => kvp.Value, kvp => kvp.Key);
        _baseOptions = baseOptions ?? SerializationHelper.DefaultSerializationOptions;
        
        // If no mappings were provided, auto-discover types that inherit from TBase
        if (typeMappings == null || typeMappings.Count == 0)
        {
            AutoDiscoverTypes();
        }
    }
    
    /// <summary>
    /// Auto-discovers types that inherit from or implement TBase
    /// </summary>
    private void AutoDiscoverTypes()
    {
        var baseType = typeof(TBase);
        var assemblies = AppDomain.CurrentDomain.GetAssemblies();
        
        foreach (var assembly in assemblies)
        {
            try
            {
                var types = assembly.GetTypes()
                    .Where(t => !t.IsAbstract && !t.IsInterface && baseType.IsAssignableFrom(t));
                
                foreach (var type in types)
                {
                    var typeName = type.Name;
                    
                    if (!_typeMap.ContainsKey(typeName))
                    {
                        _typeMap[typeName] = type;
                        _typeToNameMap[type] = typeName;
                    }
                }
            }
            catch (ReflectionTypeLoadException)
            {
                // Skip assemblies that can't be loaded
                continue;
            }
        }
    }
    
    /// <inheritdoc />
    public override TBase Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType != JsonTokenType.StartObject)
        {
            throw new JsonException("Expected start of object");
        }
        
        // First, we need to clone the reader to look ahead for type information
        var readerCopy = reader;
        
        // Read the first property which should be the type discriminator
        string typeDiscriminator = null;
        
        while (readerCopy.Read())
        {
            if (readerCopy.TokenType == JsonTokenType.PropertyName)
            {
                string propertyName = readerCopy.GetString();
                if (propertyName == "$type")
                {
                    readerCopy.Read();
                    if (readerCopy.TokenType == JsonTokenType.String)
                    {
                        typeDiscriminator = readerCopy.GetString();
                    }
                    break;
                }
            }
            
            if (readerCopy.TokenType == JsonTokenType.EndObject)
            {
                // Reached end of object without finding type discriminator
                break;
            }
        }
        
        if (string.IsNullOrEmpty(typeDiscriminator) || !_typeMap.TryGetValue(typeDiscriminator, out var concreteType))
        {
            throw new JsonException($"Missing or unknown type discriminator. Expected one of: {string.Join(", ", _typeMap.Keys)}");
        }
        
        // Parse the document with the correct type
        // Use the original reader to read the full object
        using var doc = JsonDocument.ParseValue(ref reader);
        return (TBase)JsonSerializer.Deserialize(doc.RootElement.GetRawText(), concreteType, _baseOptions);
    }
    
    /// <inheritdoc />
    public override void Write(Utf8JsonWriter writer, TBase value, JsonSerializerOptions options)
    {
        if (value == null)
        {
            writer.WriteNullValue();
            return;
        }
        
        Type runtimeType = value.GetType();
        
        if (!_typeToNameMap.TryGetValue(runtimeType, out string typeName))
        {
            // If we don't have this type in our map, add it (auto-discovery)
            typeName = runtimeType.Name;
            _typeMap[typeName] = runtimeType;
            _typeToNameMap[runtimeType] = typeName;
        }
        
        // First, serialize the object to a JsonDocument
        string json = JsonSerializer.Serialize(value, runtimeType, _baseOptions);
        using var doc = JsonDocument.Parse(json);
        
        // Now write it out, adding the type discriminator
        writer.WriteStartObject();
        writer.WriteString("$type", typeName);
        
        // Copy all properties from the original document
        foreach (var property in doc.RootElement.EnumerateObject())
        {
            // Skip the type property if it somehow exists already
            if (property.Name == "$type")
            {
                continue;
            }
            
            property.WriteTo(writer);
        }
        
        writer.WriteEndObject();
    }
} 