using System;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Metadata for save files, providing additional information about the save
/// </summary>
public class SaveMetadata
{
    /// <summary>
    /// Name of the save (e.g., "Autosave", "Manual Save 1")
    /// </summary>
    public string SaveName { get; set; } = "Unnamed Save";
    
    /// <summary>
    /// Description of the save (e.g., player notes)
    /// </summary>
    public string Description { get; set; } = "";
    
    /// <summary>
    /// Timestamp when the save was created
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
    
    /// <summary>
    /// Version of the game that created the save
    /// </summary>
    public string GameVersion { get; set; } = "1.0.0";
}