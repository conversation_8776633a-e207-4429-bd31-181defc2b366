using System.Text.Json;
using System.Text.Json.Serialization;
using Godot;
using SpaceGame.Helpers.Serialization.Converters;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Implementation of ISerializationStrategy using System.Text.Json
/// </summary>
public class JsonSerializationStrategy : ISerializationStrategy
{
    private readonly JsonSerializerOptions _options;
    
    /// <summary>
    /// Creates a new instance of JsonSerializationStrategy with optional custom options
    /// </summary>
    /// <param name="options">Custom JSON serialization options, or null to use the default options</param>
    public JsonSerializationStrategy(JsonSerializerOptions? options = null)
    {
        _options = options ?? CreateDefaultOptions();
    }
    
    /// <summary>
    /// Creates the default JSON serialization options for this strategy.
    /// </summary>
    /// <returns>A configured JsonSerializerOptions instance.</returns>
    private JsonSerializerOptions CreateDefaultOptions()
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = false,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
        
        // Register Godot converters
        GodotConverters.RegisterGodotConverters(options);
        
        return options;
    }
    
    /// <inheritdoc />
    public string Serialize(object obj)
    {
        return SerializationHelper.SerializeToJson(obj, _options);
    }
    
    /// <inheritdoc />
    public T? Deserialize<T>(string serializedData)
    {
        return SerializationHelper.DeserializeFromJson<T>(serializedData, _options);
    }
    
    /// <inheritdoc />
    public bool SaveToFile(string filePath, object obj)
    {
        return SerializationHelper.SaveToFile(filePath, obj, _options);
    }
    
    /// <inheritdoc />
    public T? LoadFromFile<T>(string filePath)
    {
        return SerializationHelper.LoadFromFile<T>(filePath, _options);
    }
    
    /// <inheritdoc />
    public string GetFileExtension()
    {
        return ".json";
    }
} 