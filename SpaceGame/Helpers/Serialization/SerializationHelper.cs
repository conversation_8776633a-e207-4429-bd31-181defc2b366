using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using <PERSON>ot;
using FileAccess = Godot.FileAccess;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Provides utility methods for serializing and deserializing objects, especially for the save system.
/// </summary>
public static class SerializationHelper
{
    /// <summary>
    /// Default JSON serialization options
    /// </summary>
    public static readonly JsonSerializerOptions DefaultSerializationOptions = new()
    {
        WriteIndented = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
        Converters = { new JsonStringEnumConverter() }
    };
    
    /// <summary>
    /// Serializes an object to JSON.
    /// </summary>
    /// <param name="obj">The object to serialize.</param>
    /// <param name="options">JSON serialization options (uses DefaultSerializationOptions if null).</param>
    /// <returns>The serialized JSON string.</returns>
    public static string SerializeToJson(object obj, JsonSerializerOptions? options = null)
    {
        try
        {
            return JsonSerializer.Serialize(obj, options ?? DefaultSerializationOptions);
        }
        catch (Exception ex)
        {
            Logger.Error($"Failed to serialize object to JSON: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="options">JSON deserialization options (uses DefaultSerializationOptions if null).</param>
    /// <returns>The deserialized object.</returns>
    public static T? DeserializeFromJson<T>(string json, JsonSerializerOptions? options = null)
    {
        try
        {
            return JsonSerializer.Deserialize<T>(json, options ?? DefaultSerializationOptions);
        }
        catch (Exception ex)
        {
            Logger.Error($"Failed to deserialize JSON to {typeof(T).Name}: {ex.Message}");
            throw;
        }
    }
    
    /// <summary>
    /// Saves data to a file in JSON format.
    /// </summary>
    /// <param name="filePath">Path to save the file (uses Godot's file system paths).</param>
    /// <param name="data">The data to serialize and save.</param>
    /// <param name="options">JSON serialization options (uses DefaultSerializationOptions if null).</param>
    /// <returns>True if successful, false otherwise.</returns>
    public static bool SaveToFile(string filePath, object data, JsonSerializerOptions? options = null)
    {
        try
        {
            string json = SerializeToJson(data, options);
            
            // Use Godot's FileAccess for cross-platform compatibility
            using var file = FileAccess.Open(filePath, FileAccess.ModeFlags.Write);
            if (file == null)
            {
                var error = FileAccess.GetOpenError();
                Logger.Error($"Failed to open file for writing: {filePath}, Error: {error}");
                return false;
            }
            
            file.StoreString(json);
            Logger.Info($"Successfully saved data to {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            Logger.Error($"Failed to save data to file {filePath}: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Loads data from a JSON file.
    /// </summary>
    /// <typeparam name="T">The type to deserialize to.</typeparam>
    /// <param name="filePath">Path to the file to load (uses Godot's file system paths).</param>
    /// <param name="options">JSON deserialization options (uses DefaultSerializationOptions if null).</param>
    /// <returns>The deserialized object of type T, or default(T) if loading fails.</returns>
    public static T? LoadFromFile<T>(string filePath, JsonSerializerOptions? options = null)
    {
        try
        {
            if (!FileAccess.FileExists(filePath))
            {
                Logger.Warning($"File does not exist: {filePath}");
                return default;
            }
            
            using var file = FileAccess.Open(filePath, FileAccess.ModeFlags.Read);
            if (file == null)
            {
                var error = FileAccess.GetOpenError();
                Logger.Error($"Failed to open file for reading: {filePath}, Error: {error}");
                return default;
            }
            
            string json = file.GetAsText();
            return DeserializeFromJson<T>(json, options);
        }
        catch (Exception ex)
        {
            Logger.Error($"Failed to load data from file {filePath}: {ex.Message}");
            return default;
        }
    }
    
    /// <summary>
    /// Creates a backup of a file.
    /// </summary>
    /// <param name="filePath">Path to the file to back up.</param>
    /// <returns>True if backup was created successfully or the file didn't exist (no backup needed), false otherwise.</returns>
    public static bool CreateBackup(string filePath)
    {
        try
        {
            if (!FileAccess.FileExists(filePath))
            {
                // No file to back up
                return true;
            }
            
            // Create backup filename with timestamp
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string backupPath = $"{filePath}.{timestamp}.bak";
            
            // Copy the file
            Error copyResult = DirAccess.CopyAbsolute(filePath, backupPath);
            if (copyResult != Error.Ok)
            {
                Logger.Error($"Failed to create backup of {filePath}: {copyResult}");
                return false;
            }
            
            Logger.Info($"Created backup of {filePath} at {backupPath}");
            return true;
        }
        catch (Exception ex)
        {
            Logger.Error($"Exception while creating backup of {filePath}: {ex.Message}");
            return false;
        }
    }
} 