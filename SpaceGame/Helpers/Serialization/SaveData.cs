using System;
using System.Text.Json.Serialization;
using SpaceGame.State;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Represents the root structure of a save file, containing version information and save state data.
/// </summary>
public class SaveData
{
    /// <summary>
    /// Current version of the save file format
    /// </summary>
    public const int CURRENT_VERSION = 1;
    
    /// <summary>
    /// Version of the save file format
    /// </summary>
    public int Version { get; set; } = CURRENT_VERSION;
    
    /// <summary>
    /// Metadata about the save (e.g., player name, save description, timestamp)
    /// </summary>
    public SaveMetadata Metadata { get; set; } = new SaveMetadata();
    
    /// <summary>
    /// The save state data
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public SaveState? State { get; set; }
    
    /// <summary>
    /// Creates a new SaveData instance
    /// </summary>
    public SaveData()
    {
        // Required for serialization
    }
    
    /// <summary>
    /// Creates a new SaveData instance with the specified game state
    /// </summary>
    /// <param name="gameState">The game state to include in the save data</param>
    public SaveData(GameState gameState)
    {
        ArgumentNullException.ThrowIfNull(gameState);
        State = gameState.ToSaveState();
    }
    
    /// <summary>
    /// Creates a new SaveData instance with the specified save state and metadata
    /// </summary>
    /// <param name="state">The save state to include in the save data</param>
    /// <param name="metadata">Metadata for the save</param>
    public SaveData(SaveState state, SaveMetadata metadata)
    {
        State = state;
        Metadata = metadata;
    }
    
    /// <summary>
    /// Creates a new SaveData instance with the specified game state and metadata
    /// </summary>
    /// <param name="gameState">The game state to include in the save data</param>
    /// <param name="metadata">Metadata for the save</param>
    public SaveData(GameState gameState, SaveMetadata metadata)
    {
        ArgumentNullException.ThrowIfNull(gameState);
        State = gameState.ToSaveState();
        Metadata = metadata;
    }
    
    /// <summary>
    /// Gets the game state from this save data, converting from SaveState if necessary
    /// </summary>
    /// <returns>The GameState, or null if neither GameState nor SaveState is available</returns>
    public GameState? GetGameState()
    {
        if (State != null)
        {
            return GameState.FromSaveState(State);
        }
        
        // For backward compatibility
        return null;
    }
}