using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using Godot;
using SpaceGame.Helpers.Serialization.Converters;
using SpaceGame.Scripts;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Configuration class for serialization settings, providing methods to
/// create and configure serialization options for different scenarios.
/// </summary>
public static class SerializationConfig
{
    /// <summary>
    /// Default save file extension
    /// </summary>
    public const string DefaultSaveExtension = ".json";
    
    /// <summary>
    /// Default save file path template
    /// </summary>
    public const string DefaultSavePathTemplate = "user://saves/save_{0}";
    
    /// <summary>
    /// Default filename for autosaves
    /// </summary>
    public const string AutosaveFilename = "autosave";
    
    /// <summary>
    /// Directory where saves are stored
    /// </summary>
    public const string SavesDirectory = "user://saves";
    
    /// <summary>
    /// Creates a standard JsonSerializerOptions instance for game state serialization
    /// </summary>
    /// <returns>A configured JsonSerializerOptions instance</returns>
    public static JsonSerializerOptions CreateGameStateSerializationOptions()
    {
        var options = GodotConverters.CreateDefaultGodotOptions();
        
        // Configure reference handling to support circular references
        options.ReferenceHandler = ReferenceHandler.Preserve;
        
        return options;
    }
    
    /// <summary>
    /// Ensures the saves directory exists
    /// </summary>
    /// <returns>True if the directory exists or was created successfully, false otherwise</returns>
    public static bool EnsureSavesDirectoryExists()
    {
        try
        {
            if (!DirAccess.DirExistsAbsolute(SavesDirectory))
            {
                Error error = DirAccess.MakeDirAbsolute(SavesDirectory);
                if (error != Error.Ok)
                {
                    Logger.Error($"Failed to create saves directory {SavesDirectory}: {error}");
                    return false;
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            Logger.Error($"Error ensuring saves directory exists: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Generates a save file path based on a save name
    /// </summary>
    /// <param name="saveName">The name of the save</param>
    /// <returns>A full path to the save file</returns>
    public static string GetSaveFilePath(string saveName)
    {
        // Ensure the save name is valid for file paths
        string safeFileName = saveName.GetStableHashCode().ToString("X8");
        
        // Ensure the directory exists
        EnsureSavesDirectoryExists();
        
        return String.Format(DefaultSavePathTemplate, $"{safeFileName}{DefaultSaveExtension}");
    }
    
    /// <summary>
    /// Gets the path for the autosave file
    /// </summary>
    /// <returns>The full path to the autosave file</returns>
    public static string GetAutosaveFilePath()
    {
        return GetSaveFilePath(AutosaveFilename);
    }
    
    /// <summary>
    /// Creates a metadata object for a new save
    /// </summary>
    /// <param name="saveName">The display name for the save</param>
    /// <param name="description">Optional description of the save</param>
    /// <returns>A SaveMetadata object with the specified information</returns>
    public static SaveMetadata CreateSaveMetadata(string saveName, string description = "")
    {
        return new SaveMetadata
        {
            SaveName = saveName,
            Description = description,
            GameVersion = Engine.GetVersionInfo()["string"].AsString(),
            Timestamp = DateTime.Now,
            // Add other metadata as needed
        };
    }
    
    /// <summary>
    /// Handles version migrations for save data
    /// </summary>
    /// <param name="saveData">The save data to migrate</param>
    /// <returns>The migrated save data</returns>
    public static SaveData MigrateSaveData(SaveData saveData)
    {
        // Current version is already up to date
        if (saveData.Version == SaveData.CURRENT_VERSION)
            return saveData;
            
        Logger.Info($"Migrating save data from version {saveData.Version} to {SaveData.CURRENT_VERSION}");
        
        // Handle migrations based on the current version
        // As new save versions are added, add migration logic here
        
        switch (saveData.Version)
        {
            case 0:
                // Migration from version 0 to 1
                MigrateFromV0ToV1(saveData);
                // Fall through to next case for further migrations
                goto case 1;
                
            case 1:
                // Migration from version 1 to 2
                // This includes migrating from GameState to SaveState if needed
                MigrateFromV1ToV2(saveData);
                break;
                
            // Add more cases as new versions are introduced
                
            default:
                Logger.Warning($"Unknown save version: {saveData.Version}. Attempting to load without migration.");
                break;
        }
        
        // Update the version to the current version
        saveData.Version = SaveData.CURRENT_VERSION;
        
        return saveData;
    }
    
    /// <summary>
    /// Migrates save data from version 0 to version 1
    /// </summary>
    /// <param name="saveData">The save data to migrate</param>
    private static void MigrateFromV0ToV1(SaveData saveData)
    {
        // Example migration logic
        Logger.Info("Migrating save data from version 0 to version 1");
        
        // Version 1 introduced SaveMetadata
        if (saveData.Metadata == null)
        {
            saveData.Metadata = new SaveMetadata
            {
                SaveName = "Migrated Save",
                Description = "This save was migrated from version 0 to version 1",
                Timestamp = DateTime.Now,
                GameVersion = Engine.GetVersionInfo()["string"].AsString()
            };
        }
        
        // Add other migration logic for version 0 to version 1
    }
    
    /// <summary>
    /// Migrates save data from version 1 to version 2
    /// This includes migrating from GameState to SaveState structure
    /// </summary>
    /// <param name="saveData">The save data to migrate</param>
    private static void MigrateFromV1ToV2(SaveData saveData)
    {
        Logger.Info("Migrating save data from version 1 to version 2");
    }
} 