using System;
using System.Text.Json.Serialization;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Attribute that marks a property or field to be excluded from serialization.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
public class DoNotSerializeAttribute : Attribute
{
}

/// <summary>
/// Attribute that marks a property or field to be specially handled during serialization.
/// Used to provide custom type information for polymorphic serialization.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
public class SerializeAsTypeAttribute : Attribute
{
    /// <summary>
    /// The type to use during serialization
    /// </summary>
    public Type SerializationType { get; }
    
    /// <summary>
    /// Creates a new instance of SerializeAsTypeAttribute with the specified type
    /// </summary>
    /// <param name="serializationType">The type to use during serialization</param>
    public SerializeAsTypeAttribute(Type serializationType)
    {
        SerializationType = serializationType;
    }
} 