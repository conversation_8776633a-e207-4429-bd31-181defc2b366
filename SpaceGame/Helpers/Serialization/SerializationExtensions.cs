using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;

namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Extension methods for working with serialization
/// </summary>
public static class SerializationExtensions
{
    /// <summary>
    /// Gets properties that should be serialized (not marked with DoNotSerialize attribute)
    /// </summary>
    /// <param name="type">The type to examine</param>
    /// <returns>An enumerable of PropertyInfo representing serializable properties</returns>
    public static IEnumerable<PropertyInfo> GetSerializableProperties(this Type type)
    {
        return type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.CanRead && !p.IsDefined(typeof(DoNotSerializeAttribute)));
    }
    
    /// <summary>
    /// Gets all properties that have a specific attribute
    /// </summary>
    /// <typeparam name="TAttribute">The attribute type to look for</typeparam>
    /// <param name="type">The type to examine</param>
    /// <returns>An enumerable of PropertyInfo with the specified attribute</returns>
    public static IEnumerable<PropertyInfo> GetPropertiesWithAttribute<TAttribute>(this Type type)
        where TAttribute : Attribute
    {
        return type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.IsDefined(typeof(TAttribute)));
    }
    
    /// <summary>
    /// Checks if a property has a DoNotSerialize attribute
    /// </summary>
    /// <param name="property">The property to check</param>
    /// <returns>True if the property should not be serialized, false otherwise</returns>
    public static bool ShouldNotSerialize(this PropertyInfo property)
    {
        return property.IsDefined(typeof(DoNotSerializeAttribute));
    }
    
    /// <summary>
    /// Gets the serialization type for a property, if specified with SerializeAsType attribute
    /// </summary>
    /// <param name="property">The property to check</param>
    /// <returns>The type to use for serialization, or null if not specified</returns>
    public static Type? GetSerializationType(this PropertyInfo property)
    {
        var attr = property.GetCustomAttribute<SerializeAsTypeAttribute>();
        return attr?.SerializationType;
    }
    
    /// <summary>
    /// Creates a deep clone of an object using JSON serialization/deserialization
    /// </summary>
    /// <typeparam name="T">The type of object to clone</typeparam>
    /// <param name="obj">The object to clone</param>
    /// <param name="options">Optional JsonSerializerOptions to use</param>
    /// <returns>A deep clone of the object</returns>
    public static T? DeepClone<T>(this T obj, JsonSerializerOptions? options = null)
    {
        if (obj == null) return default;
        
        options ??= SerializationHelper.DefaultSerializationOptions;
        string json = JsonSerializer.Serialize(obj, options);
        return JsonSerializer.Deserialize<T>(json, options);
    }
} 