namespace SpaceGame.Helpers.Serialization;

/// <summary>
/// Interface for implementing different serialization strategies (JSON, XML, YAML, etc.)
/// </summary>
public interface ISerializationStrategy
{
    /// <summary>
    /// Serializes an object to a string representation
    /// </summary>
    /// <param name="obj">The object to serialize</param>
    /// <returns>The serialized string representation</returns>
    string Serialize(object obj);
    
    /// <summary>
    /// Deserializes a string to an object of type T
    /// </summary>
    /// <typeparam name="T">The type to deserialize to</typeparam>
    /// <param name="serializedData">The serialized string to deserialize</param>
    /// <returns>The deserialized object</returns>
    T? Deserialize<T>(string serializedData);
    
    /// <summary>
    /// Saves an object to a file
    /// </summary>
    /// <param name="filePath">The path to save the file to</param>
    /// <param name="obj">The object to serialize and save</param>
    /// <returns>True if successful, false otherwise</returns>
    bool SaveToFile(string filePath, object obj);
    
    /// <summary>
    /// Loads an object from a file
    /// </summary>
    /// <typeparam name="T">The type to deserialize to</typeparam>
    /// <param name="filePath">The path to load the file from</param>
    /// <returns>The deserialized object</returns>
    T? LoadFromFile<T>(string filePath);
    
    /// <summary>
    /// Gets the file extension used by this serialization strategy
    /// </summary>
    /// <returns>The file extension (e.g., ".json", ".xml", ".yaml")</returns>
    string GetFileExtension();
} 