using System;
using System.Collections.Generic;
using <PERSON><PERSON>;

namespace SpaceGame.Helpers;

/// <summary>
/// Extension methods for collections like lists, arrays, etc.
/// </summary>
public static class CollectionExtensions
{
    /// <summary>
    /// Shuffles the elements of a list in place using the Fisher-Yates algorithm.
    /// </summary>
    /// <typeparam name="T">The type of elements in the list</typeparam>
    /// <param name="list">The list to shuffle</param>
    /// <param name="rng">Optional random number generator to use. If null, uses Godot's global RNG.</param>
    public static void Shuffle<T>(this IList<T> list, RandomNumberGenerator rng = null)
    {
        int n = list.Count;
        while (n > 1)
        {
            n--;
            int k;
            
            if (rng != null)
            {
                // Use the provided RNG if available
                k = rng.RandiRange(0, n);
            }
            else
            {
                // Fall back to <PERSON><PERSON>'s global RNG
                k = GD.RandRange(0, n);
            }
            
            (list[k], list[n]) = (list[n], list[k]);
        }
    }
} 