using System;
using System.Linq;
using Godot;
using ImGuiNET;
using SpaceGame.Debug;
using SpaceGame.Scripts.Battle;
using SpaceGame.Scripts.Events;
using SpaceGame.Helpers;

namespace SpaceGame.Universe
{
    public partial class Game
    {
        private static bool _autoStartBattle = true;
        private string _selectedBattleConfig = string.Empty;
        private int _selectedBattleConfigIndex = 0;
        private string _lastBattleStartResult = string.Empty;
        private bool _showBattleStartResult = false;
        private float _battleResultDisplayTime = 0f;
        private const float BATTLE_RESULT_DISPLAY_DURATION = 5.0f; // Show result message for 5 seconds

        /// <summary>
        /// Draws the battle testing window in the ImGui Debug UI.
        /// </summary>
        private void DrawBattleTestingWindow()
        {
            ImGui.Text("Battle Configuration Testing");
            ImGui.Separator();

            // Get all available battle configurations
            string[] configNames = BattleConfigManager.GetConfigNames();

            if (configNames.Length == 0)
            {
                ImGui.TextColored(new System.Numerics.Vector4(1.0f, 0.5f, 0.5f, 1.0f),
                    "No battle configurations available.");
                return;
            }

            if (string.IsNullOrEmpty(_selectedBattleConfig))
            {
                _selectedBattleConfig = configNames[0];
            }

            // Battle configuration selection dropdown
            if (ImGui.Combo("Battle Configuration", ref _selectedBattleConfigIndex, configNames, configNames.Length))
            {
                _selectedBattleConfig = configNames[_selectedBattleConfigIndex];
                Logger.Debug($"Selected battle configuration: {_selectedBattleConfig}");
            }

            // Display the selected configuration details
            if (!string.IsNullOrEmpty(_selectedBattleConfig))
            {
                BattleConfig config = BattleConfigManager.GetConfig(_selectedBattleConfig);
                if (config != null)
                {
                    ImGui.Separator();
                    ImGui.Text("Configuration Details:");
                    ImGui.Indent(10);
                    ImGui.Text($"Battle ID: {config.BattleId}");
                    ImGui.Text($"Battle Type: {config.BattleType}");
                    ImGui.Text($"Difficulty Level: {config.DifficultyLevel}");
                    ImGui.Text($"Player Ship Type: {config.PlayerShipType}");
                    ImGui.Text($"Max Enemy Ships: {config.MaxEnemyShips}");
                    ImGui.Text($"Background Scene: {config.BackgroundScene}");

                    // Display additional data if any
                    if (config.AdditionalData.Count > 0)
                    {
                        ImGui.Text("Additional Data:");
                        ImGui.Indent(10);
                        foreach (var key in config.AdditionalData.Keys)
                        {
                            ImGui.Text($"{key}: {config.AdditionalData[key]}");
                        }

                        ImGui.Unindent(10);
                    }

                    ImGui.Unindent(10);
                }
            }

            ImGui.Separator();

            // Start Battle button
            if ((ImGui.Button("Start Battle") || _autoStartBattle) && !string.IsNullOrEmpty(_selectedBattleConfig))
            {
                _autoStartBattle = false;
                BattleConfig config = BattleConfigManager.GetConfig(_selectedBattleConfig);
                if (config != null)
                {
                    bool result = BattleEvents.RaiseStartBattle(config);
                    _lastBattleStartResult = result
                        ? $"Successfully started battle: {_selectedBattleConfig}"
                        : $"Failed to start battle: {_selectedBattleConfig}";
                    _showBattleStartResult = true;
                    _battleResultDisplayTime = 0f;
                    Logger.Info(_lastBattleStartResult);
                }
                else
                {
                    _lastBattleStartResult = $"Failed to get battle configuration: {_selectedBattleConfig}";
                    _showBattleStartResult = true;
                    _battleResultDisplayTime = 0f;
                    Logger.Error(_lastBattleStartResult);
                }
            }

            // Display the result message if needed
            if (_showBattleStartResult)
            {
                ImGui.Separator();
                System.Numerics.Vector4 color = _lastBattleStartResult.Contains("Failed")
                    ? new System.Numerics.Vector4(1.0f, 0.3f, 0.3f, 1.0f) // Red for failure
                    : new System.Numerics.Vector4(0.3f, 1.0f, 0.3f, 1.0f); // Green for success

                ImGui.TextColored(color, _lastBattleStartResult);

                // Update the display time
                _battleResultDisplayTime += (float)GetProcessDeltaTime();
                if (_battleResultDisplayTime >= BATTLE_RESULT_DISPLAY_DURATION)
                {
                    _showBattleStartResult = false;
                }
            }
        }

        /// <summary>
        /// Initializes the battle debug system.
        /// </summary>
        private void InitializeBattleDebug()
        {
            // Subscribe to battle events for debug purposes
            BattleEvents.BattleStarted += OnBattleStarted;
            BattleEvents.BattleCompleted += OnBattleCompleted;
        }

        /// <summary>
        /// Cleans up the battle debug system.
        /// </summary>
        private void CleanupBattleDebug()
        {
            // Unsubscribe from battle events
            BattleEvents.BattleStarted -= OnBattleStarted;
            BattleEvents.BattleCompleted -= OnBattleCompleted;
        }

        /// <summary>
        /// Called when a battle is started.
        /// </summary>
        /// <param name="config">The battle configuration.</param>
        private void OnBattleStarted(BattleConfig config)
        {
            Logger.Debug(
                $"Battle started: {config.BattleId} (Type: {config.BattleType}, Difficulty: {config.DifficultyLevel})");
        }

        /// <summary>
        /// Called when a battle is completed.
        /// </summary>
        /// <param name="playerVictory">Whether the player won the battle.</param>
        private void OnBattleCompleted(bool playerVictory)
        {
            Logger.Debug($"Battle completed. Player victory: {playerVictory}");
        }
    }
}