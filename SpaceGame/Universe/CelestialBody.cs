using System;
using System.Data.Common;
using System.Collections.Generic;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe;

/// <summary>
/// Base class for all celestial bodies in the universe.
/// </summary>
public abstract partial class CelestialBody : Node2D
{
    public CelestialBodyData Data { get; protected set; }

    public CelestialBodySize Size => Data.Size;
    
    public float RenderScale { get; protected set; } = 1.0f; // Scale factor for rendering
    protected bool IsSelected = false;
    
    // Common sprite for all celestial bodies
    protected Sprite2D? _sprite;
    protected bool _spriteLoaded = false;
    
    // Dictionary to keep track of feature sprites
    protected Dictionary<CelestialFeature, Sprite2D> _featureSprites = new();
    // Node to parent all feature sprites
    protected Node2D? _featuresContainer;
    
    public Vector2 Position
    {
        get => GlobalPosition;
        set => GlobalPosition = value;
    }
    
    public HexCoordinates HexPosition => HexUtils.WorldToAxial(Position);
    
    public override void _Ready()
    {
        base._Ready();
        
        ProcessMode = ProcessModeEnum.Disabled;
        SetProcessInput(false);
        SetPhysicsProcess(false);
        
        // Add to celestial bodies group
        AddToGroup("celestial_bodies");
        
        // Initialize sprite if it doesn't exist yet
        if (_sprite == null)
        {
            // Try to get an existing sprite first (might be added in the scene)
            _sprite = GetNodeOrNull<Sprite2D>("Sprite2D");
            
            // If no sprite exists in scene, create one
            if (_sprite == null)
            {
                _sprite = new Sprite2D();
                _sprite.Name = "Sprite2D";
                AddChild(_sprite);
            }
        }
        
        // Set name from data if available
        if (Data != null && !string.IsNullOrEmpty(Data.Name))
        {
            Name = Data.Name;
        }
        
        // Load texture if we have data available
        if (Data != null)
        {
            LoadTexture();
        }
    }

    /// <summary>
    /// Loads the appropriate texture for this celestial body using the texture manager
    /// </summary>
    protected virtual void LoadTexture()
    {
        if (_spriteLoaded || _sprite == null) return;
        
        string texturePath = CelestialBodyTextureManager.GetTexturePathForBody(Data);
        
        if (!string.IsNullOrEmpty(texturePath))
        {
            LoadSpriteTexture(texturePath);
        }
        else
        {
            Logger.Warning($"No texture path found for {Data.BodyType} '{Data.Name}'");
        }
    }

    private void EnsureFeaturesContainer()
    {
        if (_featuresContainer == null)
        {
            _featuresContainer = new Node2D();
            _featuresContainer.Name = "Features";
            AddChild(_featuresContainer);
        }
    }

    protected virtual float GetRenderRadius()
    {
        // Base render radius calculation based on size enum
        float baseRadius = Data.Size switch
        {
            CelestialBodySize.Tiny => 5.0f,
            CelestialBodySize.Small => 8.0f,
            CelestialBodySize.Medium => 12.0f,
            CelestialBodySize.Large => 18.0f,
            CelestialBodySize.Huge => 25.0f,
            _ => 10.0f
        };
        
        return baseRadius * RenderScale;
    }
    
    public virtual float GetSelectionRadius()
    {
        // Make selection radius slightly larger than render radius for easier selection
        return GetRenderRadius() * 2.0f;
    }
    
    public virtual void Initialize(CelestialBodyData data)
    {
        Data = data;
        if (!string.IsNullOrEmpty(data.Name))
        {
            Name = data.Name;
        }
        
        UpdateRenderScaleForBodyType();
        
        // Initialize any features
        InitializeFeatures();
        
        // Load texture now that we have data
        LoadTexture();
        
        QueueRedraw();
    }
    
    /// <summary>
    /// Updates the render scale based on celestial body type
    /// </summary>
    protected virtual void UpdateRenderScaleForBodyType()
    {
        // Get the render scale based on body type from our configuration
        RenderScale = CelestialBodyRenderScale.GetRenderScale(Data);
        
        // Update the sprite scale if loaded
        UpdateSpriteScale();
    }
    
    // Legacy initialize method for backward compatibility
    protected virtual void Initialize(string name, CelestialBodySize size, CelestialBodyTemperature temperature)
    {
        var data = new CelestialBodyData
        {
            Name = name,
            Size = size,
            Temperature = temperature,
            Position = HexPosition
        };
        
        Initialize(data);
    }

    public virtual void OnSelected()
    {
        SetSelected(true);
    }
    
    public virtual void Deselect()
    {
        SetSelected(false);
    }
    
    public virtual string GetInfoText()
    {
        return Data.GetInfoText();
    }

    protected virtual void LoadSpriteTexture(string texturePath)
    {
        if (_sprite == null) return;
        
        var texture = GD.Load<Texture2D>(texturePath);
        if (texture != null)
        {
            _sprite.Texture = texture;
            _sprite.Scale = Vector2.One * RenderScale;
            _spriteLoaded = true;
        }
        else
        {
            Logger.Error($"Failed to load texture: {texturePath}");
        }
    }
    
    protected virtual void UpdateSpriteScale()
    {
        if (_sprite == null || _sprite.Texture == null) return;
        
        _sprite.Scale = Vector2.One * RenderScale;
        
        // Also update feature sprites
        UpdateFeatureSprites();
    }
    
    public virtual void SetSpriteTexture(Texture2D? texture)
    {
        if (_sprite == null) return;
        
        _sprite.Texture = texture;
        if (texture != null)
        {
            _spriteLoaded = true;
            UpdateSpriteScale();
        }
        else
        {
            _spriteLoaded = false;
        }
    }
    
    public void SetSelected(bool selected)
    {
        Logger.Debug($"{GetType().Name}.SetSelected({selected})");
        IsSelected = selected;

        if (selected)
        {
            UniverseEvents.RaiseCelestialBodySelected(this);
        }
        
        // Apply modulation directly when selection changes
        if (_sprite != null)
        {
            _sprite.Modulate = IsSelected ? new Color(1.1f, 1.1f, 1.1f) : Colors.White;
        }
    }
    
    /// <summary>
    /// Initializes all features from the CelestialBodyData
    /// </summary>
    protected virtual void InitializeFeatures()
    {
        if (Data == null) return;
        
        EnsureFeaturesContainer();
        
        // Clean up existing feature sprites first
        foreach (var sprite in _featureSprites.Values)
        {
            sprite.QueueFree();
        }
        _featureSprites.Clear();
        
        // Define maximum safe orbit radius (about 70% of HEX_SIZE)
        float maxSafeOrbitRadius = HexUtils.HEX_SIZE * 0.7f;
        
        // Add new feature sprites and constrain orbit radii
        foreach (var feature in Data.Features)
        {
            // Ensure orbit radius is within hex bounds
            if (feature.OrbitRadius > 0)
            {
                feature.ConstrainOrbitRadius(maxSafeOrbitRadius);
            }
            
            CreateFeatureSprite(feature);
        }
    }
    
    /// <summary>
    /// Creates a sprite for a celestial feature
    /// </summary>
    protected virtual void CreateFeatureSprite(CelestialFeature feature)
    {
        if (_featuresContainer == null) return;
        
        // If the feature doesn't have a texture path, try to get one from the texture manager
        if (string.IsNullOrEmpty(feature.TexturePath))
        {
            feature.TexturePath = CelestialBodyTextureManager.GetTexturePathForFeature(feature.Type, feature.Name);
            
            if (string.IsNullOrEmpty(feature.TexturePath))
            {
                Logger.Warning($"No texture path found for feature {feature.Type} '{feature.Name}'");
                return;
            }
        }
        
        var texture = GD.Load<Texture2D>(feature.TexturePath);
        if (texture == null)
        {
            Logger.Error($"Failed to load feature texture: {feature.TexturePath}");
            return;
        }
        
        var sprite = new Sprite2D();
        sprite.Name = $"Feature_{feature.Name}";
        sprite.Texture = texture;
        
        // Apply feature scaling and positioning
        UpdateFeatureSpriteTransform(sprite, feature);
        
        _featuresContainer.AddChild(sprite);
        _featureSprites[feature] = sprite;
    }
    
    /// <summary>
    /// Updates the transform of a feature sprite
    /// </summary>
    protected virtual void UpdateFeatureSpriteTransform(Sprite2D sprite, CelestialFeature feature)
    {
        // Scale based on the feature's scale value
        sprite.Scale = Vector2.One * feature.Scale;
        
        // Position based on feature offset
        sprite.Position = feature.Offset;
        
        // Ensure we're displaying over the parent
        sprite.ZIndex = ZIndex + 1;
        
        // If the feature has an orbit radius, position it along the orbit
        if (feature.OrbitRadius > 0)
        {
            // Default position is to the right (0 degrees)
            sprite.Position = new Vector2(feature.OrbitRadius, 0) + feature.Offset;
        }
    }
    
    /// <summary>
    /// Updates all feature sprites scales and positions
    /// </summary>
    protected virtual void UpdateFeatureSprites()
    {
        foreach (var pair in _featureSprites)
        {
            UpdateFeatureSpriteTransform(pair.Value, pair.Key);
        }
    }
    
    public override void _Process(double delta)
    {
        base._Process(delta);
        
        // Update orbiting features
        UpdateOrbitingFeatures((float)delta);
    }
    
    /// <summary>
    /// Updates the position of orbiting features
    /// </summary>
    protected virtual void UpdateOrbitingFeatures(float delta)
    {
        foreach (var pair in _featureSprites)
        {
            var feature = pair.Key;
            var sprite = pair.Value;
            
            if (feature.OrbitRadius > 0 && feature.OrbitSpeed != 0)
            {
                // Calculate orbit angle based on time, adding the initial angle offset
                // (which is encoded in the Offset property we set during generation)
                float initialAngle = 0;
                if (feature.Offset != Vector2.Zero && feature.OrbitRadius > 0)
                {
                    // Calculate the initial angle from the offset
                    initialAngle = Mathf.Atan2(feature.Offset.Y, feature.Offset.X + feature.OrbitRadius);
                }
                
                float timeComponent = (Time.GetTicksMsec() / 1000.0f) * feature.OrbitSpeed;
                float angle = timeComponent + initialAngle;
                
                // Calculate the base orbital position
                Vector2 orbitalPosition = new Vector2(
                    Mathf.Cos(angle) * feature.OrbitRadius,
                    Mathf.Sin(angle) * feature.OrbitRadius
                );
                
                // Constrain the position to stay within the hex
                Vector2 constrainedPosition = HexUtils.ConstrainToHex(orbitalPosition, Vector2.Zero);
                
                // Apply the constrained position
                sprite.Position = constrainedPosition;
                
                // Apply rotation if specified
                if (feature.RotationSpeed != 0)
                {
                    sprite.Rotation = delta * feature.RotationSpeed;
                }
            }
        }
    }
} 