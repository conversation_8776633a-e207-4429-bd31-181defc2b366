using System;
using System.Collections.Generic;
using SpaceGame.Helpers;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Generation;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Debug;

/// <summary>
/// Generates sample celestial bodies for debugging purposes.
/// Centralizes all debug object generation in one place.
/// </summary>
public class SampleObjectGenerator
{
    private readonly CelestialBodyGenerator _generator;

    public SampleObjectGenerator() {}

    /// <summary>
    /// Generates sample objects for debugging - one of each planet type and cosmic object type.
    /// </summary>
    public UniverseModel GenerateSampleObjects()
    {
        Logger.Info("Generating sample objects for debugging");
        
        // Center position
        HexCoordinates center = HexCoordinates.Zero;
        
        // Create a new universe model
        var universe = _generator.InitializeEmptyUniverse(name: "Sample Universe", seed: "Sample", size: 20);
        // Generate a star at the center
        var centralStar = _generator.GenerateCelestialBodyOfType(center, CelestialBodyType.Star);
        universe.AddCelestialBody(center, centralStar);
        
        int totalObjects = 0;
        var radius = 1;
        
        List<HexCoordinates> ringCoordinates = HexUtils.GetSpiral(center, radius);
        var celestialBodyTypes = Enum.GetValues(typeof(CelestialBodyType));

        while (ringCoordinates.Count <= celestialBodyTypes.Length)
        {
            radius++;
            ringCoordinates = HexUtils.GetSpiral(center, radius);
        }
        
        Logger.Debug($"Got {ringCoordinates.Count} spaces for {celestialBodyTypes.Length} celestial body types");
        
        var currentCoordinateIndex = 0;
        
        // Generate one of each planet type in the first ring
        foreach (CelestialBodyType celestialBodyType in celestialBodyTypes)
        {
            var currentPos = ringCoordinates[currentCoordinateIndex];
            try
            {
                while (universe.IsOccupied(currentPos))
                {
                    Logger.Debug($"Coordinate {currentPos} is occupied by {universe.GetCelestialBody(currentPos)}, skipping");
                    currentCoordinateIndex++;
                    currentPos = ringCoordinates[currentCoordinateIndex];
                }
                
                var planet = _generator.GenerateCelestialBodyOfType(currentPos, celestialBodyType);
                if (universe.AddCelestialBody(currentPos, planet))
                {
                    Logger.Debug($"Planet {planet} added at {currentPos}");
                    currentCoordinateIndex++;
                    totalObjects++;
                }
            } catch (Exception e)
            {
                Logger.Error($"Error generating celestial body of type {celestialBodyType}: {e.Message}");
            }
        }

        Logger.Info($"Generated {totalObjects} sample objects for debugging");

        return universe;
    }
} 