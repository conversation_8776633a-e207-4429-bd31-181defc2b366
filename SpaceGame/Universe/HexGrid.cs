using Godot;
using System.Collections.Generic;
using SpaceGame.Helpers;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe;

/// <summary>
/// Handles hexagonal grid layout and coordinate conversions.
/// Uses axial coordinate system (q,r) for hex grid operations.
/// Implemented as a singleton to ensure only one instance exists.
/// </summary>
public class HexGrid
{
    // Singleton instance
    private static HexGrid? _instance;
    
    // Size of the hexagon (distance from center to corner)
    public float HexSize { get; private set; }
    
    // Dictionary to track which hex cells are occupied
    private Dictionary<HexCoordinates, bool> _occupiedCells = new();
    
    /// <summary>
    /// Get the singleton instance of HexGrid
    /// </summary>
    /// <returns>The singleton instance</returns>
    public static HexGrid GetInstance()
    {
        return _instance ??= new HexGrid(HexUtils.HEX_SIZE);
    }
    
    // Private constructor to enforce singleton pattern
    private HexGrid(float hexSize)
    {
        HexSize = hexSize;
    }
    
    public void Clear()
    {
        Logger.Debug($"Clear HexGrid");
        _occupiedCells.Clear();
    }
    
    /// <summary>
    /// Marks a hex cell as occupied
    /// </summary>
    public void MarkOccupied(HexCoordinates pos)
    {
        _occupiedCells[pos] = true;
    }
    
    /// <summary>
    /// Checks if a hex cell is occupied
    /// </summary>
    public bool IsOccupied(HexCoordinates pos)
    {
        return _occupiedCells.ContainsKey(pos);
    }
    
    /// <summary>
    /// Generates a ring of hex coordinates at the given radius around (0,0)
    /// </summary>
    public List<HexCoordinates> GetRing(int radius)
    {
        List<HexCoordinates> results = new List<HexCoordinates>();
        
        if (radius <= 0) 
        {
            if (radius == 0)
            {
                results.Add(new HexCoordinates(0, 0)); // Origin
            }
            return results;
        }
        
        // Start at the leftmost position in the ring
        int q = 0;
        int r = -radius;
        
        // The six directions to move in a hex grid
        (int q, int r)[] directions = 
        {
            (1, 0),  // East
            (0, 1),  // Southeast
            (-1, 1), // Southwest
            (-1, 0), // West
            (0, -1), // Northwest
            (1, -1)  // Northeast
        };
        
        // For each side of the ring
        for (int side = 0; side < 6; side++)
        {
            // Move radius steps along this side
            for (int step = 0; step < radius; step++)
            {
                results.Add(new HexCoordinates(q, r));
                q += directions[side].q;
                r += directions[side].r;
            }
        }
        
        return results;
    }
    
    /// <summary>
    /// Generates hex coordinates for all cells within the given radius from (0,0)
    /// </summary>
    public List<HexCoordinates> GetSpiral(int radius)
    {
        List<HexCoordinates> results = new List<HexCoordinates>();
        
        for (int r = 0; r <= radius; r++)
        {
            results.AddRange(GetRing(r));
        }
        
        return results;
    }
    
    /// <summary>
    /// Gets information about a specific hex cell
    /// </summary>
    public HexInfo GetHexInfo(HexCoordinates pos)
    {
        Vector2 position = HexUtils.AxialToWorld(pos);
        bool isOccupied = IsOccupied(pos);
        
        return new HexInfo
        {
            Q = pos.Q,
            R = pos.R,
            WorldPosition = position,
            IsOccupied = isOccupied
        };
    }
    
    /// <summary>
    /// Structure to hold information about a hex cell
    /// </summary>
    public struct HexInfo
    {
        public int Q { get; set; }
        public int R { get; set; }
        public Vector2 WorldPosition { get; set; }
        public bool IsOccupied { get; set; }
        
        public override string ToString()
        {
            return $"Hex({Q},{R}) at {WorldPosition}, Occupied: {IsOccupied}";
        }
    }
}