using Godot;
using ImGuiGodot;
using ImGuiNET;
using R3;
using SpaceGame.Debug;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;
using SpaceGame.Scripts.Managers;
using SpaceGame.State;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe;

/// <summary>
/// The main scene for the game view.
/// It does not contain any game logic, but manages the camera and UI.
/// </summary>
public partial class Game : Control
{
    private UniverseRoot _universeRoot;
    private Node2D _battleRoot;

    // Access to the unified manager facade
    private GameStateManager _gameStateManager => GameStateManager.Instance;
    private GameState _gameState => _gameStateManager.GetGameState();
    private UniverseModel _currentUniverseModel => _gameState.CurrentUniverse;

    private ReadOnlyReactiveProperty<GameViewState> _viewState => _gameStateManager.CurrentViewState;

    private DisposableBag _disposables;

    public override void _Ready()
    {
        Logger.Info($"UniverseScene is starting up...");
        
        ObservableTracker.EnableTracking = true;
        ObservableTracker.EnableStackTrace = true;

        // Initialize overrides before anything else that might depend on them
        OverrideManager.InitializeOverrides();
        
        // Get references to our viewport nodes
        _universeRoot = GetNode<UniverseRoot>("Universe");
        _battleRoot = GetNode<Node2D>("BattleRoot");
        
        if (!_gameStateManager.LoadGame())
        {
            _gameStateManager.InitializeGameState();
        }
        
        _viewState.Subscribe(HandleViewStateChange)
            .AddTo(ref _disposables);

        _universeRoot.GenerateOrDisplayUniverse();
        
        // Subscribe to registry changes
        DebugRegistry.OnRegistryChanged += OnRegistryChanged;
        
        // Initialize the battle debug system
        InitializeBattleDebug();

        CoreGameEvents.RaiseGameStarted();
    }
    
    public override void _ExitTree()
    {
        // Clean up the battle debug system
        CleanupBattleDebug();
        
        // Dispose of any disposable resources
        // Since DisposableBag is a struct, we don't need to check for null
        _disposables.Dispose();

        DebugRegistry.OnRegistryChanged -= OnRegistryChanged;

        Logger.Info("UniverseScene is shutting down...");
    }

    private void HandleViewStateChange(GameViewState viewState)
    {
        Logger.Debug($"Handling view state change: {viewState}");
        UpdateUniverseRootActiveState(viewState == GameViewState.UNIVERSE_VIEW);
        UpdateBattleRootActiveState(viewState == GameViewState.SPACE_BATTLE_VIEW);
    }

    private void UpdateBattleRootActiveState(bool active)
    {
        SetNodeActiveProperties(_battleRoot, active);
    }

    private void UpdateUniverseRootActiveState(bool active)
    {
        SetNodeActiveProperties(_universeRoot, active);
    }

    private void SetNodeActiveProperties(Node2D node, bool active)
    {
        node.Visible = active;
        node.ProcessMode = active ? ProcessModeEnum.Inherit : ProcessModeEnum.Disabled;
    }
    
    private int GetSeedValue(string seedString)
    {
        if (string.IsNullOrEmpty(seedString))
            return 0;
            
        // Hash the string seed to a numeric value
        return seedString.GetStableHashCode();
    }
    
    public override void _EnterTree()
    {
#if IMGUI
        var io = ImGui.GetIO();
        io.ConfigFlags |= ImGuiConfigFlags.ViewportsEnable;
        io.ConfigFlags |= ImGuiConfigFlags.DockingEnable;
#endif
    }

    public override void _Process(double delta)
    {
        CreateDebugWindow();
    }
    
    /// <summary>
    /// Reloads the game state from disk.
    /// </summary>
    /// <returns>True if successful, false otherwise.</returns>
    private bool ReloadGameState()
    {
        // Get a reference to SaveLoadManager to reload the game state
        var saveLoadManager = SaveLoadManager.Instance;
        var loadedState = saveLoadManager.LoadGame();
        
        if (loadedState != null)
        {
            // Update GameManager with the loaded state
            _gameStateManager.ApplyGameState(loadedState);
            return true;
        }
        else
        {
            Logger.Error($"Unable to reload game state from disk");
        }
        
        return false;
    }
}
