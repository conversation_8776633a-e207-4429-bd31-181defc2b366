using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Helpers;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Statistics about a universe
/// </summary>
public class UniverseStats
{
    public string UniverseName { get; set; } = string.Empty;
    public int UniverseSize { get; set; }
    public int CellCount { get; set; }
    public int CelestialBodyCount { get; set; }
    public DateTime CreationDate { get; set; }
    public Dictionary<CelestialBodyType, int> BodyTypeDistribution { get; set; } = new();
    
    /// <summary>
    /// Returns a formatted string of universe statistics for logging purposes
    /// </summary>
    public void LogStats()
    {
        Logger.Info($"Universe generation complete. Stats: {CelestialBodyCount} celestial bodies in {CellCount} cells");
        
        // Log the distribution of celestial body types
        var bodyTypes = Enum.GetValues(typeof(CelestialBodyType))
            .Cast<CelestialBodyType>()
            .Where(t => t != CelestialBodyType.Max)
            .ToList();
            
        foreach (var bodyType in bodyTypes)
        {
            int count = BodyTypeDistribution.GetValueOrDefault(bodyType, 0);
            Logger.Info($"- {bodyType}s: {count}");
        }
    }
}