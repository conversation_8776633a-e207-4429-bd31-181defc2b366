namespace SpaceGame.Universe.Model;

/// <summary>
/// Extension methods for CelestialBodyType
/// </summary>
public static class CelestialBodyTypeExtensions
{
    /// <summary>
    /// Gets the standard prefix code for a celestial body type.
    /// </summary>
    public static string GetPrefix(this CelestialBodyType type)
    {
        return type switch
        {
            CelestialBodyType.TerranPlanet => "TER",    // Terran/Earth-like
            CelestialBodyType.DesertPlanet => "DES",    // Desert
            CelestialBodyType.ForestPlanet => "FOR",    // Forest
            CelestialBodyType.OceanPlanet => "OCN",     // Ocean
            CelestialBodyType.TundraPlanet => "TUN",    // Tundra
            CelestialBodyType.IcePlanet => "ICE",       // Ice
            CelestialBodyType.LavaPlanet => "LVA",      // Lava
            CelestialBodyType.GasGiant => "GAS",        // Gas Giant
            CelestialBodyType.RockyPlanet => "ROC",     // Rocky
            CelestialBodyType.TechPlanet => "TEC",      // Tech
            CelestialBodyType.Asteroid => "AST",        // Asteroid
            CelestialBodyType.Star => "STAR",
            CelestialBodyType.AsteroidBelt => "ABT",
            CelestialBodyType.Nebula => "NEB",
            CelestialBodyType.Quasar => "QSR",
            CelestialBodyType.Supernova => "SNV",
            CelestialBodyType.BlackHole => "BLH",
            CelestialBodyType.Comet => "CMT",
            CelestialBodyType.EmptySpace => "EMP",
            _ => "UNK"
        };
    }
}