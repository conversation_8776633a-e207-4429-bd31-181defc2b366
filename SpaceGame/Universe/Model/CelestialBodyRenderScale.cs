using System.Collections.Generic;
using <PERSON><PERSON>;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Provides render scale configuration for different celestial body types
/// </summary>
public static class CelestialBodyRenderScale
{
    /// <summary>
    /// Default render scales for each celestial body type
    /// </summary>
    private static readonly Dictionary<CelestialBodyType, float> _renderScales = new()
    {
        // Stars are typically the largest bodies
        { CelestialBodyType.Star, 2.0f },
        
        // Planets vary by type
        { CelestialBodyType.TerranPlanet, 1.0f },
        { CelestialBodyType.DesertPlanet, 0.95f },
        { CelestialBodyType.ForestPlanet, 1.0f },
        { CelestialBodyType.GasGiant, 1.6f },
        { CelestialBodyType.OceanPlanet, 1.1f },
        { CelestialBodyType.TundraPlanet, 0.9f },
        { CelestialBodyType.IcePlanet, 0.85f },
        { CelestialBodyType.LavaPlanet, 0.9f },
        { CelestialBodyType.RockyPlanet, 0.8f },
        { CelestialBodyType.TechPlanet, 1.05f },
        
        // Smaller cosmic objects
        { CelestialBodyType.Asteroid, 0.5f },
        { CelestialBodyType.AsteroidBelt, 1.4f }, // Belt is larger as a collective structure
        { CelestialBodyType.BlackHole, 1.15f },
        { CelestialBodyType.Nebula, 2.0f },       // Nebulae remain large but capped
        { CelestialBodyType.Quasar, 1.4f },
        { CelestialBodyType.Supernova, 1.8f },
        { CelestialBodyType.Comet, 0.6f }
    };

    /// <summary>
    /// Get the render scale for a specific celestial body type
    /// </summary>
    /// <param name="bodyType">The type of celestial body</param>
    /// <returns>The render scale factor for the specified body type</returns>
    public static float GetRenderScale(CelestialBodyType bodyType)
    {
        return _renderScales.GetValueOrDefault(bodyType, 1.0f);
    }
    
    /// <summary>
    /// Get the render scale for a celestial body
    /// </summary>
    /// <param name="body">The celestial body data</param>
    /// <returns>The render scale factor for the body</returns>
    public static float GetRenderScale(CelestialBodyData body)
    {
        return GetRenderScale(body.BodyType);
    }
} 