// using System.Collections.Generic;
// Use Godot Collections

using System;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Debugging;
using SpaceGame.Helpers;
using SpaceGame.State;
using System.Text.Json.Serialization;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Represents the entire universe model and acts as the root for all universe data structures.
/// </summary>
public class UniverseModel: ICelestialBodyResolver
{
    // Unique identifier for this universe (No Export, generated on creation)
    [JsonInclude]
    public string Id { get; private set; }
    
    public string Name { get; set; } = "New Universe";
    
    public string Seed { get; set; } = "";
    
    public int Size { get; set; } = 100; // Default size
    
    // Collection of all cells, keyed by HexCoordinates directly
    [JsonInclude]
    private Dictionary<HexCoordinates, HexCell> _cells { get; set; } = new();
    
    // Collection of all celestial bodies, keyed by HexCoordinates directly
    private Dictionary<HexCoordinates, CelestialBodyData> _celestialBodies { get; set; } = new();
    
    // Timestamp for universe creation (No Export, set on creation)
    [JsonInclude]
    public DateTime CreationDate { get; private set; }
    
    private UniverseStats? _cachedStats;
    
    // Constructor
    public UniverseModel()
    {
        Id = Guid.NewGuid().ToString();
        CreationDate = DateTime.Now;
    }
    
    // Constructor with parameters
    public UniverseModel(string name, string seed, int size = 100) : this()
    {
        Name = name;
        Seed = seed;
        Size = size;
    }
    
    /// <summary>
    /// Creates a new cell at the specified coordinates if it doesn't exist
    /// </summary>
    public HexCell GetOrCreateCell(HexCoordinates coords)
    {
        if (!_cells.TryGetValue(coords, out var cell))
        {
            cell = new HexCell(coords);
            _cells[coords] = cell;
        }
        return cell;
    }
    
    /// <summary>
    /// Gets a cell at the specified coordinates, or null if it doesn't exist
    /// </summary>
    public HexCell? GetCell(HexCoordinates coords)
    {
        return _cells.TryGetValue(coords, out var cell) ? cell : null;
    }
    
    /// <summary>
    /// Gets a cell at the specified coordinates, or null if it doesn't exist
    /// </summary>
    public HexCell? GetCell(int q, int r)
    {
        return GetCell(new HexCoordinates(q, r));
    }
    
    /// <summary>
    /// Gets all cells in the universe
    /// </summary>
    public IEnumerable<HexCell> GetAllCells()
    {
        return _cells.Values;
    }

    /// <summary>
    /// Gets all celestial bodies in the universe
    /// </summary>
    public IEnumerable<CelestialBodyData> GetAllCelestialBodies()
    {
        return _celestialBodies.Values;
    }

    /// <summary>
    /// Adds a celestial body to the universe at the specified coordinates
    /// </summary>
    public bool AddCelestialBody(HexCoordinates coords, CelestialBodyData body)
    {
        if (_celestialBodies.TryGetValue(coords, out var existingBody) && existingBody.BodyType != CelestialBodyType.EmptySpace)
        {
            Logger.Error($"Cannot add {body.Name} at {coords} - position already occupied by {existingBody.Name} ({existingBody.BodyType})");
            return false;
        }
        
        // Update the cell
        var cell = GetOrCreateCell(coords);
        cell.ContainedBody = body;
        
        // Update the body position
        body.Position = coords;
        
        // Add to the celestial bodies dictionary
        _celestialBodies[coords] = body;
        return true;
    }
    
    /// <summary>
    /// Removes a celestial body from the universe at the specified coordinates by replacing it with EmptySpace
    /// </summary>
    public bool RemoveCelestialBody(HexCoordinates coords)
    {
        if (!_celestialBodies.TryGetValue(coords, out var existingBody) || existingBody.BodyType == CelestialBodyType.EmptySpace)
        {
            // Nothing to remove or already empty space
            return false;
        }
        
        // Replace with EmptySpace instead of removing entirely
        var emptyBody = new CelestialBodyData(
            name: "Empty Space",
            size: CelestialBodySize.Medium,
            temperature: CelestialBodyTemperature.Cold,
            position: coords,
            bodyType: CelestialBodyType.EmptySpace,
            atmosphere: Atmosphere.None,
            habitability: HabitabilityLevel.Uninhabitable,
            surfaceGravity: SurfaceGravityLevel.Normal,
            factionType: FactionType.None
        );

        // Update the cell
        var cell = GetOrCreateCell(coords); // Cell should exist
        cell.ContainedBody = emptyBody;
        
        // Update the dictionary
        _celestialBodies[coords] = emptyBody;

        return true; 
    }
    
    /// <summary>
    /// Checks if a position is occupied by a *meaningful* celestial body (i.e., not EmptySpace).
    /// </summary>
    public bool IsOccupied(HexCoordinates coords)
    {
        // Check if a body exists at the coords and if it's not EmptySpace
        return _celestialBodies.TryGetValue(coords, out var body) && body.BodyType != CelestialBodyType.EmptySpace;
    }
    
    /// <summary>
    /// Gets the celestial body at the specified coordinates, or null if there isn't one
    /// </summary>
    public CelestialBodyData? GetCelestialBody(HexCoordinates coords)
    {
        return _celestialBodies.TryGetValue(coords, out var body) ? body : null;
    }
    
    /// <summary>
    /// Moves a celestial body from one position to another
    /// </summary>
    public bool MoveCelestialBody(HexCoordinates from, HexCoordinates to)
    {
        // Check if the source has a body and destination is empty
        if (!_celestialBodies.TryGetValue(from, out var body) || IsOccupied(to))
        {
            if (IsOccupied(to)) Logger.Warning($"Move failed: Destination {to} is occupied.");
            else Logger.Warning($"Move failed: Source {from} has no body.");
            return false;
        }

        // Get the body before removing it from the old spot
        var bodyToMove = _celestialBodies[from];

        // Remove from original position (replace with empty space)
        if (!RemoveCelestialBody(from))
        {
            Logger.Error($"Failed to remove body from {from} during move.");
            return false; // Should not happen if TryGetValue succeeded
        }

        // Add to new position
        if (!AddCelestialBody(to, bodyToMove))
        {
            Logger.Error($"Failed to add body to {to} during move.");
            // Attempt to restore the original position?
            AddCelestialBody(from, bodyToMove); // Try to put it back
            return false;
        }
        Logger.Debug($"Moved {bodyToMove.Name} from {from} to {to}");
        return true;
    }

    public CelestialBodyData? GetCelestialBodyDataById(CelestialBodyId id)
    {
        // Legacy lookup path for other ID types
        return _celestialBodies.Values.FirstOrDefault(b => b.Id == id);
    }
    
    /// <summary>
    /// Clears all data in the universe model
    /// </summary>
    public void Clear()
    {
        _cells.Clear();
        _celestialBodies.Clear();
        _cachedStats = null;
    }
    
    /// <summary>
    /// Gets statistics about the universe
    /// </summary>
    public UniverseStats GetStats()
    {
        if (_cachedStats == null)
        {
            _cachedStats = new UniverseStats
            {
                UniverseName = Name,
                UniverseSize = Size,
                CellCount = _cells.Count,
                CelestialBodyCount =
                    _celestialBodies.Values.Count(b =>
                        b.BodyType != CelestialBodyType.EmptySpace), // Count only non-empty bodies
                CreationDate = CreationDate,
                BodyTypeDistribution = _celestialBodies.Values
                    .Where(b => b.BodyType != CelestialBodyType.EmptySpace) // Exclude empty space from distribution
                    .GroupBy(b => b.BodyType)
                    .ToDictionary(g => g.Key, g => g.Count()),
            };
        }

        return _cachedStats;
    }

    public void RebuildCelestialBodiesFromCells()
    {
        _celestialBodies = _cells.ToDictionary(cell => cell.Key, cell => cell.Value.ContainedBody);
    }
}