using System;
using Godot;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Pure data class representing a hexagonal cell in the universe grid.
/// </summary>
public class HexCell
{
    // Position in the hex grid
    public HexCoordinates Coordinates { get; set; }
    
    // Reference to the celestial body in this hex (if any)
    public CelestialBodyData? ContainedBody { get; set; }
    
    // Indicates if this cell is currently visible (not under fog of war)
    public bool IsRevealed { get; set; } = false;
    
    public bool IsOccupied => ContainedBody != null && ContainedBody.BodyType != CelestialBodyType.EmptySpace;
    
    // Parameterless constructor required for Godot Resources
    public HexCell()
    {
        Coordinates = HexCoordinates.Zero; // Default coordinates
    }
    
    public HexCell(HexCoordinates coordinates)
    {
        Coordinates = coordinates;
    }
    
    public HexCell(int q, int r)
    {
        Coordinates = new HexCoordinates(q, r);
    }
    
    public override string ToString()
    {
        return $"HexCell({Coordinates})";
    }
    
    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((HexCell)obj);
    }
    
    protected bool Equals(HexCell other)
    {
        return Coordinates.Equals(other.Coordinates);
    }
    
    public override int GetHashCode()
    {
        return Coordinates.GetHashCode();
    }
} 