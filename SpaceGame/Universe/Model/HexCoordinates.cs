using System;
using Godot;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Pure data class representing coordinates in a hexagonal grid.
/// Uses axial coordinates (q, r) for representing the position.
/// </summary>
public class HexCoordinates
{
    private static readonly HexCoordinates _zero = new(0, 0);
    public static HexCoordinates Zero => _zero;
    
    // Axial coordinates
    public int Q { get; }
    public int R { get; }
    
    public HexCoordinates(int q, int r)
    {
        Q = q;
        R = r;
    }

    public HexCoordinates() {}

    /// <summary>
    /// Calculate the distance between this hex position and another in the axial coordinate system
    /// </summary>
    /// <param name="other">The other hex position</param>
    /// <returns>The distance in hex units</returns>
    public int DistanceFrom(HexCoordinates other)
    {
        // In axial coordinates, we need to convert to cube coordinates to calculate distance
        // Cube coordinates: x = q, z = r, y = -q-r
        
        int x1 = Q;
        int y1 = -Q - R;
        int z1 = R;
        
        int x2 = other.Q;
        int y2 = -other.Q - other.R;
        int z2 = other.R;
        
        // The distance is the maximum of the absolute differences of the cube coordinates
        return Math.Max(Math.Max(Math.Abs(x1 - x2), Math.Abs(y1 - y2)), Math.Abs(z1 - z2));
    }
    
    /// <summary>
    /// Computes the cube coordinate derived from this axial coordinate
    /// </summary>
    /// <returns>Tuple containing x, y, z cube coordinates</returns>
    public (int x, int y, int z) ToCube()
    {
        int x = Q;
        int z = R;
        int y = -x - z;
        return (x, y, z);
    }
    
    /// <summary>
    /// Creates a HexCoordinates from cube coordinates
    /// </summary>
    public static HexCoordinates FromCube(int x, int y, int z)
    {
        // Sanity check for valid cube coordinates
        if (x + y + z != 0)
        {
            throw new ArgumentException("Cube coordinates must sum to zero");
        }
        
        return new HexCoordinates(x, z);
    }
    
    public override string ToString() => $"{Q},{R}";

    protected bool Equals(HexCoordinates other)
    {
        return Q == other.Q && R == other.R;
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((HexCoordinates)obj);
    }

    /// <summary>
    /// Generates a hash code for this coordinate that can be used as a dictionary key.
    /// Uses a fast algorithm optimized for 2D grid coordinates.
    /// </summary>
    public override int GetHashCode()
    {
        // This is an optimized implementation using the Cantor pairing function
        // It creates a unique hash code for each coordinate pair with minimal computation
        // (Q+R)*(Q+R+1)/2 + R
        int sum = Q + R;
        return (sum * (sum + 1) / 2) + R;
    }
    
    /// <summary>
    /// Returns the string key representation for this coordinate
    /// </summary>
    public string ToStringKey()
    {
        return $"{Q},{R}";
    }
    
    /// <summary>
    /// Parses a string key representation into HexCoordinates
    /// </summary>
    public static HexCoordinates FromStringKey(string key)
    {
        var parts = key.Split(',');
        if (parts.Length != 2)
            throw new ArgumentException($"Invalid HexCoordinates key format: {key}");
            
        if (!int.TryParse(parts[0], out int q) || !int.TryParse(parts[1], out int r))
            throw new ArgumentException($"Invalid HexCoordinates values: {key}");
            
        return new HexCoordinates(q, r);
    }
    
    /// <summary>
    /// Tries to parse a string key representation into HexCoordinates
    /// </summary>
    /// <param name="key">The string key to parse</param>
    /// <param name="coordinates">The resulting coordinates if successful</param>
    /// <returns>True if parsing was successful, false otherwise</returns>
    public static bool TryFromStringKey(string key, out HexCoordinates coordinates)
    {
        coordinates = Zero;
        
        if (string.IsNullOrEmpty(key))
            return false;
            
        var parts = key.Split(',');
        if (parts.Length != 2)
            return false;
            
        if (!int.TryParse(parts[0], out int q) || !int.TryParse(parts[1], out int r))
            return false;
            
        coordinates = new HexCoordinates(q, r);
        return true;
    }
} 