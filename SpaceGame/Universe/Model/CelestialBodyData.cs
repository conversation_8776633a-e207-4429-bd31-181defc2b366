using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using SpaceGame.Scripts;
using SpaceGame.State;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Serializable data class representing a celestial body in the universe.
/// This is a plain C# class (not a Resource) that can be serialized to/from JSON.
/// </summary>
public class CelestialBodyData
{
    /// <summary>
    /// Gets or sets the unique identifier for this data instance.
    /// </summary>
    [JsonInclude]
    public CelestialBodyId Id { get; private set; }

    public string Name { get; set; } = string.Empty;
    public CelestialBodySize Size { get; set; }
    public CelestialBodyTemperature Temperature { get; set; }
    public HexCoordinates Position { get; set; }
    
    public CelestialBodyType BodyType { get; set; }
    public Atmosphere Atmosphere { get; set; }
    public HabitabilityLevel Habitability { get; set; }
    public SurfaceGravityLevel SurfaceGravity { get; set; }
    
    [JsonInclude]
    public FactionId? FactionId { get; private set; }
    [JsonInclude]
    public FactionType FactionType { get; private set; } = FactionType.None;
    
    public List<CelestialFeature> Features { get; set; } = new List<CelestialFeature>();

    /// <summary>
    /// Default constructor required for serialization.
    /// </summary>
    public CelestialBodyData()
    {   
        Id = "TEMP-" + Guid.NewGuid().ToString().Substring(0, 8);
    }

    /// <summary>
    /// Adds a feature to this celestial body
    /// </summary>
    public void AddFeature(CelestialFeature feature)
    {
        Features.Add(feature);
    }

    /// <summary>
    /// Constructor for backward compatibility.
    /// </summary>
    public CelestialBodyData(string name, CelestialBodySize size,
        CelestialBodyTemperature temperature,
        HexCoordinates position,
        CelestialBodyType bodyType,
        Atmosphere atmosphere = Atmosphere.None,
        HabitabilityLevel habitability = HabitabilityLevel.Uninhabitable,
        SurfaceGravityLevel surfaceGravity = SurfaceGravityLevel.Normal,
        FactionType factionType = FactionType.None
    ): this() {
        Name = name;
        Size = size;
        Temperature = temperature;
        Position = position;
        BodyType = bodyType;
        Atmosphere = atmosphere;
        Habitability = habitability;
        SurfaceGravity = surfaceGravity;
        FactionType = factionType;
        Features = new List<CelestialFeature>();
        
        // Generate a deterministic ID based on position and body properties
        GenerateDeterministicId();
    }

    /// <summary>
    /// Removes a feature from this celestial body
    /// </summary>
    public bool RemoveFeature(CelestialFeature feature)
    {
        return Features.Remove(feature);
    }
    
    /// <summary>
    /// Checks if this celestial body has a feature of the specified type
    /// </summary>
    public bool HasFeatureOfType(CelestialFeatureType featureType)
    {
        foreach (var feature in Features)
        {
            if (feature.Type == featureType)
                return true;
        }
        return false;
    }
    
    /// <summary>
    /// Gets all features of the specified type
    /// </summary>
    public List<CelestialFeature> GetFeaturesOfType(CelestialFeatureType featureType)
    {
        var features = new List<CelestialFeature>();
        foreach (var celestialFeature in Features)
        {
            if (celestialFeature.Type == featureType)
            {
                features.Add(celestialFeature);
            }
        }

        return features;
    }
    
    /// <summary>
    /// Creates a copy of this celestial body data
    /// </summary>
    public CelestialBodyData Clone()
    {
        var clone = new CelestialBodyData
        {
            Size = Size,
            Temperature = Temperature,
            Position = Position,
            BodyType = BodyType,
            Atmosphere = Atmosphere,
            Habitability = Habitability,
            SurfaceGravity = SurfaceGravity,
            FactionType = FactionType,
            Features = new List<CelestialFeature>()
        };
        
        // Copy the ID from this object to the clone
        clone.Id = Id;
        
        // Deep copy the features
        foreach (var feature in Features)
        {
            clone.Features.Add(feature.Clone());
        }
        
        return clone;
    }

    public void SetFaction(FactionState? faction)
    {
        FactionId = faction?.Id;
        FactionType = faction?.Type ?? FactionType.None;
    }

    public void SetFaction(Faction? faction)
    {
        FactionId = faction?.Id;
        FactionType = faction?.Type ?? FactionType.None;
    }
    
    /// <summary>
    /// Returns a formatted information string about this celestial body
    /// </summary>
    public string GetInfoText()
    {
        var info = $"ID: {Id}\n" +
               $"Name: {Name}\n" +
               $"Position: {Position}\n" +
               $"Type: {BodyType}\n" +
               $"Size: {Size}\n" +
               $"Temperature: {Temperature}\n" +
               $"Atmosphere: {Atmosphere}\n" +
               $"Habitability: {Habitability}\n" +
               $"Surface Gravity: {SurfaceGravity}\n" +
               $"Faction: {FactionType}";
               
        if (Features.Count > 0)
        {
            info += "\nFeatures:";
            foreach (var feature in Features)
            {
                info += $"\n  - {feature}";
            }
        }
        
        return info;
    }
    
    public override string ToString()
    {
        return $"{Name} ({BodyType})";
    }
    
    /// <summary>
    /// Generates a deterministic ID based on position and body properties
    /// </summary>
    private void GenerateDeterministicId()
    {
        string positionPart = $"Q{Position.Q,0:P000;N000}-R{Position.R,0:P000;N000}";
        string bodyType = BodyType.GetPrefix();
        
        // Create a simple, human-readable ID. SEC for "Sector"
        Id = $"SEC-{positionPart}-{bodyType}";
    }
}