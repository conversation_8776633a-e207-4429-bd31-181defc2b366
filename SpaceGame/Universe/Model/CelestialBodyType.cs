using Godot;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Enumeration of celestial body types
/// </summary>
public enum CelestialBodyType
{
    // Represents empty space where no celestial body exists
    EmptySpace, 
    // Basic celestial body types
    Star,
    TerranPlanet,
    DesertPlanet,
    ForestPlanet,
    GasGiant,
    OceanPlanet,
    TundraPlanet,
    IcePlanet,
    LavaPlanet,
    RockyPlanet,
    TechPlanet,
    
    // Additional cosmic objects
    Asteroid,
    AsteroidBelt,
    BlackHole,
    Nebula,
    Quasar,
    Supernova,
    Comet,
    
    // Max entry for easy enumeration size
    Max
}