using Godot;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Represents a feature that can be attached to a celestial body, such as moons, rings, stations, or dyson spheres
/// </summary>
public class CelestialFeature
{
    public string Name { get; set; } = string.Empty;
    public CelestialFeatureType Type { get; set; }
    public float Scale { get; set; } = 1.0f;
    public Vector2 Offset { get; set; } = Vector2.Zero;
    public float OrbitSpeed { get; set; } = 0.0f;
    public float OrbitRadius { get; set; } = 0.0f;
    public float RotationSpeed { get; set; } = 0.0f;
    public string TexturePath { get; set; } = string.Empty;
    
    public CelestialFeature() { }
    
    public CelestialFeature(string name, CelestialFeatureType type, string texturePath)
    {
        Name = name;
        Type = type;
        TexturePath = texturePath;
    }
    
    /// <summary>
    /// Creates a copy of this feature
    /// </summary>
    public CelestialFeature Clone()
    {
        return new CelestialFeature
        {
            Name = Name,
            Type = Type,
            Scale = Scale,
            Offset = Offset,
            OrbitSpeed = OrbitSpeed,
            OrbitRadius = OrbitRadius,
            RotationSpeed = RotationSpeed,
            TexturePath = TexturePath
        };
    }
    
    public override string ToString()
    {
        return $"{Name} ({Type})";
    }
    
    /// <summary>
    /// Constrains the orbit radius to ensure the feature stays within the parent's hex.
    /// </summary>
    /// <param name="maxRadius">The maximum allowed orbit radius</param>
    public void ConstrainOrbitRadius(float maxRadius)
    {
        if (OrbitRadius > maxRadius)
        {
            OrbitRadius = maxRadius;
        }
    }
} 