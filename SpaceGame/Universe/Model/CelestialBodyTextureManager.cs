using System.Collections.Generic;
using Godot;
using SpaceGame.Helpers;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Manages texture paths for different celestial body types and features.
/// Uses lazy initialization of textures - only loads paths when requested.
/// </summary>
public static class CelestialBodyTextureManager
{
    // Mapping of celestial body types to their directory names
    private static readonly Dictionary<CelestialBodyType, string> _bodyTypeFolders = new()
    {
        { CelestialBodyType.Star, "Sun" },
        { CelestialBodyType.TerranPlanet, "Terran" },
        { CelestialBodyType.DesertPlanet, "Desert" },
        { CelestialBodyType.ForestPlanet, "Forest" },
        { CelestialBodyType.GasGiant, "GasGiant" },
        { CelestialBodyType.OceanPlanet, "Ocean" },
        { CelestialBodyType.TundraPlanet, "Tundra" },
        { CelestialBodyType.IcePlanet, "Ice" },
        { CelestialBodyType.LavaPlanet, "Lava" },
        { CelestialBodyType.RockyPlanet, "Rocky" },
        { CelestialBodyType.TechPlanet, "Tech" },
        { CelestialBodyType.Asteroid, "Asteroid" },
        { CelestialBodyType.AsteroidBelt, "AsteroidBelt" },
        { CelestialBodyType.BlackHole, "BlackHole" },
        { CelestialBodyType.Nebula, "Nebula" },
        { CelestialBodyType.Quasar, "Quasar" },
        { CelestialBodyType.Supernova, "Supernova" },
        { CelestialBodyType.Comet, "Comet" }
    };

    // Cache for available texture paths for each celestial body type
    private static readonly Dictionary<CelestialBodyType, List<string>> _bodyTextureCache = new();
    
    // Cache for available texture paths for each feature type
    private static readonly Dictionary<CelestialFeatureType, List<string>> _featureTextureCache = new();
    
    // Mapping of feature types to their directory names
    private static readonly Dictionary<CelestialFeatureType, string> _featureTypeFolders = new()
    {
        { CelestialFeatureType.Moon, "Moon" },
        { CelestialFeatureType.Rings, "Rings" },
        { CelestialFeatureType.Station, "Station" },
        { CelestialFeatureType.DysonSphere, "DysonSphere" }
    };
    
    /// <summary>
    /// Gets a texture path for a celestial body based on its type and name
    /// </summary>
    /// <param name="bodyType">The type of celestial body</param>
    /// <param name="name">The name of the celestial body, used for hashing</param>
    /// <returns>The path to an appropriate texture</returns>
    public static string GetTexturePathForBody(CelestialBodyType bodyType, string name)
    {
        // First ensure we have the textures for this body type
        if (!_bodyTextureCache.ContainsKey(bodyType))
        {
            LoadTexturesForBodyType(bodyType);
        }
        
        // If we still don't have textures, return an empty string
        if (!_bodyTextureCache.ContainsKey(bodyType) || _bodyTextureCache[bodyType].Count == 0)
        {
            Logger.Warning($"No textures found for {bodyType}");
            return string.Empty;
        }
        
        // Use the name's hash to select a texture consistently
        int nameHash = name.GetStableHashCode();
        int index = Mathf.Abs(nameHash) % _bodyTextureCache[bodyType].Count;
        
        return _bodyTextureCache[bodyType][index];
    }
    
    /// <summary>
    /// Gets a texture path for a celestial body
    /// </summary>
    /// <param name="bodyData">The celestial body data</param>
    /// <returns>The path to an appropriate texture</returns>
    public static string GetTexturePathForBody(CelestialBodyData bodyData)
    {
        return GetTexturePathForBody(bodyData.BodyType, bodyData.Name);
    }
    
    /// <summary>
    /// Gets a texture path for a celestial feature
    /// </summary>
    /// <param name="featureType">The type of celestial feature</param>
    /// <param name="name">The name of the feature, used for hashing</param>
    /// <returns>The path to an appropriate texture</returns>
    public static string GetTexturePathForFeature(CelestialFeatureType featureType, string name)
    {
        // First ensure we have the textures for this feature type
        if (!_featureTextureCache.ContainsKey(featureType))
        {
            LoadTexturesForFeatureType(featureType);
        }
        
        // If we still don't have textures, return an empty string
        if (!_featureTextureCache.ContainsKey(featureType) || _featureTextureCache[featureType].Count == 0)
        {
            Logger.Warning($"No textures found for feature {featureType}");
            return string.Empty;
        }
        
        // Use the name's hash to select a texture consistently
        int nameHash = name.GetStableHashCode();
        int index = Mathf.Abs(nameHash) % _featureTextureCache[featureType].Count;
        
        return _featureTextureCache[featureType][index];
    }
    
    /// <summary>
    /// Loads all available textures for a celestial body type
    /// </summary>
    private static void LoadTexturesForBodyType(CelestialBodyType bodyType)
    {
        Logger.Debug($"Loading texture for body type {bodyType}");
        // Skip if already loaded
        if (_bodyTextureCache.ContainsKey(bodyType)) return;
        
        var texturePaths = new List<string>();
        
        // First determine the directory path
        string dirPath;
        if (bodyType == CelestialBodyType.Star)
        {
            dirPath = "res://SpaceGame/Assets/CosmicObjects/Sun";
        }
        else if (bodyType is CelestialBodyType.AsteroidBelt or 
                 CelestialBodyType.BlackHole or 
                 CelestialBodyType.Nebula or 
                 CelestialBodyType.Quasar or 
                 CelestialBodyType.Supernova or 
                 CelestialBodyType.Comet)
        {
            // Get the folder name from the mapping, defaulting to the enum name
            string folderName = _bodyTypeFolders.GetValueOrDefault(bodyType, bodyType.ToString());
            dirPath = $"res://SpaceGame/Assets/CosmicObjects/{folderName}";
        }
        else
        {
            // This is a planet type
            string folderName = _bodyTypeFolders.GetValueOrDefault(bodyType, bodyType.ToString());
            dirPath = $"res://SpaceGame/Assets/Planets/{folderName}";
        }
        
        // Try to open the directory
        using var dir = DirAccess.Open(dirPath);
        if (dir != null)
        {
            dir.ListDirBegin();
            string fileName = dir.GetNext();
            
            while (fileName != "")
            {
                // Skip directories and non-PNG files
                if (!dir.CurrentIsDir() && fileName.EndsWith(".png"))
                {
                    texturePaths.Add($"{dirPath}/{fileName}");
                }
                fileName = dir.GetNext();
            }
            
            dir.ListDirEnd();
        }
        else
        {
            Logger.Warning($"Could not open directory for {bodyType}: {dirPath}");
        }
        
        // Add to cache even if empty
        _bodyTextureCache[bodyType] = texturePaths;
    }
    
    /// <summary>
    /// Loads all available textures for a celestial feature type
    /// </summary>
    private static void LoadTexturesForFeatureType(CelestialFeatureType featureType)
    {
        // Skip if already loaded
        if (_featureTextureCache.ContainsKey(featureType)) return;
        
        var texturePaths = new List<string>();
        
        // Determine directory based on feature type
        string folderName = _featureTypeFolders.GetValueOrDefault(featureType, featureType.ToString());
        string dirPath = $"res://SpaceGame/Assets/PlanetAddons/{folderName}";
        
        // Try to open the directory
        using var dir = DirAccess.Open(dirPath);
        if (dir != null)
        {
            dir.ListDirBegin();
            string fileName = dir.GetNext();
            
            while (fileName != "")
            {
                // Skip directories and non-PNG files
                if (!dir.CurrentIsDir() && fileName.EndsWith(".png"))
                {
                    texturePaths.Add($"{dirPath}/{fileName}");
                }
                fileName = dir.GetNext();
            }
            
            dir.ListDirEnd();
        }
        else
        {
            Logger.Warning($"Could not open directory for feature {featureType}: {dirPath}");
        }
        
        // Add to cache even if empty
        _featureTextureCache[featureType] = texturePaths;
    }
} 