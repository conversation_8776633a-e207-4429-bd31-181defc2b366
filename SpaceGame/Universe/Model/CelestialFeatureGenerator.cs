using System.Collections.Generic;
using Godot;
using SpaceGame.Universe.Generators;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Model;

/// <summary>
/// Provides functionality to automatically generate appropriate features for celestial bodies
/// based on their types, ensuring features only appear on compatible celestial bodies.
/// </summary>
public static class CelestialFeatureGenerator
{
    // Dictionary mapping celestial body types to compatible feature types
    private static readonly Dictionary<CelestialBodyType, List<CelestialFeatureType>> _compatibleFeatures = new()
    {
        // Stars can have stations and dyson spheres
        { CelestialBodyType.Star, new List<CelestialFeatureType> { CelestialFeatureType.DysonSphere } },
        
        // Terrestrial planets can have moons, rings, and stations
        { CelestialBodyType.TerranPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.DesertPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.ForestPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.OceanPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.TundraPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.IcePlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.LavaPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.RockyPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        { CelestialBodyType.TechPlanet, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        
        // Gas giants can have moons, rings, and stations
        { CelestialBodyType.GasGiant, new List<CelestialFeatureType> { CelestialFeatureType.Moon, CelestialFeatureType.Rings, CelestialFeatureType.Station } },
        
        // Large asteroids can have small moons or stations
        { CelestialBodyType.Asteroid, new List<CelestialFeatureType>() },
        { CelestialBodyType.AsteroidBelt, new List<CelestialFeatureType>() },
        { CelestialBodyType.BlackHole, new List<CelestialFeatureType>() },
        { CelestialBodyType.Nebula, new List<CelestialFeatureType>() },
        { CelestialBodyType.Quasar, new List<CelestialFeatureType>() },
        { CelestialBodyType.Supernova, new List<CelestialFeatureType>() },
        { CelestialBodyType.Comet, new List<CelestialFeatureType>() },
    };
    
    // Feature generation probability settings
    private const float MOON_PROBABILITY = 0.4f;
    private const float RINGS_PROBABILITY = 0.15f;
    private const float STATION_PROBABILITY = 0.2f;
    private const float DYSON_SPHERE_PROBABILITY = 0.05f;
    
    // Max number of features per type
    private const int MAX_MOONS = 3;
    private const int MAX_RINGS = 1;
    private const int MAX_STATIONS = 2;
    private const int MAX_DYSON_SPHERES = 1;
    
    // Base feature limits
    private const int MIN_FEATURES = 2;
    private const int MAX_FEATURES = 5;
    
    // Track current orbiting features for angle distribution
    private static readonly Dictionary<string, List<OrbitalFeatureInfo>> _orbitalFeaturesTracking = new();
    
    /// <summary>
    /// Structure to track orbital features and their angles
    /// </summary>
    private struct OrbitalFeatureInfo
    {
        public CelestialFeatureType Type;
        public float StartAngle; // In radians
    }
    
    /// <summary>
    /// Determines the maximum number of features a celestial body can have based on its size
    /// </summary>
    /// <param name="celestialBody">The celestial body</param>
    /// <returns>The maximum number of features allowed</returns>
    private static int GetMaxFeaturesForBody(CelestialBodyData celestialBody)
    {
        // Get the render scale which represents the relative size
        float scale = CelestialBodyRenderScale.GetRenderScale(celestialBody);
        
        // Calculate max features based on size:
        // - Small bodies (scale < 0.7): 2 features
        // - Medium bodies (scale 0.7-1.3): 3 features
        // - Large bodies (scale 1.3-1.8): 4 features
        // - Huge bodies (scale > 1.8): 5 features

        return scale switch
        {
            < 0.7f => MIN_FEATURES,
            < 1.3f => 3,
            < 1.8f => 4,
            _ => MAX_FEATURES
        };
    }
    
    /// <summary>
    /// Generates and adds appropriate features to a celestial body based on its type
    /// </summary>
    /// <param name="celestialBody">The celestial body to add features to</param>
    /// <param name="rng">Random number generator to use</param>
    public static void GenerateFeatures(CelestialBodyData celestialBody, RandomNumberGenerator rng)
    {
        if (celestialBody == null)
            return;
            
        // Check if this body type has compatible features
        if (!_compatibleFeatures.TryGetValue(celestialBody.BodyType, out var compatibleFeatureTypes) || 
            compatibleFeatureTypes.Count == 0)
            return;
        
        // Clear any tracking for this body
        _orbitalFeaturesTracking[celestialBody.Name] = new List<OrbitalFeatureInfo>();
        
        // Determine maximum features for this body based on its size
        int maxTotalFeatures = GetMaxFeaturesForBody(celestialBody);
        
        // Create a list of possible feature types to generate in random order
        var featureTypesToGenerate = new List<CelestialFeatureType>(compatibleFeatureTypes);
        
        int totalFeatures = 0;
        
        // Generate features in random order until we reach the maximum
        foreach (var featureType in featureTypesToGenerate)
        {
            // Stop if we've reached the maximum total features
            if (totalFeatures >= maxTotalFeatures)
                break;
                
            switch (featureType)
            {
                case CelestialFeatureType.Moon:
                    int moonCount = GenerateMoons(celestialBody, rng, maxTotalFeatures - totalFeatures);
                    totalFeatures += moonCount;
                    break;
                    
                case CelestialFeatureType.Rings:
                    if (GenerateRings(celestialBody, rng))
                        totalFeatures++;
                    break;
                    
                case CelestialFeatureType.Station:
                    int stationCount = GenerateStations(celestialBody, rng, maxTotalFeatures - totalFeatures);
                    totalFeatures += stationCount;
                    break;
                    
                case CelestialFeatureType.DysonSphere:
                    if (GenerateDysonSphere(celestialBody, rng))
                        totalFeatures++;
                    break;
            }
        }
    }
    
    /// <summary>
    /// Calculates a distributed starting angle for a new orbiting feature
    /// </summary>
    /// <param name="celestialBodyName">The name of the parent celestial body</param>
    /// <param name="featureType">The type of feature to place</param>
    /// <param name="rng">Random number generator</param>
    /// <returns>A starting angle in radians</returns>
    private static float GetDistributedAngle(string celestialBodyName, CelestialFeatureType featureType, RandomNumberGenerator rng)
    {
        // Ensure the tracking list exists
        if (!_orbitalFeaturesTracking.ContainsKey(celestialBodyName))
        {
            _orbitalFeaturesTracking[celestialBodyName] = new List<OrbitalFeatureInfo>();
        }
        
        var existingFeatures = _orbitalFeaturesTracking[celestialBodyName];
        
        if (existingFeatures.Count == 0)
        {
            // First feature gets a random angle
            float angle = rng.RandfRange(0, Mathf.Pi * 2);
            existingFeatures.Add(new OrbitalFeatureInfo { Type = featureType, StartAngle = angle });
            return angle;
        }
        
        // Find the largest gap between existing features
        List<float> angles = new List<float>();
        foreach (var feature in existingFeatures)
        {
            angles.Add(feature.StartAngle);
        }
        
        // Sort angles in ascending order
        angles.Sort();
        
        // Find the largest gap
        float largestGap = 0;
        float gapStartAngle = 0;
        
        for (int i = 0; i < angles.Count; i++)
        {
            float currentAngle = angles[i];
            float nextAngle = angles[(i + 1) % angles.Count];
            
            // Handle wrap-around
            float gap = (i == angles.Count - 1) 
                ? Mathf.Pi * 2 - currentAngle + nextAngle 
                : nextAngle - currentAngle;
                
            if (gap > largestGap)
            {
                largestGap = gap;
                gapStartAngle = currentAngle;
            }
        }
        
        // Place new feature in the middle of the largest gap
        float newAngle = gapStartAngle + largestGap / 2;
        
        // Normalize angle to 0-2π range
        if (newAngle > Mathf.Pi * 2)
        {
            newAngle -= Mathf.Pi * 2;
        }
        
        // Add new feature to tracking
        existingFeatures.Add(new OrbitalFeatureInfo { Type = featureType, StartAngle = newAngle });
        
        return newAngle;
    }
    
    /// <summary>
    /// Gets an appropriate orbit radius based on feature type, body scale, and existing features
    /// </summary>
    /// <param name="celestialBody">The celestial body</param>
    /// <param name="featureType">The type of feature to place</param>
    /// <param name="rng">Random number generator</param>
    /// <returns>A suitable orbit radius that won't overlap with the body or other features</returns>
    private static float GetVaryingOrbitRadius(CelestialBodyData celestialBody, CelestialFeatureType featureType, RandomNumberGenerator rng)
    {
        // Get body scale to ensure features don't overlap with the body itself
        float bodyScale = CelestialBodyRenderScale.GetRenderScale(celestialBody);
        
        // Base minimum distance to avoid overlapping with body
        float minSafeDistance = 40.0f * bodyScale;
        
        // Maximum safe radius to stay within hex boundaries
        float maxSafeDistance = 250.0f;
        
        // Vary distance ranges by feature type
        switch (featureType)
        {
            case CelestialFeatureType.Moon:
                // Moons orbit closer to the body
                return rng.RandfRange(minSafeDistance, minSafeDistance + 90.0f);
                
            case CelestialFeatureType.Station:
                // Stations orbit farther out
                return rng.RandfRange(minSafeDistance + 50.0f, maxSafeDistance);
                
            case CelestialFeatureType.DysonSphere:
                // Dyson spheres do not have a radius
                return 0f;
                
            default:
                // Default distance range
                return rng.RandfRange(minSafeDistance, maxSafeDistance);
        }
    }
   
    private static int GenerateMoons(CelestialBodyData celestialBody, RandomNumberGenerator rng, int maxAllowed = MAX_MOONS)
    {
        int moonCount = 0;
        int maxMoons = Mathf.Min(MAX_MOONS, maxAllowed);
        
        // Generate between 0 and maxMoons moons
        while (moonCount < maxMoons && rng.Randf() < MOON_PROBABILITY)
        {
            string moonName = $"{celestialBody.Name}-Moon-{moonCount + 1}";
            
            // Randomize moon properties
            float scale = rng.RandfRange(0.2f, 0.4f);
            
            // Get a varying orbit radius for this feature
            float orbitRadius = GetVaryingOrbitRadius(celestialBody, CelestialFeatureType.Moon, rng);
            
            float orbitSpeed = rng.RandfRange(0.15f, 0.3f);
            
            // Calculate distributed starting angle
            float startAngle = GetDistributedAngle(celestialBody.Name, CelestialFeatureType.Moon, rng);
            
            // Convert angle to initial position offset
            Vector2 initialPosition = new Vector2(
                Mathf.Cos(startAngle) * orbitRadius, 
                Mathf.Sin(startAngle) * orbitRadius
            );
            
            CelestialFeature moon = CelestialFeatureFactory.CreateMoon(moonName, scale, orbitRadius, orbitSpeed);
            moon.Offset = initialPosition - new Vector2(orbitRadius, 0); // Adjust for initial angle
            celestialBody.AddFeature(moon);
            
            moonCount++;
        }
        return moonCount;
    }
    
    private static bool GenerateRings(CelestialBodyData celestialBody, RandomNumberGenerator rng)
    {
        if (rng.Randf() < RINGS_PROBABILITY && !celestialBody.HasFeatureOfType(CelestialFeatureType.Rings))
        {
            string ringsName = $"{celestialBody.Name}-Rings";
            
            CelestialFeature rings = CelestialFeatureFactory.CreateRings(ringsName, CelestialBodyRenderScale.GetRenderScale(celestialBody));
            celestialBody.AddFeature(rings);
            return true;
        }
        return false;
    }
    
    private static int GenerateStations(CelestialBodyData celestialBody, RandomNumberGenerator rng, int maxAllowed = MAX_STATIONS)
    {
        int stationCount = 0;
        int maxStations = Mathf.Min(MAX_STATIONS, maxAllowed);
        
        // Generate between 0 and maxStations stations
        while (stationCount < maxStations && rng.Randf() < STATION_PROBABILITY)
        {
            string stationName = $"{celestialBody.Name}-Station-{stationCount + 1}";
            
            // Randomize station properties
            float scale = rng.RandfRange(0.4f, 0.8f);
            
            // Get a varying orbit radius for this feature
            float orbitRadius = GetVaryingOrbitRadius(celestialBody, CelestialFeatureType.Station, rng);
            
            float orbitSpeed = rng.RandfRange(0.05f, 0.15f);
            
            // Calculate distributed starting angle
            float startAngle = GetDistributedAngle(celestialBody.Name, CelestialFeatureType.Station, rng);
            
            // Convert angle to initial position offset
            Vector2 initialPosition = new Vector2(
                Mathf.Cos(startAngle) * orbitRadius, 
                Mathf.Sin(startAngle) * orbitRadius
            );
            
            CelestialFeature station = CelestialFeatureFactory.CreateStation(stationName, scale, orbitRadius, orbitSpeed);
            station.Offset = initialPosition - new Vector2(orbitRadius, 0); // Adjust for initial angle
            celestialBody.AddFeature(station);
            
            stationCount++;
        }
        return stationCount;
    }
    
    private static bool GenerateDysonSphere(CelestialBodyData celestialBody, RandomNumberGenerator rng)
    {
        if (rng.Randf() < DYSON_SPHERE_PROBABILITY && !celestialBody.HasFeatureOfType(CelestialFeatureType.DysonSphere))
        {
            string dysonSphereName = $"{celestialBody.Name}-DysonSphere";
            
            // Randomize dyson sphere properties
            float scale = rng.RandfRange(1.6f, 2.0f);
            
            // Get a special orbit radius for dyson spheres
            float orbitRadius = GetVaryingOrbitRadius(celestialBody, CelestialFeatureType.DysonSphere, rng);
            
            CelestialFeature dysonSphere = CelestialFeatureFactory.CreateDysonSphere(dysonSphereName, scale, orbitRadius);
            
            // Also set a random starting angle for rotating dyson spheres
            float startAngle = GetDistributedAngle(celestialBody.Name, CelestialFeatureType.DysonSphere, rng);
            
            // Convert angle to initial position offset
            if (orbitRadius > 0)
            {
                Vector2 initialPosition = new Vector2(
                    Mathf.Cos(startAngle) * orbitRadius, 
                    Mathf.Sin(startAngle) * orbitRadius
                );
                
                dysonSphere.Offset = initialPosition - new Vector2(orbitRadius, 0); // Adjust for initial angle
            }
            
            celestialBody.AddFeature(dysonSphere);
            return true;
        }
        return false;
    }
} 