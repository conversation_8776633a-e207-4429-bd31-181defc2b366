using System;
using System.Collections.Generic;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Universe.Generators;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Generation;

/// <summary>
/// Pure C# class for generating celestial bodies.
/// </summary>
public class CelestialBodyGenerator
{
    // Random number generator
    private RandomNumberGenerator _rng = new();
    public RandomNumberGenerator Rand => _rng;
    
    public CelestialBodyGenerator() {}

    private Atmosphere GetAtmosphereForType(CelestialBodyType type)
    {
        switch (type)
        {
            case CelestialBodyType.EmptySpace:
                return Atmosphere.None;
            case CelestialBodyType.TerranPlanet:
            case CelestialBodyType.ForestPlanet:
            case CelestialBodyType.OceanPlanet:
            case CelestialBodyType.TechPlanet:
                return Atmosphere.Normal;
            case CelestialBodyType.DesertPlanet:
            case CelestialBodyType.TundraPlanet:
                return _rng.Randf() < 0.7f ? Atmosphere.Normal : Atmosphere.Thin;
            case CelestialBodyType.IcePlanet:
            case CelestialBodyType.RockyPlanet:
                return Atmosphere.Thin;
            case CelestialBodyType.LavaPlanet:
                return _rng.Randf() < 0.6f ? Atmosphere.Toxic : Atmosphere.Corrosive;
            case CelestialBodyType.GasGiant:
                return Atmosphere.Toxic;
            default:
                return Atmosphere.None;
        }
    }
    
    private HabitabilityLevel GetHabitabilityForType(CelestialBodyType type)
    {
        switch (type)
        {
            case CelestialBodyType.EmptySpace:
                return HabitabilityLevel.Uninhabitable;
            case CelestialBodyType.TerranPlanet:
            case CelestialBodyType.ForestPlanet:
            case CelestialBodyType.TechPlanet:
                return HabitabilityLevel.Optimal;
            case CelestialBodyType.OceanPlanet:
            case CelestialBodyType.DesertPlanet:
            case CelestialBodyType.TundraPlanet:
                return HabitabilityLevel.Habitable;
            default:
                return HabitabilityLevel.Uninhabitable;
        }
    }
    
    private SurfaceGravityLevel GetSurfaceGravityForType(CelestialBodyType type)
    {
        switch (type)
        {
            case CelestialBodyType.EmptySpace:
                return SurfaceGravityLevel.Normal;
            case CelestialBodyType.TerranPlanet:
            case CelestialBodyType.ForestPlanet:
            case CelestialBodyType.OceanPlanet:
            case CelestialBodyType.DesertPlanet:
            case CelestialBodyType.TundraPlanet:
            case CelestialBodyType.TechPlanet:
                return SurfaceGravityLevel.Normal;
            case CelestialBodyType.IcePlanet:
            case CelestialBodyType.RockyPlanet:
                return SurfaceGravityLevel.Low;
            case CelestialBodyType.LavaPlanet:
                return SurfaceGravityLevel.High;
            case CelestialBodyType.GasGiant:
            case CelestialBodyType.Star:
                return SurfaceGravityLevel.VeryHigh;
            default:
                return SurfaceGravityLevel.Normal;
        }
    }
    
    private CelestialBodySize GetSizeForType(CelestialBodyType type)
    {
        switch (type)
        {
            case CelestialBodyType.EmptySpace:
                return CelestialBodySize.Medium;
            case CelestialBodyType.GasGiant:
                return CelestialBodySize.Huge;
            case CelestialBodyType.TerranPlanet:
            case CelestialBodyType.ForestPlanet:
            case CelestialBodyType.OceanPlanet:
                return CelestialBodySize.Medium;
            case CelestialBodyType.DesertPlanet:
            case CelestialBodyType.TundraPlanet:
            case CelestialBodyType.IcePlanet:
            case CelestialBodyType.LavaPlanet:
                return CelestialBodySize.Small;
            case CelestialBodyType.RockyPlanet:
                return CelestialBodySize.Tiny;
            case CelestialBodyType.TechPlanet:
                return CelestialBodySize.Large;

            // Cosmic objects
            case CelestialBodyType.Nebula:
            case CelestialBodyType.Supernova:
            case CelestialBodyType.Quasar:
            case CelestialBodyType.BlackHole:
            case CelestialBodyType.Star:
                return CelestialBodySize.Huge;
            case CelestialBodyType.AsteroidBelt:
                return CelestialBodySize.Large;
            case CelestialBodyType.Asteroid:
                return CelestialBodySize.Tiny;
            case CelestialBodyType.Comet:
                return CelestialBodySize.Tiny;
            
            default:
                return CelestialBodySize.Medium;
        }
    }
    
    private CelestialBodyTemperature GetTemperatureForType(CelestialBodyType type)
    {
        switch (type)
        {
            case CelestialBodyType.EmptySpace:
                return CelestialBodyTemperature.Cold;
            case CelestialBodyType.LavaPlanet:
            case CelestialBodyType.Star:
            case CelestialBodyType.Quasar:
            case CelestialBodyType.Supernova:
            case CelestialBodyType.BlackHole:
                return CelestialBodyTemperature.Scorching;
            case CelestialBodyType.DesertPlanet:
                return CelestialBodyTemperature.Hot;
            case CelestialBodyType.TerranPlanet:
            case CelestialBodyType.ForestPlanet:
            case CelestialBodyType.OceanPlanet:
            case CelestialBodyType.TechPlanet:
                return CelestialBodyTemperature.Moderate;
            case CelestialBodyType.TundraPlanet:
            case CelestialBodyType.AsteroidBelt:
                return CelestialBodyTemperature.Cold;
            case CelestialBodyType.IcePlanet:
            case CelestialBodyType.RockyPlanet:
            case CelestialBodyType.Asteroid:
            case CelestialBodyType.Comet:
                return CelestialBodyTemperature.Freezing;
            case CelestialBodyType.Nebula:
                return CelestialBodyTemperature.Hot;
            default:
                return CelestialBodyTemperature.Moderate;
        }
    }
    
    /// <summary>
    /// Determines a faction type for a celestial body based on its type and location
    /// </summary>
    private FactionType GetFactionForBody(CelestialBodyType type, HexCoordinates coordinates)
    {
        // For now, assign None by default and let the player set factions manually
        // In the future, this could have logic to assign factions based on position in the universe or body type
        return FactionType.None;
    }
    
    public CelestialBodyData GenerateCelestialBodyOfType(HexCoordinates coordinates, CelestialBodyType type)
    {
        // Handle EmptySpace separately as it doesn't need complex generation
        if (type == CelestialBodyType.EmptySpace)
        {
            return new CelestialBodyData(
                name: "Empty Space",
                size: CelestialBodySize.Medium, // Default, doesn't really apply
                temperature: CelestialBodyTemperature.Cold, // Default
                position: coordinates,
                bodyType: CelestialBodyType.EmptySpace,
                atmosphere: Atmosphere.None,
                habitability: HabitabilityLevel.Uninhabitable,
                surfaceGravity: SurfaceGravityLevel.Normal, // Default
                factionType: FactionType.None
            );
        }

       CelestialBodySize size = GetSizeForType(type);
        CelestialBodyTemperature temperature = GetTemperatureForType(type);
        Atmosphere atmosphere = GetAtmosphereForType(type);
        HabitabilityLevel habitability = GetHabitabilityForType(type);
        SurfaceGravityLevel surfaceGravity = GetSurfaceGravityForType(type);
        FactionType faction = GetFactionForBody(type, coordinates);
        
        return new CelestialBodyData(
            name: GenerateCelestialBodyName(type, size, temperature),
            size: size,
            temperature: temperature,
            position: coordinates,
            bodyType: type,
            atmosphere: atmosphere,
            habitability: habitability,
            surfaceGravity: surfaceGravity,
            factionType: faction
        );
    }
    
    private string GenerateCelestialBodyName(CelestialBodyType type, CelestialBodySize size, CelestialBodyTemperature temperature)
    {
        // Handle EmptySpace directly
        if (type == CelestialBodyType.EmptySpace)
        {
            return "Empty Space";
        }

        // Generate a unique ID number for the planet
        int idNumber = _rng.RandiRange(1000, 9999);
        
        // Get prefix from the extension method
        string prefix = type.GetPrefix();
        
        // Add a descriptor based on key properties
        string descriptor = "";
        
        // Size descriptor
        if (type is CelestialBodyType.Star or CelestialBodyType.Quasar or CelestialBodyType.Supernova)
        {
            descriptor = size == CelestialBodySize.Huge ? "PRIME-" : "MINOR-";
        }
        else if (type == CelestialBodyType.GasGiant)
        {
            descriptor = size == CelestialBodySize.Huge ? "MAJOR-" : "STANDARD-";
        }
        else 
        {
            if (size == CelestialBodySize.Tiny) descriptor = "LIGHT-";
            else if (size == CelestialBodySize.Large || size == CelestialBodySize.Huge) descriptor = "HEAVY-";
        }
        
        // Temperature descriptor for specific types
        if (type == CelestialBodyType.TerranPlanet && temperature == CelestialBodyTemperature.Warm)
        {
            descriptor += "TROPICAL-";
        }
        else if (type == CelestialBodyType.DesertPlanet && temperature == CelestialBodyTemperature.Hot)
        {
            descriptor += "SCORCHED-";
        }
        else if (type is CelestialBodyType.IcePlanet or CelestialBodyType.TundraPlanet && temperature == CelestialBodyTemperature.Freezing)
        {
            descriptor += "FROZEN-";
        }
        
        // Return the formatted name
        return $"{prefix}-{descriptor}{idNumber}";
    }

    public UniverseModel InitializeEmptyUniverse(string name, string seed, int size)
    {
        var universe = new UniverseModel(name, seed, size);
        InitializeRng(seed);
        InitializeHexGrid(universe);
        return universe;
    }

    private void InitializeRng(string seed)
    {
        _rng = new RandomNumberGenerator();
        
        uint numericSeed = GetNumericSeed(seed);
        
        if (numericSeed == 0)
        {
            // Use random seed
            _rng.Randomize();
            Logger.Info($"Universe generation using random seed (numeric: {_rng.Seed})");
        }
        else
        {
            // Use specified seed
            _rng.Seed = numericSeed;
            Logger.Info($"Universe generation using string seed: '{seed}' (numeric: {numericSeed})");
        }

    }
    
    /// <summary>
    /// Converts a string seed to a numeric value that can be used with RandomNumberGenerator
    /// </summary>
    private uint GetNumericSeed(string seed)
    {
        // Empty string means use random seed
        if (string.IsNullOrEmpty(seed))
        {
            return 0;
        }
        
        // Get a numeric value from the string using GetHashCode
        int hashCode = seed.GetStableHashCode();
        
        // Convert to uint (ensuring it's always positive)
        uint numericSeed = (uint)(hashCode & 0x7FFFFFFF);
        
        return numericSeed;
    }
    
    public CelestialBodyData GeneratePlanet(HexCoordinates coordinates)
    {
        // Random planet type excluding Star, AsteroidBelt, etc.
        var validPlanetTypes = new[] {
            CelestialBodyType.TerranPlanet,
            CelestialBodyType.DesertPlanet,
            CelestialBodyType.ForestPlanet,
            CelestialBodyType.GasGiant,
            CelestialBodyType.OceanPlanet,
            CelestialBodyType.TundraPlanet,
            CelestialBodyType.IcePlanet,
            CelestialBodyType.LavaPlanet,
            CelestialBodyType.RockyPlanet,
            CelestialBodyType.TechPlanet
        };
        
        int typeIndex = _rng.RandiRange(0, validPlanetTypes.Length - 1);
        CelestialBodyType type = validPlanetTypes[typeIndex];
        
        return GenerateCelestialBodyOfType(coordinates, type);
    }

    /// <summary>
    /// Generates the hexagonal grid for the universe
    /// </summary>
    private void InitializeHexGrid(UniverseModel universe)
    {
        // For a hexagonal grid, we'll create a roughly circular arrangement
        int radius = universe.Size / 2;
        
        // Create cells and initialize with EmptySpace
        for (int q = -radius; q <= radius; q++)
        {
            int r1 = Math.Max(-radius, -q - radius);
            int r2 = Math.Min(radius, -q + radius);
            
            for (int r = r1; r <= r2; r++)
            {
                HexCoordinates coords = new HexCoordinates(q, r);
                // Create the cell (GetOrCreateCell does this)
                universe.GetOrCreateCell(coords);
                // Generate and add the initial EmptySpace body
                // AddCelestialBody will handle linking it to the cell
                var emptyBody = GenerateCelestialBodyOfType(coords, CelestialBodyType.EmptySpace);
                universe.AddCelestialBody(coords, emptyBody); 
            }
        }
    }

    /// <summary>
    /// Generates appropriate features for all celestial bodies in the universe
    /// </summary>
    public void GenerateFeaturesForAllBodies(UniverseModel universe)
    {
        foreach (var cell in universe.GetAllCells())
        {
            if (cell.ContainedBody != null)
            {
                CelestialFeatureGenerator.GenerateFeatures(cell.ContainedBody, _rng);
            }
        }
    }
} 