using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Generation;

public abstract class BaseUniverseGenerator
{
    // Default size of the universe
    public const int DEFAULT_SIZE = 20;

    // Generator configuration
    protected string _seed = "";
    protected string _name;
    protected int _size;

    
    protected readonly CelestialBodyGenerator _celestialBodyGenerator = new();

    public BaseUniverseGenerator(string name = "New Universe", string seed = "", int size = DEFAULT_SIZE)
    {
        _name = name;
        _seed = seed;
        _size = size;
    }
    
    public UniverseModel Generate()
    {
        var universe = _celestialBodyGenerator.InitializeEmptyUniverse(
            name: _name,
            seed: _seed,
            size: _size
        );
        
        GenerateUniverseContent(universe);
        return universe;
    }

    protected abstract void GenerateUniverseContent(UniverseModel universe);
}