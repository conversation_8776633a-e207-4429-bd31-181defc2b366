using System.Collections.Generic;
using System.Linq;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts;
using SpaceGame.Scripts.Serialization.Blueprint;
using SpaceGame.State;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Generation.Factions
{
    /// <summary>
    /// Generates factions and assigns territory in the universe.
    /// </summary>
    public class FactionGenerator
    {
        public class FactionGenerationResult
        {
            public FactionState PlayerFaction { get; private set; }
            public FactionState EnemyFaction { get; private set; }
            
            public FactionGenerationResult(FactionState playerFaction, FactionState enemyFaction)
            {
                PlayerFaction = playerFaction;
                EnemyFaction = enemyFaction;
            }

        }
        
        private readonly RandomNumberGenerator _rng;
        private readonly BlueprintManager _blueprintManager;
        
        // Configuration parameters
        private readonly float _territoryPercentage = 0.1f; // 10% of planets will be assigned to factions
        
        // Minimum distance between faction starting locations (in hex units)
        private readonly int _minimumFactionDistance = 20;
        
        /// <summary>
        /// Creates a new FactionGenerator with the specified seed.
        /// </summary>
        /// <param name="seed">The seed to use for random generation.</param>
        public FactionGenerator(string seed)
        {
            _rng = new RandomNumberGenerator();
            _rng.Seed = (ulong)seed.GetStableHashCode(); // Simple string to int hash for seed
            
            _blueprintManager = BlueprintManager.Instance;
        }

        private FactionState CreatePlayerFaction(ICelestialBodyResolver bodyResolver)
        {
            var faction = CreateFaction(
                bodyResolver: bodyResolver,
                displayName: "Federation",
                type: FactionType.Federation,
                playerRelationship: RelationshipLevel.Allied,
                color: new Color(0.2f, 0.5f, 1.0f), // Blue
                description: "Your faction, representing the democratic Federation of star systems.",
                isPlayerFaction: true // This is the player faction
            );
            
            return faction;
        }

        private FactionState CreateFaction(ICelestialBodyResolver bodyResolver, string displayName, FactionType type, RelationshipLevel playerRelationship,
            Color color, string description = "", bool isPlayerFaction = false)
        {
            var faction = new Faction(displayName, type, playerRelationship, color, description, isPlayerFaction);
            var newFaction = new FactionState(faction, bodyResolver);

            Logger.Info($"Created new faction: {displayName} ({faction.Id})");

            return newFaction;
        }
        
        /// <summary>
        /// Creates an enemy faction if it doesn't already exist.
        /// </summary>
        /// <returns>The enemy faction.</returns>
        public FactionState CreateEnemyFaction(ICelestialBodyResolver bodyResolver)
        {
            return CreateFaction(
                bodyResolver: bodyResolver,
                displayName: "Empire",
                type: FactionType.Empire,
                playerRelationship: RelationshipLevel.Hostile, // Empire starts as hostile
                color: new Color(1.0f, 0.3f, 0.3f), // Red
                description: "An expansionist empire seeking to control the galaxy through military might."
            );
        }

        
        /// <summary>
        /// Generates factions for a universe model.
        /// </summary>
        /// <param name="universe">The universe model to generate factions for.</param>
        public FactionGenerationResult GenerateFactions(UniverseModel universe)
        {
            Logger.Info("Generating factions for universe...");
            
            // Create or get player faction (Federation)
            FactionState playerFaction = CreatePlayerFaction(universe);
            
            // Create or get enemy faction (Empire)
            FactionState enemyFaction = CreateEnemyFaction(universe);
            
            var startingLocations = AssignStartingLocations(universe, playerFaction, enemyFaction);
            if (startingLocations.Count == 0)
            {
                Logger.Warning("Could not assign proper starting locations for factions.");
            }
            else
            {
                Logger.Info("Successfully assigned starting locations. Each faction has one habitable planet and one neighboring star.");
            }
            
            Logger.Info($"Successfully generated factions: {playerFaction.Name}, {enemyFaction.Name}");

            return new FactionGenerationResult(
                playerFaction: playerFaction,
                enemyFaction: enemyFaction
            );
        }
        
        /// <summary>
        /// Assigns territories to factions in the universe.
        /// </summary>
        /// <param name="universe">The universe to assign territories in.</param>
        /// <param name="playerFaction">The player faction.</param>
        /// <param name="enemyFaction">The enemy faction.</param>
        private void AssignTerritories(UniverseModel universe, FactionState playerFaction, FactionState enemyFaction)
        {
            Logger.Info("Assigning territories to factions...");
            
            // First, assign starting locations for each faction
            var startingLocations = AssignStartingLocations(universe, playerFaction, enemyFaction);
            if (startingLocations.Count == 0)
            {
                Logger.Warning("Could not assign proper starting locations. Falling back to random territory assignment.");
            }
            
            // Get all habitable planets
            var habitableBodies = universe.GetAllCelestialBodies()
                .Where(body => body.Habitability != HabitabilityLevel.Uninhabitable)
                .ToList();
            
            // Skip if there are no habitable planets
            if (habitableBodies.Count == 0)
            {
                Logger.Warning("No habitable planets found for faction territories.");
                return;
            }
            
            // If we already assigned starting locations, remove them from the list of planets to assign
            var assignedPlanetIds = new HashSet<string>();
            Dictionary<FactionState, HexCoordinates> factionStartingPoints = new Dictionary<FactionState, HexCoordinates>();
            
            if (startingLocations.Count > 0)
            {
                // Get all planet IDs already assigned to factions as starting locations
                foreach (var (faction, planets) in startingLocations)
                {
                    foreach (var planet in planets)
                    {
                        assignedPlanetIds.Add(planet.Id);
                        
                        // Store the first habitable planet of each faction as its starting point for distance calculations
                        if (!factionStartingPoints.ContainsKey(faction) && 
                            planet.BodyType != CelestialBodyType.Star &&
                            planet.Habitability == HabitabilityLevel.Habitable)
                        {
                            factionStartingPoints[faction] = planet.Position;
                        }
                    }
                }
                
                // Filter out planets that are already assigned as starting locations
                habitableBodies = habitableBodies
                    .Where(body => !assignedPlanetIds.Contains(body.Id))
                    .ToList();
            }
            
            // Determine how many additional planets to assign to each faction
            int planetsToAssign = (int)(habitableBodies.Count * _territoryPercentage);
            int playerPlanets = planetsToAssign / 2;
            int enemyPlanets = planetsToAssign / 2;
            
            Logger.Info($"Assigning {playerPlanets} additional planets to {playerFaction.Name} and {enemyPlanets} additional planets to {enemyFaction.Name}");
            
            // If we have starting points, use distance-based assignment
            if (factionStartingPoints.Count > 0)
            {
                Logger.Info("Using distance-based territory assignment expanding from starting locations.");
                
                // Create a list of habitable planets sorted by distance from faction starting points
                var playerPriority = new List<(CelestialBodyData Planet, float Distance)>();
                var enemyPriority = new List<(CelestialBodyData Planet, float Distance)>();
                
                // Calculate distance of each planet to each faction's starting point
                foreach (var planet in habitableBodies)
                {
                    if (factionStartingPoints.TryGetValue(playerFaction, out var playerStart))
                    {
                        int distance = HexUtils.Distance(planet.Position, playerStart);
                        playerPriority.Add((planet, distance));
                    }
                    
                    if (factionStartingPoints.TryGetValue(enemyFaction, out var enemyStart))
                    {
                        int distance = HexUtils.Distance(planet.Position, enemyStart);
                        enemyPriority.Add((planet, distance));
                    }
                }
                
                // Sort lists by distance (closest first)
                playerPriority.Sort((a, b) => a.Distance.CompareTo(b.Distance));
                enemyPriority.Sort((a, b) => a.Distance.CompareTo(b.Distance));
                
                // Track all planets assigned to each faction to ensure territories don't touch
                var playerAssignedPositions = new HashSet<HexCoordinates>();
                var enemyAssignedPositions = new HashSet<HexCoordinates>();
                
                // Add starting positions to the assigned sets
                if (startingLocations.TryGetValue(playerFaction, out var playerStartingBodies))
                {
                    foreach (var body in playerStartingBodies)
                    {
                        playerAssignedPositions.Add(body.Position);
                    }
                }
                
                if (startingLocations.TryGetValue(enemyFaction, out var enemyStartingBodies))
                {
                    foreach (var body in enemyStartingBodies)
                    {
                        enemyAssignedPositions.Add(body.Position);
                    }
                }
                
                // Define no-touch radius to ensure territories don't touch
                var noTouchRadius = 2; // Planets must be at least this far apart from other faction territories
                
                // Assign planets to player faction based on distance
                int playerAssigned = 0;
                foreach (var (planet, distance) in playerPriority)
                {
                    // Skip if this planet was already assigned
                    if (planet.FactionId != null)
                        continue;
                        
                    // Skip if this planet is too close to enemy territory
                    bool isTooClose = false;
                    foreach (var enemyPos in enemyAssignedPositions)
                    {
                        if (HexUtils.Distance(planet.Position, enemyPos) <= noTouchRadius)
                        {
                            isTooClose = true;
                            break;
                        }
                    }
                    
                    if (isTooClose)
                        continue;
                        
                    // Assign to player faction
                    planet.SetFaction(playerFaction);
                    playerFaction.AddControlledPlanet(planet.Id);
                    playerAssignedPositions.Add(planet.Position);
                    playerAssigned++;
                    
                    Logger.Debug($"Assigned planet {planet.Name} at {planet.Position} to {playerFaction.Name} (distance: {distance})");
                    
                    // Break if we've assigned enough planets
                    if (playerAssigned >= playerPlanets)
                        break;
                }
                
                // Assign planets to enemy faction based on distance
                int enemyAssigned = 0;
                foreach (var (planet, distance) in enemyPriority)
                {
                    // Skip if this planet was already assigned
                    if (planet.FactionId != null)
                        continue;
                        
                    // Skip if this planet is too close to player territory
                    bool isTooClose = false;
                    foreach (var playerPos in playerAssignedPositions)
                    {
                        if (HexUtils.Distance(planet.Position, playerPos) <= noTouchRadius)
                        {
                            isTooClose = true;
                            break;
                        }
                    }
                    
                    if (isTooClose)
                        continue;
                        
                    // Assign to enemy faction
                    planet.SetFaction(enemyFaction);
                    enemyFaction.AddControlledPlanet(planet.Id);
                    enemyAssignedPositions.Add(planet.Position);
                    enemyAssigned++;
                    
                    Logger.Debug($"Assigned planet {planet.Name} at {planet.Position} to {enemyFaction.Name} (distance: {distance})");
                    
                    // Break if we've assigned enough planets
                    if (enemyAssigned >= enemyPlanets)
                        break;
                }
                
                Logger.Info($"Distance-based assignment completed. Assigned {playerAssigned} planets to {playerFaction.Name} and {enemyAssigned} planets to {enemyFaction.Name}");
            }
            else
            {
                // Fallback to random assignment if no starting points were established
                Logger.Info("Using random territory assignment as fallback.");
                
                // Randomize the planets list to avoid bias
                habitableBodies = habitableBodies.OrderBy(_ => _rng.RandfRange(0, 1)).ToList();
                
                // Assign player territories
                for (int i = 0; i < playerPlanets && i < habitableBodies.Count; i++)
                {
                    var planet = habitableBodies[i];
                    planet.SetFaction(playerFaction);
                    
                    // Add the planet to the faction's controlled planets list
                    playerFaction.AddControlledPlanet(planet.Id);
                    
                    Logger.Debug($"Assigned planet {planet.Name} at {planet.Position} to {playerFaction.Name}");
                }
                
                // Assign enemy territories
                for (int i = playerPlanets; i < playerPlanets + enemyPlanets && i < habitableBodies.Count; i++)
                {
                    var planet = habitableBodies[i];
                    planet.SetFaction(enemyFaction);
                    
                    // Add the planet to the faction's controlled planets list
                    enemyFaction.AddControlledPlanet(planet.Id);
                    
                    Logger.Debug($"Assigned planet {planet.Name} at {planet.Position} to {enemyFaction.Name}");
                }
            }
            
            Logger.Info("Finished assigning territories to factions.");
        }
        
        /// <summary>
        /// Assigns starting locations for each faction.
        /// Each faction gets ONLY ONE habitable planet with a star in a neighboring hex.
        /// No additional planets or stars are assigned.
        /// - Player faction starts at or near the center (0,0)
        /// - Other factions are placed further away from center
        /// - Each faction gets exactly one habitable planet and star
        /// - Faction territories don't touch
        /// </summary>
        /// <param name="universe">The universe model</param>
        /// <param name="playerFaction">The player faction</param>
        /// <param name="enemyFaction">The enemy faction</param>
        /// <returns>Dictionary mapping factions to their starting planets and stars</returns>
        private Dictionary<FactionState, List<CelestialBodyData>> AssignStartingLocations(
            UniverseModel universe, 
            FactionState playerFaction, 
            FactionState enemyFaction)
        {
            Logger.Info("Assigning starting locations for factions (one habitable planet and one star per faction)...");
            
            var results = new Dictionary<FactionState, List<CelestialBodyData>>();
            var factions = new List<FactionState> { playerFaction, enemyFaction };
            
            // Get all habitable planets
            var habitablePlanets = universe.GetAllCelestialBodies()
                .Where(body => body.Habitability != HabitabilityLevel.Uninhabitable &&
                               body.BodyType != CelestialBodyType.Star &&
                               body.BodyType != CelestialBodyType.EmptySpace)
                .ToList();
            
            if (habitablePlanets.Count == 0)
            {
                Logger.Warning("No habitable planets found for faction starting locations.");
                return results;
            }
            
            // Find planets that have stars in neighboring hexes
            var suitablePlanets = new List<(CelestialBodyData Planet, CelestialBodyData Star, int DistanceFromCenter)>();
            
            foreach (var planet in habitablePlanets)
            {
                // Get all neighbors of the planet
                var neighbors = HexUtils.GetRing(planet.Position, 1);
                
                // Check if any neighbor is a star
                foreach (var neighborPos in neighbors)
                {
                    var body = universe.GetCelestialBody(neighborPos);
                    if (body is { BodyType: CelestialBodyType.Star })
                    {
                        // Calculate distance from center (0,0)
                        int distanceFromCenter = HexUtils.Distance(planet.Position, HexCoordinates.Zero);
                        suitablePlanets.Add((planet, body, distanceFromCenter));
                        break; // We found a star neighbor, no need to check other neighbors
                    }
                }
            }
            
            if (suitablePlanets.Count == 0)
            {
                Logger.Warning("No suitable planets with neighboring stars found for faction starting locations.");
                return results;
            }
            
            // Sort planets by distance from center
            suitablePlanets = suitablePlanets.OrderBy(p => p.DistanceFromCenter).ToList();
            
            // Keep track of assigned starting locations
            var assignedPlanetPositions = new List<HexCoordinates>();
            var noTouchRadius = _minimumFactionDistance / 2; // Radius to ensure territories don't touch
            
            // First, try to place player faction at or near center (0,0)
            bool playerAssigned = false;
            
            // Try to find a suitable position at the center or close to it for player
            foreach (var (planet, star, distanceFromCenter) in suitablePlanets)
            {
                // Skip planets too far from center for player faction
                if (distanceFromCenter > 5) // Only look at planets very close to center
                    continue;
                
                // Skip if already assigned
                if (planet.FactionId != null || star.FactionId != null)
                    continue;
                
                // This is a suitable starting location for player faction
                // Assign the planet and star to the faction
                playerFaction.AddControlledPlanet(planet);
                playerFaction.AddControlledPlanet(star);
                
                // Add to the results dictionary
                results[playerFaction] = new List<CelestialBodyData> { planet, star };
                
                // Keep track of the assigned position and its "no-touch" zone
                assignedPlanetPositions.Add(planet.Position);
                
                Logger.Info($"Assigned ONLY starting location for {playerFaction.Name}: " +
                           $"Planet {planet.Name} at {planet.Position} with " +
                           $"Star {star.Name} at {star.Position} (distance from center: {distanceFromCenter})");
                
                playerAssigned = true;
                break;
            }
            
            // If we couldn't place the player near center, try any valid position
            if (!playerAssigned)
            {
                Logger.Warning("Could not find suitable starting location near center for player faction. Trying further positions.");
                
                foreach (var (planet, star, _) in suitablePlanets)
                {
                    // Skip if already assigned
                    if (planet.FactionId != null || star.FactionId != null)
                        continue;
                    
                    playerFaction.AddControlledPlanet(planet);
                    playerFaction.AddControlledPlanet(star);
                    
                    // Add to the results dictionary
                    results[playerFaction] = new List<CelestialBodyData> { planet, star };
                    
                    // Keep track of the assigned position
                    assignedPlanetPositions.Add(planet.Position);
                    
                    Logger.Info($"Assigned ONLY starting location for {playerFaction.Name}: " +
                               $"Planet {planet.Name} at {planet.Position} with " +
                               $"Star {star.Name} at {star.Position}");
                    
                    playerAssigned = true;
                    break;
                }
            }
            
            // Now assign other factions, prioritizing those furthest from the player and center
            // Sort suitable planets by distance from center (furthest first) for other factions
            var remainingSuitablePlanets = suitablePlanets.OrderBy(p => p.DistanceFromCenter).ToList();
            
            // Handle enemy and other factions (skip player faction)
            foreach (var faction in factions.Where(f => f.Id != playerFaction.Id))
            {
                bool foundSuitableLocation = false;
                
                foreach (var (planet, star, distanceFromCenter) in remainingSuitablePlanets)
                {
                    // Skip if this planet is already assigned
                    if (planet.FactionId != null)
                        continue;
                    
                    // Skip if this star is already assigned
                    if (star.FactionId != null)
                        continue;
                    
                    // Check if this planet is far enough from other faction starting points
                    // and that their territories won't touch
                    bool isFarEnough = true;
                    foreach (var pos in assignedPlanetPositions)
                    {
                        // Calculate the distance needed to ensure territories don't touch
                        if (HexUtils.Distance(planet.Position, pos) < _minimumFactionDistance)
                        {
                            isFarEnough = false;
                            break;
                        }
                        
                        // Check if any position in the "no-touch radius" around this planet 
                        // would be too close to assigned positions
                        var territoryHexes = HexUtils.GetSpiral(planet.Position, noTouchRadius);
                        foreach (var territoryHex in territoryHexes)
                        {
                            var existingTerritories = HexUtils.GetSpiral(pos, noTouchRadius);
                            if (existingTerritories.Contains(territoryHex))
                            {
                                isFarEnough = false;
                                break;
                            }
                        }
                        
                        if (!isFarEnough)
                            break;
                    }
                    
                    if (!isFarEnough)
                        continue;
                    
                    // Skip planets too close to center for non-player factions
                    if (distanceFromCenter < _minimumFactionDistance)
                        continue;
                    
                    // Add the planet and star to the faction's controlled planets list
                    faction.AddControlledPlanet(planet);
                    faction.AddControlledPlanet(star);
                    
                    // Add to the results dictionary
                    results[faction] = new List<CelestialBodyData> { planet, star };
                    
                    // Keep track of the assigned position
                    assignedPlanetPositions.Add(planet.Position);
                    
                    Logger.Info($"Assigned ONLY starting location for {faction.Name}: " +
                               $"Planet {planet.Name} at {planet.Position} with " +
                               $"Star {star.Name} at {star.Position} (distance from center: {distanceFromCenter})");
                    
                    foundSuitableLocation = true;
                    break;
                }
                
                if (!foundSuitableLocation)
                {
                    Logger.Warning($"Could not find a suitable starting location for {faction.Name}.");
                }
            }
            
            Logger.Info($"Completed faction starting location assignment. Each faction has received ONLY ONE habitable planet and ONE neighboring star.");
            
            return results;
        }
    }
}
