using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using ImGuiNET;
using SpaceGame.Debug;
using SpaceGame.Helpers;

namespace SpaceGame.Universe
{
public partial class Game
{
    private string? _selectedCategory;
    private string? _selectedObjectKey;
    private object? _inspectedObject;
    private Dictionary<string, bool> _categoryExpanded = new();
    private Dictionary<string, bool> _memberExpanded = new();
    
    /// <summary>
    /// Called when the registry changes to update the UI state if needed.
    /// </summary>
    public void OnRegistryChanged()
    {
        // If the selected object was removed, clear the selection
        if (_selectedObjectKey != null && DebugRegistry.GetObject(_selectedObjectKey) == null)
        {
            _selectedObjectKey = null;
            _inspectedObject = null;
        }
        // Otherwise, refresh the inspected object
        else if (_selectedObjectKey != null)
        {
            _inspectedObject = DebugRegistry.GetObject(_selectedObjectKey);
        }
    }
    
    private void DrawObjectInspector()
    {
        // Get all categories
        var categories = DebugRegistry.GetCategories().ToList();
        if (categories.Count == 0)
        {
            ImGui.Text("No objects registered in DebugRegistry.");
            return;
        }
    
        // Main layout with two columns - objects and properties
        if (ImGui.BeginTable("DebugInspectorTable", 2, ImGuiTableFlags.Resizable))
        {
            ImGui.TableSetupColumn("Objects", ImGuiTableColumnFlags.WidthFixed, 200.0f);
            ImGui.TableSetupColumn("Properties", ImGuiTableColumnFlags.WidthStretch);
            ImGui.TableHeadersRow();
        
            // Left column - Object selection with its own scrollable area
            ImGui.TableNextColumn();
            
            // Create a child window for the object selection panel to make it independently scrollable
            float objectPanelHeight = ImGui.GetContentRegionAvail().Y;
            if (ImGui.BeginChild("ObjectSelectionPanel", new Vector2(0, objectPanelHeight)))
            {
                RenderObjectSelectionPanel(categories);
                ImGui.EndChild();
            }
        
            // Right column - Property inspection with its own scrollable area
            ImGui.TableNextColumn();
            
            // Create a child window for the property inspection panel to make it independently scrollable
            if (ImGui.BeginChild("PropertyInspectionPanel"))
            {
                RenderPropertyInspectionPanel();
                ImGui.EndChild();
            }
        
            ImGui.EndTable();
        }
    }
    
        /// <summary>
    /// Renders the left panel with object selection.
    /// </summary>
    private void RenderObjectSelectionPanel(List<string> categories)
    {
        foreach (var category in categories)
        {
            // Initialize category expanded state if not already set
            if (!_categoryExpanded.ContainsKey(category))
            {
                _categoryExpanded[category] = false;
            }
            
            // Category header
            bool isExpanded = ImGui.CollapsingHeader(category, ImGuiTreeNodeFlags.DefaultOpen);
            _categoryExpanded[category] = isExpanded;
            
            if (isExpanded)
            {
                // Get objects in this category
                var objects = DebugRegistry.GetObjectsByCategory(category).ToList();
                
                // Display objects as selectable items
                ImGui.Indent();
                foreach (var (key, obj) in objects)
                {
                    bool isSelected = key == _selectedObjectKey;
                    if (ImGui.Selectable(key, isSelected))
                    {
                        _selectedObjectKey = key;
                        _inspectedObject = obj;
                        _memberExpanded.Clear(); // Reset expanded state when selecting a new object
                    }
                }
                ImGui.Unindent();
            }
        }
    }
    
    /// <summary>
    /// Renders the right panel with property inspection.
    /// </summary>
    private void RenderPropertyInspectionPanel()
    {
        if (_inspectedObject == null)
        {
            ImGui.Text("No object selected.");
            return;
        }
        
        // Display object type
        ImGui.Text($"Type: {_inspectedObject.GetType().FullName}");
        ImGui.Separator();
        
        // Get inspectable members
        var members = ReflectionHelper.GetInspectableMembers(_inspectedObject);
        
        // Group members by declaring type
        var membersByType = members
            .GroupBy(m => m.DeclaringType)
            .OrderBy(g => GetTypeHierarchyDepth(g.Key, _inspectedObject.GetType()));
        
        // Display members grouped by type
        foreach (var typeGroup in membersByType)
        {
            string typeName = typeGroup.Key.Name;
            
            // Create a collapsible header for each type
            if (ImGui.CollapsingHeader(typeName, ImGuiTreeNodeFlags.DefaultOpen))
            {
                ImGui.Indent();
                
                // Display members for this type
                foreach (var member in typeGroup)
                {
                    RenderMember(member);
                }
                
                ImGui.Unindent();
            }
        }
    }
    
    /// <summary>
    /// Gets the depth of a type in the inheritance hierarchy (0 for the most derived type).
    /// </summary>
    private int GetTypeHierarchyDepth(Type type, Type derivedType)
    {
        if (type == derivedType)
            return 0;
            
        int depth = 0;
        Type? currentType = derivedType;
        
        while (currentType != null && currentType != type)
        {
            depth++;
            currentType = currentType.BaseType;
        }
        
        return depth;
    }
    
    /// <summary>
    /// Renders a single member (property or field).
    /// </summary>
    private void RenderMember(ReflectionHelper.InspectableMember member)
    {
        // Get current value
        object? value = member.GetValue();
        string valueStr = value?.ToString() ?? "null";
        
        // Create a unique ID for this member
        string id = $"###{member.Name}";
        
        // Initialize expanded state if not already set
        if (!_memberExpanded.ContainsKey(member.Name))
        {
            _memberExpanded[member.Name] = false;
        }
        
        // For complex objects (Godot objects or other reference types) - use tree nodes
        if (member.IsGodotObject || (!member.IsPrimitive && !member.IsString && !member.IsEnum && !member.IsCollection))
        {
            // Complex object - allow expanding
            string nodeText = $"{member.Name} ({member.Type.Name})";
            if (value == null)
            {
                nodeText += " = null";
            }
            else if (!string.IsNullOrEmpty(valueStr) && valueStr != member.Type.Name)
            {
                // Add a preview of the object's string representation if it's meaningful
                nodeText += $" - {valueStr}";
            }
            
            bool isExpanded = ImGui.TreeNode($"{nodeText}##{member.Name}");
            _memberExpanded[member.Name] = isExpanded;
            
            if (isExpanded)
            {
                if (value == null)
                {
                    ImGui.Text("null");
                }
                else
                {
                    // Group child members by declaring type
                    var childMembers = ReflectionHelper.GetInspectableMembers(value);
                    var membersByType = childMembers
                        .GroupBy(m => m.DeclaringType)
                        .OrderBy(g => GetTypeHierarchyDepth(g.Key, value.GetType()));
                    
                    // Display members grouped by type
                    foreach (var typeGroup in membersByType)
                    {
                        string typeName = typeGroup.Key.Name;
                        
                        // Create a collapsible header for each type
                        if (ImGui.CollapsingHeader(typeName, ImGuiTreeNodeFlags.DefaultOpen))
                        {
                            ImGui.Indent();
                            
                            // Display members for this type
                            foreach (var childMember in typeGroup)
                            {
                                RenderMember(childMember);
                            }
                            
                            ImGui.Unindent();
                        }
                    }
                }
                
                ImGui.TreePop();
            }
        }
        // For collections, use tree nodes
        else if (member.IsCollection)
        {
            int count = 0;
            if (value != null)
            {
                count = GetCollectionCount(value);
            }
            
            bool isExpanded = ImGui.TreeNode($"{member.Name} ({member.Type.Name}) - Collection with {count} items##{member.Name}");
            _memberExpanded[member.Name] = isExpanded;
            
            if (isExpanded)
            {
                if (value == null)
                {
                    ImGui.Text("null collection");
                }
                else
                {
                    // In the future, we could display collection items here
                    ImGui.Text("Collection items not yet supported in the inspector");
                }
                
                ImGui.TreePop();
            }
        }
        // For simple types (primitives, strings, enums), show editors immediately with aligned labels
        else
        {
            // Use a two-column layout for each property
            if (ImGui.BeginTable($"PropertyTable_{member.Name}", 2, ImGuiTableFlags.SizingFixedFit))
            {
                ImGui.TableSetupColumn("Name", ImGuiTableColumnFlags.WidthFixed, 200.0f);
                ImGui.TableSetupColumn("Value", ImGuiTableColumnFlags.WidthStretch);
                
                ImGui.TableNextRow();
                
                // Left column - Property name
                ImGui.TableNextColumn();
                ImGui.AlignTextToFramePadding();
                ImGui.Text($"{member.Name} ({member.Type.Name}):");
                
                // Right column - Property value/editor
                ImGui.TableNextColumn();
                
                if (member.IsReadOnly)
                {
                    // Read-only members just display the value
                    ImGui.TextColored(new Vector4(0.7f, 0.7f, 0.7f, 1.0f), valueStr);
                }
                else if (member.IsPrimitive)
                {
                    RenderPrimitiveEditor(member, value, id);
                }
                else if (member.IsString)
                {
                    RenderStringEditor(member, value, id);
                }
                else if (member.IsEnum)
                {
                    RenderEnumEditor(member, value, id);
                }
                else
                {
                    // Fallback for other simple types
                    ImGui.Text(valueStr);
                }
                
                ImGui.EndTable();
            }
        }
    }
    
    /// <summary>
    /// Renders an editor for primitive types (int, float, bool, etc.).
    /// </summary>
    private void RenderPrimitiveEditor(ReflectionHelper.InspectableMember member, object? value, string id)
    {
        if (value == null)
        {
            ImGui.Text("null");
            return;
        }
        
        if (member.Type == typeof(bool))
        {
            bool boolValue = (bool)value;
            if (ImGui.Checkbox(id, ref boolValue))
            {
                ReflectionHelper.TrySetMemberValue(member, boolValue);
            }
        }
        else if (member.Type == typeof(int))
        {
            int intValue = (int)value;
            if (ImGui.InputInt(id, ref intValue))
            {
                ReflectionHelper.TrySetMemberValue(member, intValue);
            }
        }
        else if (member.Type == typeof(float))
        {
            float floatValue = (float)value;
            if (ImGui.InputFloat(id, ref floatValue))
            {
                ReflectionHelper.TrySetMemberValue(member, floatValue);
            }
        }
        else if (member.Type == typeof(double))
        {
            float floatValue = (float)(double)value;
            if (ImGui.InputFloat(id, ref floatValue))
            {
                ReflectionHelper.TrySetMemberValue(member, (double)floatValue);
            }
        }
        else
        {
            // For other primitives, use a text input
            string stringValue = value.ToString() ?? "";
            if (ImGui.InputText(id, ref stringValue, 100))
            {
                ReflectionHelper.TrySetMemberValue(member, stringValue);
            }
        }
    }
    
    /// <summary>
    /// Renders a string editor.
    /// </summary>
    private void RenderStringEditor(ReflectionHelper.InspectableMember member, object? value, string id)
    {
        string stringValue = value as string ?? "";
        if (ImGui.InputText(id, ref stringValue, 100))
        {
            ReflectionHelper.TrySetMemberValue(member, stringValue);
        }
    }
    
    /// <summary>
    /// Renders an enum editor.
    /// </summary>
    private void RenderEnumEditor(ReflectionHelper.InspectableMember member, object? value, string id)
    {
        if (value == null)
        {
            ImGui.Text("null");
            return;
        }
        
        // Get all enum values
        Array enumValues = Enum.GetValues(member.Type);
        string[] enumNames = Enum.GetNames(member.Type);
        
        // Find current index
        int currentIndex = Array.IndexOf(enumValues, value);
        if (currentIndex >= 0)
        {
            if (ImGui.BeginCombo(id, enumNames[currentIndex]))
            {
                for (int i = 0; i < enumNames.Length; i++)
                {
                    bool isSelected = i == currentIndex;
                    if (ImGui.Selectable(enumNames[i], isSelected))
                    {
                        ReflectionHelper.TrySetMemberValue(member, enumValues.GetValue(i));
                    }
                    
                    if (isSelected)
                    {
                        ImGui.SetItemDefaultFocus();
                    }
                }
                ImGui.EndCombo();
            }
        }
        else
        {
            ImGui.Text(value.ToString() ?? "Invalid enum value");
        }
    }
    
    /// <summary>
    /// Gets the count of items in a collection.
    /// </summary>
    private int GetCollectionCount(object collection)
    {
        if (collection is System.Collections.ICollection c)
        {
            return c.Count;
        }
        
        if (collection is System.Collections.IEnumerable e)
        {
            int count = 0;
            foreach (var _ in e)
            {
                count++;
            }
            return count;
        }
        
        return 0;
    }
}
}
