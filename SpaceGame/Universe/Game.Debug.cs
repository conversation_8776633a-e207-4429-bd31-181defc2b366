using System.Collections.Generic;
using System.Linq;
using Godot;
using ImGuiGodot;
using ImGuiGodot.Internal;
using ImGuiNET;
using R3;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Debug;
using SpaceGame.Scripts.Managers;
using SpaceGame.State;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;
using SpaceGame.Universe.Selection;
using SpaceGame.Scripts.Battle.Entities; // Added for ShipEntity
using SpaceGame.Scripts.Battle; // Added for BattleManager & SpaceBattleScene (and potential BattleScene property)

namespace SpaceGame.Universe
{
    public partial class Game
    {
        // Static flag for debug mode allowing interaction with unrevealed hexes
        public static bool ShowUnrevealedHexesDebug { get; private set; } = false;

        // For debug visualization
        private bool _showDebugHighlight = false;
        private bool _showFactionHighlight = true;
        private bool _showObjectInspector = false;
        private bool _showLogViewer = false;
        
        // Log viewer instance
        private LogViewer _logViewer = new();

        private string _seedInput = "";

        private CelestialBodyRegistry _celestialRegistry => CelestialBodyRegistry.GetInstance();
        private HexGridManager _hexGridManager => _universeRoot.HexGridManager;
        private SelectionManager _selectionManager => _universeRoot.SelectionManager;

        private void CreateDebugWindow()
        {
            // Set up window flags
            ImGuiWindowFlags windowFlags =
                ImGuiWindowFlags.NoCollapse
                | ImGuiWindowFlags.NoBringToFrontOnFocus
                | ImGuiWindowFlags.MenuBar
                | ImGuiWindowFlags.NoNavFocus;

            // Start the main UI window
            if (ImGui.Begin("Main UI", windowFlags))
            {
                // Add a menu bar at the top of the debug window
                if (ImGui.BeginMenuBar())
                {
                    if (ImGui.BeginMenu("Debug Tools"))
                    {
                        if (ImGui.MenuItem("Object Inspector", null, _showObjectInspector))
                        {
                            _showObjectInspector = !_showObjectInspector;
                            Logger.Debug($"Object Inspector visibility toggled: {_showObjectInspector}");
                        }
                        
                        if (ImGui.MenuItem("Log Viewer", null, _showLogViewer))
                        {
                            _showLogViewer = !_showLogViewer;
                            Logger.Debug($"Log Viewer visibility toggled: {_showLogViewer}");
                        }
                        
                        ImGui.EndMenu();
                    }
                    ImGui.EndMenuBar();
                }
            
                // Only show the Object Inspector window when the toggle is enabled
                if (_showObjectInspector)
                {
                    if (ImGui.Begin("Object Inspector", ref _showObjectInspector, ImGuiWindowFlags.NoCollapse))
                    {
                        DrawObjectInspector();
                    }
                    ImGui.End();
                }
                
                // Show the Log Viewer window when enabled
                if (_showLogViewer)
                {
                    _logViewer.Draw(ref _showLogViewer);
                }

                if (
                    ImGui.BeginChild(
                        "Engine Data",
                        System.Numerics.Vector2.Zero,
                        ImGuiChildFlags.AutoResizeY | ImGuiChildFlags.AlwaysAutoResize
                    )
                )
                {
                    DrawEngineData();
                }

                ImGui.EndChild();

                var dockSpaceId = ImGui.GetID("dockspace");
                ImGui.DockSpace(dockSpaceId, System.Numerics.Vector2.Zero, ImGuiDockNodeFlags.None);
                // Universe Info Section
                if (ImGui.Begin("Debug", ImGuiWindowFlags.NoCollapse | ImGuiWindowFlags.NoResize))
                {
                    DrawImGuiOverlay();
                }

                ImGui.End();

                // Generation Section
                if (
                    ImGui.Begin(
                        "Generation",
                        ImGuiWindowFlags.NoCollapse | ImGuiWindowFlags.NoResize
                    )
                )
                {
                    // Universe Seed Section
                    if (
                        ImGui.BeginChild(
                            "Universe Seed Section",
                            System.Numerics.Vector2.Zero,
                            ImGuiChildFlags.AutoResizeY | ImGuiChildFlags.AlwaysAutoResize
                        )
                    )
                    {
                        DrawDebugSeedWindow();
                    }

                    ImGui.EndChild();
                }

                ImGui.End();

                // GameState Debug Section
                if (
                    ImGui.Begin(
                        "GameState",
                        ImGuiWindowFlags.NoCollapse | ImGuiWindowFlags.NoResize
                    )
                )
                {
                    DrawGameStateDebugWindow();
                }

                ImGui.End();
                
                // Battle Testing Section
                if (
                    ImGui.Begin(
                        "Battle Testing",
                        ImGuiWindowFlags.NoCollapse | ImGuiWindowFlags.NoResize
                    )
                )
                {
                    DrawBattleTestingWindow();
                }

                ImGui.End();
            }

            ImGui.End();
        }

        private void DrawEngineData()
        {
            ImGui.Text("Engine Data");
            ImGui.Separator();
            ImGui.Text($"FPS: {Engine.GetFramesPerSecond()}");
            ImGui.Text($"CPU Architecture: {Engine.GetArchitectureName()}");
            ImGui.Text("Log Observables:"); ImGui.SameLine();
            if (ImGui.Button("Log"))
            {
                ObservableTracker.ForEachActiveTask(state =>
                {
                    Logger.Debug(state.ToString());
                });
            }

            // Count active celestial bodies
            int totalBodies = _celestialRegistry.Count;
            int activeBodies = _celestialRegistry
                .GetAllBodies()
                .Count(kvp =>
                    IsInstanceValid(kvp.Value) && kvp.Value.ProcessMode != ProcessModeEnum.Disabled
                );

            ImGui.Text($"Celestial Bodies: {activeBodies} active / {totalBodies} total");

            ImGui.Separator();
        }

        private void InfoLine(string label, string value)
        {
            ImGui.Text(label); ImGui.SameLine(); ImGui.Text(value);
        }

        private void DrawImGuiOverlay()
        {
            // Debug Tools Section
            ImGui.Text("Universe Info");
            ImGui.Separator();
            InfoLine("ID: ", _currentUniverseModel.Id);
            InfoLine("Name: ", _currentUniverseModel.Name);
            InfoLine("Seed: ", _currentUniverseModel.Seed);
            var stats = _currentUniverseModel.GetStats();
            InfoLine("Creation date: ", stats.CreationDate.ToString());
            InfoLine("Size: ", stats.UniverseSize.ToString());
            InfoLine("Total Hexes: ", stats.CellCount.ToString());
            InfoLine("Celestial bodies: ", stats.CelestialBodyCount.ToString());
            ImGui.Separator();

            // Show selection info
            var selectedBody = _selectionManager.GetSelectedCelestialBody();
            var selectedHexagon = _selectionManager.GetSelectedHexagon();

            ImGui.Text("Selection:");
            if (IsInstanceValid(selectedHexagon))
            {
                ImGui.Text($"Hexagon: {selectedHexagon.HexPosition}");
            }
            else
            {
                ImGui.Text("Hexagon: None");
            }

            if (IsInstanceValid(selectedBody))
            {
                ImGui.Text($"Body: {selectedBody.GetInfoText()}");

                // Add faction control dropdown
                ImGui.Separator();
                ImGui.Text("Faction Control:");
                // Get all factions (FactionState) and display names
                var factions = _gameStateManager.GetAllFactions().ToList();
                var factionNames = new List<string> { "None" };
                factionNames.AddRange(factions.Select(f => f.ToString()));
                // Find the index of the current faction, or 0 if none
                int currentFactionIndex = 0;
                if (selectedBody.Data.FactionId != null)
                {
                    int foundIndex = factions.FindIndex(f => f.Id == selectedBody.Data.FactionId);
                    if (foundIndex != -1)
                        currentFactionIndex = foundIndex + 1; // +1 for None
                }

                if (
                    ImGui.Combo(
                        "Set Faction",
                        ref currentFactionIndex,
                        factionNames.ToArray(),
                        factionNames.Count
                    )
                )
                {
                    if (currentFactionIndex == 0)
                    {
                        // Remove control
                        if (selectedBody.Data.FactionId != null)
                        {
                            _gameStateManager.RemovePlanetFromFaction(
                                selectedBody.Data.FactionId,
                                selectedBody.Data.Id
                            );
                            _hexGridManager.RefreshHexagon(selectedBody.HexPosition);
                            Logger.Info(
                                $"Removed faction control from {selectedBody.Name} at {selectedBody.HexPosition}"
                            );
                        }
                    }
                    else
                    {
                        var selectedFaction = factions[currentFactionIndex - 1]; // -1 for None
                        if (selectedFaction != null)
                        {
                            if (
                                _gameStateManager.AssignPlanetToFaction(
                                    selectedFaction.Id,
                                    selectedBody.Data
                                )
                            )
                            {
                                var hexPos = selectedBody.HexPosition;
                                _hexGridManager.RefreshHexagon(hexPos);
                                Logger.Info(
                                    $"Set faction for {selectedBody.Name} at {hexPos} to {selectedFaction.Name}"
                                );
                            }
                            else
                            {
                                Logger.Error(
                                    $"Unable to assign faction {selectedFaction.Name} to {selectedBody.Name}"
                                );
                            }
                        }
                    }
                }
            }
            else
            {
                ImGui.Text("Body: None");
            }

            // Fog of War Debug
            ImGui.Separator();
            ImGui.TextColored(new System.Numerics.Vector4(0.5f, 0.8f, 0.5f, 1), "Fog of War Debug");
            bool showUnrevealed = ShowUnrevealedHexesDebug;
            if (ImGui.Checkbox("Show Unrevealed Hexes (Semi-Transparent)", ref showUnrevealed))
            {
                ShowUnrevealedHexesDebug = showUnrevealed;
                // Trigger a refresh of all hex visuals
                _universeRoot.UpdateAllHexVisuals();
            }

            if (selectedHexagon != null)
            {
                // Use the stored model to check the cell data
                var cellData = _currentUniverseModel?.GetCell(selectedHexagon.HexPosition);
                bool isCurrentlyRevealed = cellData?.IsRevealed ?? false; // Default to false if cell/model is null

                if (
                    ImGui.Button(
                        isCurrentlyRevealed ? "Un-Reveal Selected Hex" : "Reveal Selected Hex"
                    )
                )
                {
                    if (_currentUniverseModel != null && cellData != null)
                    {
                        cellData.IsRevealed = !isCurrentlyRevealed;
                        _universeRoot.UpdateNeighboringHexVisuals(selectedHexagon.HexPosition);
                        selectedHexagon.UpdateVisualState(_currentUniverseModel); // Update the selected hex itself
                        Logger.Info(
                            $"Manually set reveal status for hex {selectedHexagon.HexPosition} to {cellData.IsRevealed}"
                        );
                    }
                }
            }

            // Show camera info
            ImGui.Separator();
            var camera = _universeRoot.Camera;
            ImGui.Text($"Camera Position: {camera.Position.X:F1}, {camera.Position.Y:F1}");
            ImGui.Text($"Camera Zoom: {camera.Zoom.X:F2}x");

            // Debug options
            ImGui.Separator();
            ImGui.Text("Display Options:");

            bool newDebugHighlightMode = _showDebugHighlight;
            if (ImGui.Checkbox("Show Hex Debug Highlight", ref newDebugHighlightMode))
            {
                _showDebugHighlight = newDebugHighlightMode;
                _hexGridManager.SetDebugHighlightMode(_showDebugHighlight);
            }

            // Add faction highlight toggle
            bool factionHighlight = _showFactionHighlight;
            if (ImGui.Checkbox("Show Faction Highlight", ref factionHighlight))
            {
                _showFactionHighlight = factionHighlight;
                _hexGridManager.SetFactionHighlightMode(_showFactionHighlight);
            }

            // Add option to highlight faction starting locations
            ImGui.Separator();
            if (ImGui.CollapsingHeader("Faction Starting Locations"))
            {
                // Get all factions
                var factions = _gameStateManager.GetAllFactions().ToList();

                if (factions.Count == 0)
                {
                    ImGui.Text("No factions available.");
                }
                else
                {
                    ImGui.Text($"Displaying starting locations for {factions.Count} factions:");
                    ImGui.Spacing();

                    foreach (var faction in factions)
                    {
                        ImGui.PushStyleColor(ImGuiCol.Text, faction.Color.ToVector4());

                        // Identify the starting planets and stars for this faction
                        var controlledPlanets = faction.ControlledPlanets;

                        var habitablePlanets = controlledPlanets
                            .Where(p =>
                                p.Habitability == HabitabilityLevel.Habitable
                                && p.BodyType != CelestialBodyType.Star
                            )
                            .ToList();

                        var stars = controlledPlanets
                            .Where(p => p.BodyType == CelestialBodyType.Star)
                            .ToList();

                        ImGui.Text($"{faction.Name} ({faction.Type}):");

                        // Display faction's habitable planets
                        if (habitablePlanets.Count > 0)
                        {
                            ImGui.Indent();
                            ImGui.Text("Habitable Planets:");
                            ImGui.Indent();

                            foreach (var planet in habitablePlanets)
                            {
                                // Check if this planet has a star neighbor
                                var neighbors = HexUtils.GetRing(planet.Position, 1);
                                var hasStarNeighbor = false;
                                foreach (var neighborPos in neighbors)
                                {
                                    var neighborBody = _currentUniverseModel.GetCelestialBody(
                                        neighborPos
                                    );
                                    if (
                                        neighborBody != null
                                        && neighborBody.BodyType == CelestialBodyType.Star
                                        && neighborBody.FactionId == faction.Id
                                    )
                                    {
                                        hasStarNeighbor = true;
                                        break;
                                    }
                                }

                                string startingLocationIndicator = hasStarNeighbor
                                    ? " (Starting Location)"
                                    : "";

                                // Check if the planet is selected
                                if (
                                    ImGui.Selectable(
                                        $"{planet.Name} at {planet.Position}{startingLocationIndicator}"
                                    )
                                )
                                {
                                    // Center the camera on this planet if selected
                                    Vector2 worldPos = HexUtils.AxialToWorld(
                                        planet.Position.Q,
                                        planet.Position.R
                                    );
                                    camera.GlobalPosition = worldPos;

                                    // Also select the planet
                                    var body = FindCelestialBodyAtPosition(planet.Position);
                                    if (body != null)
                                    {
                                        _selectionManager.SelectCelestialBody(body);
                                    }
                                }
                            }

                            ImGui.Unindent();
                            ImGui.Unindent();
                        }
                        else
                        {
                            ImGui.Indent();
                            ImGui.Text("No habitable planets controlled.");
                            ImGui.Unindent();
                        }

                        // Display faction's stars
                        if (stars.Count > 0)
                        {
                            ImGui.Indent();
                            ImGui.Text("Stars:");
                            ImGui.Indent();

                            foreach (var star in stars)
                            {
                                // Check if the star is selected
                                if (ImGui.Selectable($"{star.Name} at {star.Position}"))
                                {
                                    // Center the camera on this star if selected
                                    Vector2 worldPos = HexUtils.AxialToWorld(
                                        star.Position.Q,
                                        star.Position.R
                                    );
                                    camera.GlobalPosition = worldPos;

                                    // Also select the star
                                    var body = FindCelestialBodyAtPosition(star.Position);
                                    if (body != null)
                                    {
                                        _selectionManager.SelectCelestialBody(body);
                                    }
                                }
                            }

                            ImGui.Unindent();
                            ImGui.Unindent();
                        }

                        ImGui.PopStyleColor();
                        ImGui.Spacing();
                    }

                    // Add a button to center on the player faction's starting location
                    var playerFaction = factions.FirstOrDefault(f => f.IsPlayerFaction);
                    if (playerFaction != null)
                    {
                        ImGui.Separator();
                        if (ImGui.Button("Center on Player Starting Location"))
                        {
                            // Try to find the player's starting planet
                            var controlledPlanets = playerFaction
                                .ControlledPlanets.Where(p =>
                                    p.Habitability == HabitabilityLevel.Habitable
                                    && p.BodyType != CelestialBodyType.Star
                                )
                                .ToList();

                            // Check each planet to see if it has a neighboring star that belongs to the player faction
                            foreach (var planet in controlledPlanets)
                            {
                                var neighbors = HexUtils.GetRing(planet.Position, 1);
                                var hasStarNeighbor = false;

                                foreach (var neighborPos in neighbors)
                                {
                                    var neighborBody = _currentUniverseModel.GetCelestialBody(
                                        neighborPos
                                    );
                                    if (
                                        neighborBody != null
                                        && neighborBody.BodyType == CelestialBodyType.Star
                                        && neighborBody.FactionId == playerFaction.Id
                                    )
                                    {
                                        hasStarNeighbor = true;
                                        break;
                                    }
                                }

                                if (hasStarNeighbor)
                                {
                                    // This is likely the starting planet
                                    Vector2 worldPos = HexUtils.AxialToWorld(
                                        planet.Position.Q,
                                        planet.Position.R
                                    );
                                    camera.GlobalPosition = worldPos;

                                    // Also select the planet
                                    var body = FindCelestialBodyAtPosition(planet.Position);
                                    if (body != null)
                                    {
                                        _selectionManager.SelectCelestialBody(body);
                                    }

                                    break;
                                }
                            }
                        }
                    }
                }
            }

            // Add button to clear selection
            if (ImGui.Button("Clear Selection"))
            {
                _selectionManager.ClearSelection();
            }
        }

        private void DrawDebugSeedWindow()
        {
            ImGui.PushStyleVar(ImGuiStyleVar.FramePadding, new System.Numerics.Vector2(10, 5));
            ImGui.TextColored(new System.Numerics.Vector4(1, 1, 0, 1), "Universe Generation");
            ImGui.Separator();

            ImGui.Text("Current Seed: ");
            ImGui.SameLine();
            ImGui.TextColored(new System.Numerics.Vector4(0.2f, 0.8f, 1.0f, 1.0f), _universeRoot.CurrentSeed);

            // Seed input
            ImGui.Text("New Seed: ");
            ImGui.SameLine();
            ImGui.SetNextItemWidth(150);
            
            if (ImGui.InputText("##UniverseSeed", ref _seedInput, 100))
            {
                // Nothing to do here, we'll update when button is clicked
            }

            ImGui.SameLine();
            if (ImGui.Button("Generate"))
            {
                _universeRoot.UpdateGeneratorSeed(_seedInput);
            }

            if (ImGui.IsItemHovered())
            {
                ImGui.BeginTooltip();
                ImGui.Text("Regenerate using the entered seed");
                ImGui.EndTooltip();
            }

            // Multi-pass universe generation options
            ImGui.Separator();
            ImGui.TextColored(new System.Numerics.Vector4(1, 0.5f, 0, 1), "Multi-Pass Generation");
            ImGui.TextWrapped(
                "Generates celestial bodies for universes using a multi-pass approach for balanced distribution"
            );

            if (ImGui.Button("Small Universe"))
            {
                GenerateMultiPassSmallUniverse();
            }

            if (ImGui.IsItemHovered())
            {
                ImGui.BeginTooltip();
                ImGui.Text("Generate a small universe with multi-pass generation");
                ImGui.EndTooltip();
            }

            ImGui.SameLine();
            if (ImGui.Button("Medium Universe"))
            {
                GenerateMultiPassMediumUniverse();
            }

            if (ImGui.IsItemHovered())
            {
                ImGui.BeginTooltip();
                ImGui.Text("Generate a medium-sized universe with multi-pass generation");
                ImGui.EndTooltip();
            }

            ImGui.SameLine();
            if (ImGui.Button("Large Universe"))
            {
                GenerateMultiPassLargeUniverse();
            }

            if (ImGui.IsItemHovered())
            {
                ImGui.BeginTooltip();
                ImGui.Text("Generate a large universe with multi-pass generation");
                ImGui.EndTooltip();
            }

            // Show example seeds
            ImGui.Separator();
            ImGui.Text("Preset Seeds:");

            // Common preset seeds
            string[] presetSeeds = new string[]
            {
                "galaxy42",
                "cool space",
                "earth prime",
                "utopia",
                "dystopia",
                "void",
                "paradise",
                "spaceGame",
            };

            // Display preset seeds as buttons
            foreach (var preset in presetSeeds)
            {
                if (ImGui.Button(preset))
                {
                    _universeRoot.UpdateGeneratorSeed(preset);
                }

                ImGui.SameLine();
            }

            ImGui.NewLine();
            ImGui.Text("Same seed always generates the same universe");
            ImGui.Text("Note: Regenerating will destroy all current celestial bodies");

            ImGui.PopStyleVar();
        }

        private void DrawPlanetListWindow()
        {
            ImGui.TextColored(new System.Numerics.Vector4(1, 1, 0, 1), "Celestial Bodies");
            ImGui.Separator();
            ImGui.Text(
                $"NOTE: Opening a list with a lot of celestial bodies will negatively impact performance !!!"
            );
            ImGui.Separator();

            var allBodies = _celestialRegistry.GetAllBodies();
            int bodyCount = _celestialRegistry.Count;

            ImGui.Text($"Total bodies: {bodyCount}");
            ImGui.Separator();

            // Group bodies by type
            var starsList = allBodies.Where(b => b.Value is Sun).ToList();
            var planetsList = allBodies.Where(b => b.Value is Planet).ToList();
            var cosmicObjectsList = allBodies.Where(b => b.Value is Cosmic.CosmicObject).ToList();

            // Stars/Suns section
            bool starsOpen = ImGui.CollapsingHeader($"Stars ({starsList.Count})");
            if (starsOpen)
            {
                if (starsList.Count > 0)
                {
                    DrawCelestialBodyTable(starsList, "Stars");
                }
                else
                {
                    ImGui.Text("No stars in the universe.");
                }
            }

            // Regular Planets section
            bool planetsOpen = ImGui.CollapsingHeader($"Planets ({planetsList.Count})");
            if (planetsOpen)
            {
                if (planetsList.Count > 0)
                {
                    DrawCelestialBodyTable(planetsList, "Planets");
                }
                else
                {
                    ImGui.Text("No planets in the universe.");
                }
            }

            // Cosmic Objects section
            bool cosmicOpen = ImGui.CollapsingHeader($"Cosmic Objects ({cosmicObjectsList.Count})");
            if (cosmicOpen)
            {
                if (cosmicObjectsList.Count > 0)
                {
                    DrawCelestialBodyTable(cosmicObjectsList, "CosmicObjects");
                }
                else
                {
                    ImGui.Text("No cosmic objects in the universe.");
                }
            }
        }

        private void DrawCelestialBodyTable(
            List<KeyValuePair<HexCoordinates, CelestialBody>> bodies,
            string tableId
        )
        {
            if (
                ImGui.BeginTable(
                    $"CelestialBodiesTable_{tableId}",
                    5,
                    ImGuiTableFlags.Borders | ImGuiTableFlags.RowBg
                )
            )
            {
                ImGui.TableSetupColumn("Type");
                ImGui.TableSetupColumn("Name");
                ImGui.TableSetupColumn("Position");
                ImGui.TableSetupColumn("Faction");
                ImGui.TableSetupColumn("Actions");
                ImGui.TableHeadersRow();

                foreach (var bodyEntry in bodies)
                {
                    var hexPosition = bodyEntry.Key;
                    var body = bodyEntry.Value;

                    ImGui.TableNextRow();

                    // Column 1: Type
                    ImGui.TableNextColumn();

                    // Color-code different types
                    System.Numerics.Vector4 typeColor = new System.Numerics.Vector4(
                        1.0f,
                        1.0f,
                        1.0f,
                        1.0f
                    ); // White default
                    string typeText = body.Data.BodyType.ToString();
                    ImGui.TextColored(typeColor, typeText);

                    // Column 2: Name
                    ImGui.TableNextColumn();
                    ImGui.Text(body.Name);

                    // Column 3: Position
                    ImGui.TableNextColumn();
                    ImGui.Text($"({hexPosition})");

                    // Column 4: Faction
                    ImGui.TableNextColumn();
                    ImGui.Text(body.Data.FactionType.ToString());

                    // Column 5: Actions
                    ImGui.TableNextColumn();
                    if (ImGui.Button($"Select##{body.GetInstanceId()}"))
                    {
                        // Get world position from hex coordinates
                        Vector2 worldPos = HexUtils.AxialToWorld(hexPosition.Q, hexPosition.R);

                        // Set as selected body
                        _selectionManager.SelectCelestialBody(body);

                        // Center camera on this body
                        _universeRoot.SetCameraTarget(worldPos);
                    }
                }

                ImGui.EndTable();
            }
        }

        private void DrawGameStateDebugWindow()
        {
            ImGui.PushStyleVar(ImGuiStyleVar.FramePadding, new System.Numerics.Vector2(10, 5));

            if (ImGui.Button("Save Current Game State"))
            {
                Error result = SaveLoadManager.Instance.SaveGame(_gameState);
                if (result == Error.Ok)
                {
                    Logger.Info("GameState saved successfully via Debug UI");
                }
                else
                {
                    Logger.Error($"Failed to save GameState from Debug UI. Error: {result}");
                }
            }

            ImGui.SameLine();

            if (ImGui.Button("Reload Game State"))
            {
                bool result = ReloadGameState();
                if (result)
                {
                    Logger.Info("GameState reloaded successfully via Debug UI");
                }
                else
                {
                    Logger.Error("Failed to reload GameState from Debug UI");
                }
            }

            ImGui.Separator();

            if (ImGui.CollapsingHeader("Game State", ImGuiTreeNodeFlags.DefaultOpen))
            {
                ImGui.Indent(10);

                // Add Factions section
                if (ImGui.CollapsingHeader("Factions"))
                {
                    ImGui.Indent(10);
                    DrawFactionsSection();
                    ImGui.Unindent(10);
                }

                // Planet List Section
                if (ImGui.CollapsingHeader("Planets"))
                {
                    ImGui.Indent(10);
                    DrawPlanetListWindow();
                    ImGui.Unindent(10);
                }

                ImGui.Unindent();
            }

            ImGui.PopStyleVar();
        }

        /// <summary>
        /// Draws the factions section of the debug UI with nested faction information.
        /// </summary>
        private void DrawFactionsSection()
        {
            // Get all factions from the GameManagerFacade
            var factions = _gameStateManager.GetAllFactions().ToList();
            ImGui.Text($"Total Factions: {factions.Count}");
            ImGui.Separator();

            if (factions.Count > 0)
            {
                foreach (var faction in factions)
                {
                    // Determine if this is the player faction and style accordingly
                    System.Numerics.Vector4 headerColor = faction.IsPlayerFaction
                        ? new System.Numerics.Vector4(0.3f, 0.7f, 1.0f, 1.0f) // Brighter blue for player faction
                        : new System.Numerics.Vector4(1.0f, 1.0f, 1.0f, 1.0f); // White for other factions

                    // Create the faction header with player indicator if applicable
                    string playerIndicator = faction.IsPlayerFaction ? " [PLAYER]" : "";
                    string headerText =
                        $"{faction.Name}{playerIndicator} ({faction.Type})";

                    ImGui.PushStyleColor(ImGuiCol.Text, headerColor);
                    bool factionOpen = ImGui.CollapsingHeader(
                        $"{headerText}##Faction{faction.Id}",
                        ImGuiTreeNodeFlags.DefaultOpen
                    );
                    ImGui.PopStyleColor();

                    if (factionOpen)
                    {
                        ImGui.Indent(10);

                        // Display faction details
                        ImGui.Text($"Faction ID: {faction.Id}");
                        ImGui.Text($"Relationship: {faction.PlayerRelationship}");

                        // Display faction color as a colored square
                        System.Numerics.Vector4 factionColor = new System.Numerics.Vector4(
                            faction.Color.R,
                            faction.Color.G,
                            faction.Color.B,
                            faction.Color.A
                        );
                        ImGui.ColorButton(
                            "##FactionColor",
                            factionColor,
                            ImGuiColorEditFlags.NoTooltip,
                            new System.Numerics.Vector2(20, 20)
                        );
                        ImGui.SameLine();
                        ImGui.Text("Faction Color");

                        if (!string.IsNullOrEmpty(faction.Description))
                        {
                            ImGui.TextWrapped($"Description: {faction.Description}");
                        }

                        ImGui.Separator();

                        // Display planets controlled by this faction
                        var planets = faction.ControlledPlanets;
                        if (
                            ImGui.TreeNode(
                                $"Controlled Planets ({planets.Count})##FactionPlanets{faction.Id}"
                            )
                        )
                        {
                            DrawFactionPlanets(faction);
                            ImGui.TreePop();
                        }

                        ImGui.Unindent(10);
                    }
                }
            }
            else
            {
                ImGui.TextColored(
                    new System.Numerics.Vector4(1.0f, 1.0f, 0.0f, 1.0f),
                    "No factions found"
                );
            }
        }

        /// <summary>
        /// Draws the planets controlled by a specific faction.
        /// </summary>
        private void DrawFactionPlanets(FactionState faction)
        {
            var planets = faction.ControlledPlanets;
            if (planets.Count > 0)
            {
                if (
                    ImGui.BeginTable(
                        $"PlanetsTable{faction.Id}",
                        4,
                        ImGuiTableFlags.Borders | ImGuiTableFlags.RowBg
                    )
                )
                {
                    ImGui.TableSetupColumn("Name");
                    ImGui.TableSetupColumn("Type");
                    ImGui.TableSetupColumn("Position");
                    ImGui.TableSetupColumn("Actions");
                    ImGui.TableHeadersRow();

                    foreach (var planet in planets)
                    {
                        ImGui.TableNextRow();

                        // Planet Name
                        ImGui.TableSetColumnIndex(0);
                        ImGui.Text(planet.Name);

                        // Planet Type
                        ImGui.TableSetColumnIndex(1);
                        ImGui.Text(planet.BodyType.ToString());

                        // Position
                        ImGui.TableSetColumnIndex(2);
                        ImGui.Text(planet.Position.ToString());

                        // Actions
                        ImGui.TableSetColumnIndex(3);

                        // View planet button
                        if (ImGui.SmallButton($"View##Planet{planet.Id}"))
                        {
                            // Get world position from hex coordinates
                            Vector2 worldPos = HexUtils.AxialToWorld(
                                planet.Position.Q,
                                planet.Position.R
                            );

                            // Find the celestial body node
                            var hex = _hexGridManager.GetHexagon(planet.Position);
                            if (hex != null)
                            {
                                // Set as selected body
                                _selectionManager.SelectHexagon(hex);
                            }

                            // Center camera on this planet
                            _universeRoot.SetCameraTarget(worldPos);
                        }

                        ImGui.SameLine();

                        // Release control button
                        if (ImGui.SmallButton($"Release Control##Planet{planet.Id}"))
                        {
                            _gameStateManager.RemovePlanetFromFaction(faction.Id, planet.Id);
                            _hexGridManager.RefreshHexagon(planet.Position);
                        }
                    }

                    ImGui.EndTable();
                }
            }
            else
            {
                ImGui.TextColored(
                    new System.Numerics.Vector4(1.0f, 1.0f, 0.0f, 1.0f),
                    "No planets controlled by this faction"
                );
            }
        }
        
        // Helper method to find a celestial body at a specific position
        private CelestialBody? FindCelestialBodyAtPosition(HexCoordinates position)
        {
            return _hexGridManager.GetHexagon(position)?.ContainedBody;
        }
        
        /// <summary>
        /// Generates a small test universe with multi-pass generation
        /// </summary>
        private void GenerateMultiPassSmallUniverse()
        {
            Logger.Info("Generating small universe with multi-pass generation");
            GenerateMultiPassUniverseFromSeed(_universeRoot.CurrentSeed, 20);
        }
    
        /// <summary>
        /// Generates a medium-sized universe with multi-pass generation
        /// </summary>
        private void GenerateMultiPassMediumUniverse()
        {
            Logger.Info("Generating medium universe with multi-pass generation");
            GenerateMultiPassUniverseFromSeed(_universeRoot.CurrentSeed, 100);
        }

        private void GenerateMultiPassUniverseFromSeed(string seed, int size)
        {
            Logger.Debug($"Generating universe with seed '{seed}' and size {size}");
            _universeRoot.GenerateUniverseFromSeed(seed, size);
            _seedInput = seed;
        }
    
        /// <summary>
        /// Generates a large universe with multi-pass generation
        /// </summary>
        private void GenerateMultiPassLargeUniverse()
        {
            Logger.Info("Generating large universe with multi-pass generation");
            GenerateMultiPassUniverseFromSeed(_universeRoot.CurrentSeed, 200);
        }
    }
}
