using System.Collections.Generic;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Hexagon;

public partial class Hexagon : Node2D
{
    public HexCoordinates HexPosition { get; private set; }
    [Export] public bool IsSelected { get; private set; }
    
    public bool IsOccupied => ContainedBody != null;

    // Reference to the celestial body in this hex (if any)
    public CelestialBody? ContainedBody { get; private set; }

    // Visibility notifier for the hex
    private VisibleOnScreenNotifier2D _visibilityNotifier;

    // Hex size and visual properties
    private Color _defaultFillColor = new Color(1, 1, 1, 0.1f);
    private Color _occupiedFillColor = new Color(0, 1, 0, 0.2f);
    private Color _selectedFillColor = new Color(1, 1, 1, 0.3f);
    private Color _selectedOccupiedFillColor = new Color(0, 1, 0, 0.4f);
    private Color _defaultBorderColor = new Color(1, 1, 1, 0.5f);
    private Color _selectedBorderColor = new Color(1, 1, 1, 0.9f);
    
    // Faction colors for hex borders
    private static readonly Dictionary<FactionType, Color> FactionColors = new()
    {
        { FactionType.None, new Color(1, 1, 1, 0.5f) }, // Default/Neutral border color
        { FactionType.Federation, new Color(0.2f, 0.5f, 1.0f, 0.7f) }, // Blueish
        { FactionType.Empire, new Color(1.0f, 0.3f, 0.3f, 0.7f) }, // Reddish
    };
    
    // Faction fill colors (more transparent versions for highlighting)
    private static readonly Dictionary<FactionType, Color> FactionFillColors = new()
    {
        { FactionType.None, new Color(1, 1, 1, 0.1f) }, // Default fill color
        { FactionType.Federation, new Color(0.2f, 0.5f, 1.0f, 0.2f) }, // Transparent blue
        { FactionType.Empire, new Color(1.0f, 0.3f, 0.3f, 0.2f) }, // Transparent red
    };
    
    // Debug highlight mode - separates debug visualization from actual hex display
    private bool _debugHighlightMode = true;
    
    // Faction highlight mode - highlights hexes with faction-colored fills
    private bool _factionHighlightMode = false;
    
    // Fog of War state
    private bool _isRevealed = false;
    private bool _isNeighborRevealed = false; // Is at least one neighbor revealed?
    
    // Cache the polygon points for reuse
    private Vector2[] _hexPoints = [];
    
    // Reference to the central registry
    private CelestialBodyRegistry _celestialRegistry => CelestialBodyRegistry.GetInstance();
    
    // Pulsation effect for selected hexagons
    private float _pulsationTime = 0.0f;
    private float _pulsationSpeed = 2.0f; // Speed of pulsation
    private float _pulsationIntensity = 0.2f; // Intensity of pulsation effect
    private Color _glowColor = new Color(1.0f, 1.0f, 1.0f, 0.5f); // Glow color for selected hexes
    
    public override void _Ready()
    {
        // Set node name for easier debugging
        var hexPos = HexUtils.WorldToAxial(Position);
        HexPosition = new HexCoordinates(hexPos.Q, hexPos.R); 
        Name = $"Hex({HexPosition})";
        
        // Initialize the hex points
        InitializeHexPoints();
        
        // Initialize other properties
        IsSelected = false;
        
        // Set up visibility notifier
        _visibilityNotifier = new VisibleOnScreenNotifier2D();
        // Calculate the bounding box of the hex points
        Rect2 hexBounds = CalculateHexBoundingBox();
        _visibilityNotifier.Rect = hexBounds;

        AddChild(_visibilityNotifier);
        _visibilityNotifier.ScreenEntered += _OnHexScreenEntered;
        _visibilityNotifier.ScreenExited += _OnHexScreenExited;
        
        // Initial check: If not on screen initially, disable processing
        if (!_visibilityNotifier.IsOnScreen())
        {
            _OnHexScreenExited(); // Call exit handler to disable processing immediately
        }
    }
    
    /// <summary>
    /// Called when the node exits the scene tree. Unsubscribes from events to prevent memory leaks.
    /// </summary>
    public override void _ExitTree()
    {
        if (_visibilityNotifier != null)
        {
            _visibilityNotifier.ScreenEntered -= _OnHexScreenEntered;
            _visibilityNotifier.ScreenExited -= _OnHexScreenExited;
        }
    }
    
    /// <summary>
    /// Initialize the hexagon points for drawing and hit testing
    /// </summary>
    private void InitializeHexPoints()
    {
        var hexSize = HexUtils.HEX_SIZE;
        float height = Mathf.Sqrt(3) * hexSize;
        
        _hexPoints = new Vector2[6];
        _hexPoints[0] = new Vector2(hexSize, 0);                 // Right
        _hexPoints[1] = new Vector2(hexSize/2, height/2);        // Bottom right
        _hexPoints[2] = new Vector2(-hexSize/2, height/2);       // Bottom left  
        _hexPoints[3] = new Vector2(-hexSize, 0);                // Left
        _hexPoints[4] = new Vector2(-hexSize/2, -height/2);      // Top left
        _hexPoints[5] = new Vector2(hexSize/2, -height/2);       // Top right
    }
    
    /// <summary>
    /// Calculates the AABB for the hexagon polygon points.
    /// </summary>
    private Rect2 CalculateHexBoundingBox()
    {
        if (_hexPoints.IsEmpty())
        {
            InitializeHexPoints(); // Ensure points are initialized
        }
        
        if (_hexPoints.IsEmpty()) return new Rect2(0, 0, 0, 0); // Should not happen
        
        // Find min/max x and y from the points
        float minX = _hexPoints[0].X;
        float maxX = _hexPoints[0].X;
        float minY = _hexPoints[0].Y;
        float maxY = _hexPoints[0].Y;
        
        for (int i = 1; i < _hexPoints.Length; i++)
        {
            minX = Mathf.Min(minX, _hexPoints[i].X);
            maxX = Mathf.Max(maxX, _hexPoints[i].X);
            minY = Mathf.Min(minY, _hexPoints[i].Y);
            maxY = Mathf.Max(maxY, _hexPoints[i].Y);
        }
        
        // Create the Rect2 (Position is top-left corner, Size is width/height)
        return new Rect2(minX, minY, maxX - minX, maxY - minY);
    }
    
    public override void _Draw()
    {
        DrawHexagon();
    }
    
    private void DrawHexagon()
    {
        // Ensure hex points are initialized
        if (_hexPoints.IsEmpty())
        {
            InitializeHexPoints();
        }
        
        // Get the faction from the contained body if present
        var currentFaction = ContainedBody?.Data?.FactionType ?? FactionType.None;
        
        // Determine fill color based on state and modes
        Color fillColor = _defaultFillColor;
        
        if (_factionHighlightMode && currentFaction != FactionType.None)
        {
            // When faction highlight is enabled, use faction fill color
            fillColor = FactionFillColors[currentFaction];
            
            // Make it more visible when selected
            if (IsSelected)
            {
                fillColor = fillColor.Lightened(0.3f);
            }
        }
        else if (IsSelected)
        {
            fillColor = IsOccupied ? _selectedOccupiedFillColor : _selectedFillColor;
        }
        else if (IsOccupied && _debugHighlightMode)
        {
            fillColor = _occupiedFillColor;
        }
        
        // Draw fill based on active modes
        if (_debugHighlightMode || (_factionHighlightMode && currentFaction != FactionType.None))
        {
            DrawPolygon(_hexPoints, [fillColor]);
        }
        
        // Determine border color based on faction and selection state
        Color borderColor = FactionColors[currentFaction];
        if (IsSelected)
        {
            // Make selected border color use the defined selected color with higher opacity
            borderColor = _selectedBorderColor;
            
            // Apply pulsation effect to selected border
            float pulse = (Mathf.Sin(_pulsationTime) + 1) * 0.5f * _pulsationIntensity;
            borderColor = borderColor.Lightened(pulse);
        }
        
        // Always draw the border regardless of mode
        // Increased line width for selected hexagons from 2.0f to 3.0f for better emphasis
        float lineWidth = IsSelected ? 3.0f : 1.0f;
        
        // Adjust fill and border alpha using the value calculated in UpdateVisualState
        float alphaModulate = 1.0f;
        if (Game.ShowUnrevealedHexesDebug && !_isRevealed && !_isNeighborRevealed)
        {
            alphaModulate = 0.15f; 
        }
        else if (!_isRevealed && _isNeighborRevealed)
        {
            alphaModulate = 0.5f; 
        }
        
        fillColor.A *= alphaModulate;
        borderColor.A *= alphaModulate;
        
        // Draw glow effect for selected hexagons
        if (IsSelected)
        {
            // Create a glow effect by drawing a slightly larger outline with a semitransparent color
            Color glowColor = _glowColor;
            glowColor.A *= alphaModulate;
            
            // Apply the same pulsation to the glow
            float pulse = (Mathf.Sin(_pulsationTime) + 1) * 0.5f * _pulsationIntensity;
            glowColor = glowColor.Lightened(pulse);
            
            // Draw the glow outline slightly larger than the regular border
            for (int i = 0; i < 6; i++)
            {
                // Draw each edge with a thicker, semi-transparent line for the glow effect
                DrawLine(_hexPoints[i], _hexPoints[(i + 1) % 6], glowColor, lineWidth + 2.0f);
            }
        }
        
        // Draw the main border
        for (int i = 0; i < 6; i++)
        {
            // Draw each edge
            DrawLine(_hexPoints[i], _hexPoints[(i + 1) % 6], borderColor, lineWidth);
        }
    }
    
    /// <summary>
    /// Updates the visual state (visibility, transparency) based on the current universe model and debug flags.
    /// </summary>
    public void UpdateVisualState(UniverseModel universeModel)
    {
        var cellData = universeModel.GetCell(HexPosition);
        if (cellData == null) 
        {
            Visible = false; // Should not happen if model is consistent
            return;
        }
        
        _isRevealed = cellData.IsRevealed;
        
        // Check neighbors only if this hex isn't revealed itself
        _isNeighborRevealed = false;
        if (!_isRevealed) 
        {
            List<HexCoordinates> neighbors = HexUtils.GetRing(HexPosition, 1);
            foreach (var neighborCoords in neighbors)
            {
                var neighborCell = universeModel.GetCell(neighborCoords);
                if (neighborCell is { IsRevealed: true })
                {
                    _isNeighborRevealed = true;
                    break;
                }
            }
        }
        
        // Determine visibility based on state and debug flag
        bool shouldBeVisible = _isRevealed || _isNeighborRevealed || Game.ShowUnrevealedHexesDebug;
        Visible = shouldBeVisible;
        
        // Calculate alpha modulation based on reveal state and debug mode
        float alphaModulate = 1.0f;
        if (Game.ShowUnrevealedHexesDebug && !_isRevealed && !_isNeighborRevealed)
        {
            // This hex is only visible because debug mode is on, make it faint
            alphaModulate = 0.15f; 
        }
        else if (!_isRevealed && _isNeighborRevealed)
        {
            // This hex is visible because a neighbor is revealed, make it slightly dimmed
            alphaModulate = 0.5f; 
        }
        
        // Update contained body visibility and transparency
        if (ContainedBody != null)
        {
            // Body is visible if the hex is visible
            ContainedBody.Visible = shouldBeVisible;
            
            // Apply the same alpha modulation to the body
            Color currentModulate = ContainedBody.Modulate;
            currentModulate.A = alphaModulate;
            ContainedBody.Modulate = currentModulate;
        }
        
        QueueRedraw(); // Update visuals
    }
    
    /// <summary>
    /// Set whether this hexagon is selected
    /// </summary>
    public void SetSelected(bool selected)
    {
        IsSelected = selected;
        if (selected)
        {
            UniverseEvents.RaiseHexagonSelected(this);
        }
        QueueRedraw();
    }
    
    /// <summary>
    /// Set debug highlight mode
    /// </summary>
    public void SetDebugHighlightMode(bool enabled)
    {
        _debugHighlightMode = enabled;
        QueueRedraw();
    }
    
    /// <summary>
    /// Set faction highlight mode
    /// </summary>
    public void SetFactionHighlightMode(bool enabled)
    {
        _factionHighlightMode = enabled;
        QueueRedraw();
    }
    
    /// <summary>
    /// Set the celestial body contained in this hexagon
    /// </summary>
    public void SetContainedBody(CelestialBody? body)
    {
        ContainedBody = body;
        body?.RemoveFromParent();
        
        if (body != null)
        {
            AddChild(body);
        }
        
        // Update the registry
        if (body != null)
        {
            _celestialRegistry.RegisterBody(HexPosition, body);
        }
        else
        {
            _celestialRegistry.RemoveBody(HexPosition);
        }
        
        QueueRedraw();
    }
    
    /// <summary>
    /// Gets the celestial body contained in this hexagon.
    /// </summary>
    public CelestialBody? GetContainedBody()
    {
        return ContainedBody;
    }

    // --- Visibility Handling ---

    private void _OnHexScreenEntered()
    {
        // Enable processing for the hexagon itself
        ProcessMode = ProcessModeEnum.Inherit;
        
        // If there's a contained body, make it visible and enable its processing
        if (IsInstanceValid(ContainedBody))
        {
            ContainedBody.Visible = true;
            ContainedBody.ProcessMode = ProcessModeEnum.Inherit; // Inherit processing state from parent (Hexagon)
        }
        
        // GD.Print($"Hex {HexPosition} entered screen");
    }

    private void _OnHexScreenExited()
    {
        // Disable processing for the hexagon itself
        ProcessMode = ProcessModeEnum.Disabled;
        
        // If there's a contained body, hide it and disable its processing
        if (IsInstanceValid(ContainedBody))
        {
            ContainedBody.Visible = false;
            ContainedBody.ProcessMode = ProcessModeEnum.Disabled;
        }
    }

    // Add Process method to update pulsation effect when selected
    public override void _Process(double delta)
    {
        base._Process(delta);
        
        // Only update pulsation time if selected and visible on screen
        if (IsSelected && _visibilityNotifier.IsOnScreen())
        {
            _pulsationTime += (float)delta * _pulsationSpeed;
            if (_pulsationTime > Mathf.Pi * 2)
            {
                _pulsationTime -= Mathf.Pi * 2; // Keep within one cycle
            }
            
            // Force redraw to update pulsation effect
            QueueRedraw();
        }
    }
    
    /// <summary>
    /// Set the colors for this hexagon based on the current contrast and color scheme settings
    /// </summary>
    /// <param name="borderColor">The border color for non-selected state</param>
    /// <param name="fillColor">The fill color for non-selected state</param>
    /// <param name="selectedBorderColor">The border color when selected</param>
    public void SetColors(Color borderColor, Color fillColor, Color selectedBorderColor)
    {
        // Update the default colors
        _defaultBorderColor = borderColor;
        _defaultFillColor = fillColor;
        _selectedBorderColor = selectedBorderColor;
        
        // Update faction colors dictionary to use the new default border color
        FactionColors[FactionType.None] = borderColor;
        
        // Update faction fill colors dictionary to use the new default fill color
        FactionFillColors[FactionType.None] = fillColor;
        
        // Queue redraw to apply the new colors
        QueueRedraw();
    }
}