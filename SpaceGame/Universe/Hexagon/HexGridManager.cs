using Godot;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Helpers;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Hexagon;

public partial class HexGridManager : Node2D
{
    // Using computed property to access HexGrid singleton
    private HexGrid _hexGrid => HexGrid.GetInstance();
    
    // Dictionary to store all created hexagons
    private Dictionary<(int q, int r), Hexagon> _hexagons = new Dictionary<(int q, int r), Hexagon>();
    
    // Hexagon scene to instance
    private PackedScene _hexagonScene;
    
    // Display settings
    private bool _showDebugHighlight = false;
    private bool _showFactionHighlight = true;
    
    // Contrast settings
    public enum ContrastLevel
    {
        Low,
        Medium,
        High
    }
    
    // Current contrast level, defaults to Medium
    private ContrastLevel _contrastLevel = ContrastLevel.High;
    
    // Color scheme options
    public enum ColorScheme
    {
        Default,   // White/Default color scheme
        BlueScale, // Blue-based color scheme for better visibility
        HighContrast // High contrast color scheme for accessibility
    }
    
    // Current color scheme, defaults to Default
    private ColorScheme _colorScheme = ColorScheme.Default;
    
    // Color definitions for different contrast levels and color schemes
    private readonly Dictionary<ContrastLevel, Dictionary<ColorScheme, Color>> _borderColors = new()
    {
        { 
            ContrastLevel.Low, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.3f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.6f, 1.0f, 0.3f) },
                { ColorScheme.HighContrast, new Color(1, 1, 0, 0.4f) }
            }
        },
        { 
            ContrastLevel.Medium, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.5f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.6f, 1.0f, 0.5f) },
                { ColorScheme.HighContrast, new Color(1, 1, 0, 0.6f) }
            }
        },
        { 
            ContrastLevel.High, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.7f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.6f, 1.0f, 0.7f) },
                { ColorScheme.HighContrast, new Color(1, 1, 0, 0.8f) }
            }
        }
    };
    
    // Fill colors for different contrast levels and color schemes
    private readonly Dictionary<ContrastLevel, Dictionary<ColorScheme, Color>> _fillColors = new()
    {
        { 
            ContrastLevel.Low, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.05f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.6f, 1.0f, 0.05f) },
                { ColorScheme.HighContrast, new Color(0.1f, 0.1f, 0.1f, 0.1f) }
            }
        },
        { 
            ContrastLevel.Medium, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.1f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.6f, 1.0f, 0.1f) },
                { ColorScheme.HighContrast, new Color(0.1f, 0.1f, 0.1f, 0.15f) }
            }
        },
        { 
            ContrastLevel.High, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.2f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.6f, 1.0f, 0.2f) },
                { ColorScheme.HighContrast, new Color(0.1f, 0.1f, 0.1f, 0.25f) }
            }
        }
    };
    
    // Selected border colors
    private readonly Dictionary<ContrastLevel, Dictionary<ColorScheme, Color>> _selectedBorderColors = new()
    {
        { 
            ContrastLevel.Low, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.7f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.7f, 1.0f, 0.7f) },
                { ColorScheme.HighContrast, new Color(1, 0.8f, 0, 0.8f) }
            }
        },
        { 
            ContrastLevel.Medium, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 0.9f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.7f, 1.0f, 0.9f) },
                { ColorScheme.HighContrast, new Color(1, 0.8f, 0, 0.9f) }
            }
        },
        { 
            ContrastLevel.High, new Dictionary<ColorScheme, Color>
            {
                { ColorScheme.Default, new Color(1, 1, 1, 1.0f) },
                { ColorScheme.BlueScale, new Color(0.4f, 0.7f, 1.0f, 1.0f) },
                { ColorScheme.HighContrast, new Color(1, 0.8f, 0, 1.0f) }
            }
        }
    };
    
    public override void _Ready()
    {
        // Load the hexagon scene
        _hexagonScene = GD.Load<PackedScene>("res://SpaceGame/Universe/Hexagon/Hexagon.tscn");
        
        if (_hexagonScene == null)
        {
            Logger.Error("Failed to load Hexagon scene");
        }
    }

    /// <summary>
    /// Set the visibility of the hex grid debug highlighting
    /// </summary>
    public void SetDebugHighlightMode(bool enabled)
    {
        _showDebugHighlight = enabled;
        
        // Update existing hexagons
        foreach (var hexagon in _hexagons.Values)
        {
            hexagon.SetDebugHighlightMode(enabled);
        }
    }
    
    /// <summary>
    /// Set the visibility of faction highlighting
    /// </summary>
    public void SetFactionHighlightMode(bool enabled)
    {
        _showFactionHighlight = enabled;
        
        // Update existing hexagons
        foreach (var hexagon in _hexagons.Values)
        {
            hexagon.SetFactionHighlightMode(enabled);
        }
    }
    
    /// <summary>
    /// Set the contrast level for the hex grid
    /// </summary>
    public void SetContrastLevel(ContrastLevel level)
    {
        _contrastLevel = level;
        UpdateHexVisualSettings();
    }
    
    /// <summary>
    /// Set the color scheme for the hex grid
    /// </summary>
    public void SetColorScheme(ColorScheme scheme)
    {
        _colorScheme = scheme;
        UpdateHexVisualSettings();
    }
    
    /// <summary>
    /// Update the visual settings of all hexagons based on current contrast and color scheme
    /// </summary>
    private void UpdateHexVisualSettings()
    {
        // Get the colors for the current settings
        Color borderColor = _borderColors[_contrastLevel][_colorScheme];
        Color fillColor = _fillColors[_contrastLevel][_colorScheme];
        Color selectedBorderColor = _selectedBorderColors[_contrastLevel][_colorScheme];
        
        // Apply to all hexagons
        foreach (var hexagon in _hexagons.Values)
        {
            hexagon.SetColors(borderColor, fillColor, selectedBorderColor);
            hexagon.QueueRedraw(); // Force redraw to apply changes immediately
        }
        
        Logger.Debug($"Updated hex grid visuals - Contrast: {_contrastLevel}, Scheme: {_colorScheme}");
    }
    
    /// <summary>
    /// Cycle through available contrast levels (Low -> Medium -> High -> Low)
    /// </summary>
    public void CycleContrastLevel()
    {
        _contrastLevel = _contrastLevel switch
        {
            ContrastLevel.Low => ContrastLevel.Medium,
            ContrastLevel.Medium => ContrastLevel.High,
            ContrastLevel.High => ContrastLevel.Low,
            _ => ContrastLevel.Medium
        };
        
        UpdateHexVisualSettings();
        Logger.Debug($"Cycled hex grid contrast to: {_contrastLevel}");
    }
    
    /// <summary>
    /// Cycle through available color schemes (Default -> BlueScale -> HighContrast -> Default)
    /// </summary>
    public void CycleColorScheme()
    {
        _colorScheme = _colorScheme switch
        {
            ColorScheme.Default => ColorScheme.BlueScale,
            ColorScheme.BlueScale => ColorScheme.HighContrast,
            ColorScheme.HighContrast => ColorScheme.Default,
            _ => ColorScheme.Default
        };
        
        UpdateHexVisualSettings();
        Logger.Debug($"Cycled hex grid color scheme to: {_colorScheme}");
    }
    
    /// <summary>
    /// Clear all hexagons, used when regenerating the universe
    /// </summary>
    public void ClearHexagons()
    {
        Logger.Debug($"ClearHexagons");
        foreach (var hexagon in _hexagons.Values)
        {
            hexagon.RemoveAndQueueFreeChildren();
            // Now free the hexagon
            hexagon.QueueFree();
        }
        
        _hexagons.Clear();
    }

    /// <summary>
    /// Get an existing hexagon or create a new one if it doesn't exist
    /// </summary>
    public Hexagon GetOrCreateHexagon(HexCoordinates pos)
    {
        return GetOrCreateHexagon(pos.Q, pos.R);
    }

    /// <summary>
    /// Get an existing hexagon or create a new one if it doesn't exist
    /// </summary>
    public Hexagon GetOrCreateHexagon(int q, int r)
    {
        if (_hexagons.TryGetValue((q, r), out var hexagon))
        {
            // Update existing hexagon
            hexagon.QueueRedraw(); // Ensure the outline is updated
            return hexagon;
        }
        
        // Create new hexagon
        hexagon = _hexagonScene.Instantiate<Hexagon>();
        if (hexagon == null)
        {
            Logger.Error($"Failed to instantiate hexagon scene");
            return null;
        }

        // Position the hexagon in the world
        Vector2 worldPos = HexUtils.AxialToWorld(q, r);
        hexagon.Position = worldPos;

        hexagon.SetDebugHighlightMode(_showDebugHighlight);
        hexagon.SetFactionHighlightMode(_showFactionHighlight);
        
        // Apply current contrast and color scheme
        Color borderColor = _borderColors[_contrastLevel][_colorScheme];
        Color fillColor = _fillColors[_contrastLevel][_colorScheme];
        Color selectedBorderColor = _selectedBorderColors[_contrastLevel][_colorScheme];
        hexagon.SetColors(borderColor, fillColor, selectedBorderColor);
        
        // Add to scene and dictionary
        AddChild(hexagon);
        _hexagons[(q, r)] = hexagon;
        return hexagon;
    }
    
    /// <summary>
    /// Get the hexagon at the specified coordinates, or null if it doesn't exist
    /// </summary>
    public Hexagon? GetHexagon(HexCoordinates pos)
    {
        return _hexagons.GetValueOrDefault((pos.Q, pos.R));
    }
    
    public bool RefreshHexagon(HexCoordinates pos)
    {
        Hexagon? hex = _hexagons.GetValueOrDefault((pos.Q, pos.R));
        if (hex != null)
        {
            hex.QueueRedraw();
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Find the hexagon that contains the given world position
    /// </summary>
    public Hexagon? FindHexagonAtPoint(Vector2 worldPosition)
    {
        Logger.Debug($"FindHexagonAtPoint {worldPosition}");
        // Convert to axial coordinates
        var hexCoord = HexUtils.WorldToAxial(worldPosition);
        
        // Try to get the hexagon at these coordinates
        return _hexagons.GetValueOrDefault((hexCoord.Q, hexCoord.R));
    }
    
    /// <summary>
    /// Get the hexagon at the specified axial coordinates
    /// </summary>
    public Hexagon? GetHexagonAt(HexCoordinates pos)
    {
        return GetHexagonAt(pos.Q, pos.R);
    }
    
    /// <summary>
    /// Get the hexagon at the specified axial coordinates
    /// </summary>
    public Hexagon? GetHexagonAt(int q, int r)
    {
        return _hexagons.GetValueOrDefault((q, r));
    }
}