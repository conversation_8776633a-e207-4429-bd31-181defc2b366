using System.Collections.Generic;
using System.Linq;
using Godot;
using SpaceGame.Helpers;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Hexagon;

public static class HexUtils
{
    public const float HEX_SIZE = 100.0f;

    /// <summary>
    /// Converts axial coordinates (q,r) to world position (x,y)
    /// </summary>
    public static Vector2 AxialToWorld(HexCoordinates pos)
    {
        return AxialToWorld(pos.Q, pos.R);
    }

    /// <summary>
    /// Converts axial coordinates (q,r) to world position (x,y)
    /// </summary>
    public static Vector2 AxialToWorld(int q, int r)
    {
        // Flat-topped hexagon layout
        float x = HEX_SIZE * (3.0f/2.0f * q);
        float y = HEX_SIZE * (Mathf.Sqrt(3)/2.0f * q + Mathf.Sqrt(3) * r);
        return new Vector2(x, y);
    }
    
    /// <summary>
    /// Converts world position (x,y) to axial coordinates (q,r)
    /// </summary>
    public static HexCoordinates WorldToAxial(Vector2 position)
    {
        // Flat-topped hexagon layout - inverse of AxialToWorld
        float q = 2.0f/3.0f * position.X / HEX_SIZE;
        float r = (-1.0f/3.0f * position.X + Mathf.Sqrt(3)/3.0f * position.Y) / HEX_SIZE;
        
        // We need to round to the nearest hex cell
        return AxialRound(q, r);
    }
    
    /// <summary>
    /// Rounds floating point axial coordinates to the nearest valid hex
    /// </summary>
    public static HexCoordinates AxialRound(float q, float r)
    {
        // Convert to cube coordinates for rounding
        float x = q;
        float z = r;
        float y = -x - z;
        
        // Round cube coordinates
        float rx = Mathf.Round(x);
        float ry = Mathf.Round(y);
        float rz = Mathf.Round(z);
        
        // Fix rounding errors
        float xDiff = Mathf.Abs(rx - x);
        float yDiff = Mathf.Abs(ry - y);
        float zDiff = Mathf.Abs(rz - z);
        
        if (xDiff > yDiff && xDiff > zDiff)
        {
            rx = -ry - rz;
        }
        else if (yDiff > zDiff)
        {
            ry = -rx - rz;
        }
        else
        {
            rz = -rx - ry;
        }
        
        // Convert back to axial coordinates
        return new HexCoordinates((int)rx, (int)rz);
    }

        /// <summary>
    /// Gets all hex coordinates in a ring at the specified radius from a center.
    /// </summary>
    /// <param name="center">The center coordinates</param>
    /// <param name="radius">The radius of the ring</param>
    /// <returns>List of hex coordinates forming the ring</returns>
    public static List<HexCoordinates> GetRing(HexCoordinates center, int radius)
    {
        List<HexCoordinates> results = new List<HexCoordinates>();
        
        if (radius < 0)
        {
            return results;
        }
        
        if (radius == 0)
        {
            results.Add(center); // Just the center
            return results;
        }
        
        // Start at the position that is 'radius' units away in one direction
        HexCoordinates hexPos = new HexCoordinates(center.Q + radius, center.R - radius);
        
        // The six directions to move in a hex grid (clockwise order)
        (int q, int r)[] directions = 
        {
            (0, 1),   // Southeast
            (-1, 1),  // Southwest
            (-1, 0),  // West
            (0, -1),  // Northwest
            (1, -1),  // Northeast
            (1, 0),   // East
        };
        
        // For each side of the ring
        for (int side = 0; side < 6; side++)
        {
            // Move radius steps along this side
            for (int step = 0; step < radius; step++)
            {
                results.Add(hexPos);
                hexPos = new HexCoordinates(hexPos.Q + directions[side].q, hexPos.R + directions[side].r);
            }
        }
        
        return results;
    }
    
    /// <summary>
    /// Gets all hex coordinates within the specified radius from a center (spiral).
    /// </summary>
    /// <param name="center">The center coordinates</param>
    /// <param name="radius">The maximum radius</param>
    /// <returns>List of hex coordinates within the radius</returns>
    public static List<HexCoordinates> GetSpiral(HexCoordinates center, int radius)
    {
        List<HexCoordinates> results = new List<HexCoordinates>();
        
        for (int r = 0; r <= radius; r++)
        {
            results.AddRange(GetRing(center, r));
        }
        
        return results.ToHashSet().ToList();
    }
    
    /// <summary>
    /// Gets the distance between two hex coordinates in the hex grid.
    /// </summary>
    /// <param name="a">First coordinates</param>
    /// <param name="b">Second coordinates</param>
    /// <returns>The distance in hex units</returns>
    public static int Distance(HexCoordinates a, HexCoordinates b)
    {
        return a.DistanceFrom(b);
    }
    
    /// <summary>
    /// Constrains a world position to stay within a hex cell's boundaries.
    /// Useful for ensuring orbital features stay within their parent hex.
    /// </summary>
    /// <param name="position">The world position to constrain</param>
    /// <param name="hexCenter">The center world position of the hex</param>
    /// <param name="margin">Optional margin inside the hex boundary (between 0 and 1)</param>
    /// <returns>The constrained position that is guaranteed to be within the hex</returns>
    public static Vector2 ConstrainToHex(Vector2 position, Vector2 hexCenter, float margin = 0.1f)
    {
        // Calculate the vector from hex center to the position
        Vector2 offset = position - hexCenter;
        
        // Maximum distance from center to a corner of the hex
        float cornerDistance = HEX_SIZE;
        
        // Maximum distance from center to the middle of an edge
        float edgeDistance = HEX_SIZE * 0.866f; // sqrt(3)/2 * HEX_SIZE
        
        // Apply margin
        float maxAllowedDistance = Mathf.Min(cornerDistance, edgeDistance) * (1.0f - margin);
        
        // Get the current distance
        float currentDistance = offset.Length();
        
        // If the position is already within bounds, return it as is
        if (currentDistance <= maxAllowedDistance)
        {
            return position;
        }
        
        // Otherwise, scale the offset to fit within the max allowed distance
        Vector2 constrainedOffset = offset * (maxAllowedDistance / currentDistance);
        
        // Return the constrained position
        return hexCenter + constrainedOffset;
    }
}