using Godot;
using SpaceGame.Helpers;
using SpaceGame.Scripts.Events;
using SpaceGame.Universe.Hexagon;

namespace SpaceGame.Universe.Selection;

/// <summary>
/// Manages selection of both Hexagons and CelestialBodies, keeping them in sync
/// </summary>
public partial class SelectionManager : Node2D
{
    // Current selections
    private Hexagon.Hexagon? _selectedHexagon;
    private CelestialBody? _selectedCelestialBody;
    
    private HexGridManager _hexGridManager;
    
    public override void _Ready()
    {
        // Connect to universe events
        UniverseEvents.CelestialBodySelected += OnCelestialBodySelected;
        
        // Get required references
        _hexGridManager = GetNode<HexGridManager>("%HexGridManager");
        if (_hexGridManager == null)
        {
            Logger.Error("SelectionManager failed to find HexGridManager");
        }
    }
    
    /// <summary>
    /// Called when the node exits the scene tree. Unsubscribes from events to prevent memory leaks.
    /// </summary>
    public override void _ExitTree()
    {
        UniverseEvents.CelestialBodySelected -= OnCelestialBodySelected;
    }
    
    /// <summary>
    /// Handle a celestial body being selected
    /// </summary>
    private void OnCelestialBodySelected(CelestialBody? body)
    {
        SelectCelestialBody(body);
    }
    
    /// <summary>
    /// Select a hexagon and its contained celestial body (if any)
    /// </summary>
    public void SelectHexagon(Hexagon.Hexagon? hexagon)
    {
        // Do not allow selecting unrevealed hexes, unless debug mode is enabled
        if (hexagon != null && !hexagon.Visible && !Game.ShowUnrevealedHexesDebug)
        {
            return; // Ignore click on invisible hex (unless debug override)
        }
        
        // Skip if same hexagon is already selected
        if (_selectedHexagon == hexagon)
            return;
            
        // Deselect current hexagon
        _selectedHexagon?.SetSelected(false);
        
        // Update selected hexagon
        _selectedHexagon = hexagon;
        _selectedHexagon?.SetSelected(true);
        
        // If new hexagon contains a celestial body, select it
        var celestialBody = hexagon?.GetContainedBody();
        
        // Only update celestial body selection if it's different
        if (celestialBody != _selectedCelestialBody)
        {
            // We need to avoid recursion when selections trigger each other
            _selectedCelestialBody?.SetSelected(false);
            _selectedCelestialBody = celestialBody;
            _selectedCelestialBody?.SetSelected(true);
        }
    }
    
    /// <summary>
    /// Select a celestial body and its containing hexagon
    /// </summary>
    public void SelectCelestialBody(CelestialBody? body)
    {
        // Find the hexagon containing this celestial body
        Hexagon.Hexagon? hexagon = null;
        if (body != null)
        {
            // Convert body position to hex position
            var hexPos = HexUtils.WorldToAxial(body.Position);
            // Find the hexagon at this position
            hexagon = _hexGridManager.GetHexagon(hexPos);
        }

        // Do not allow selecting bodies in unrevealed hexes, unless debug mode is enabled
        if (hexagon != null && !hexagon.Visible && !Game.ShowUnrevealedHexesDebug)
        {
            return; // Ignore selection if hex isn't visible (unless debug override)
        }
        
        // Skip if same body is already selected
        if (_selectedCelestialBody == body)
            return;
            
        // Deselect current celestial body
        _selectedCelestialBody?.SetSelected(false);
        
        // Update selected celestial body
        _selectedCelestialBody = body;
        _selectedCelestialBody?.SetSelected(true);
        
        // Only update hexagon selection if it's different
        if (hexagon != _selectedHexagon)
        {
            // We need to avoid recursion when selections trigger each other
            _selectedHexagon?.SetSelected(false);
            _selectedHexagon = hexagon;
            _selectedHexagon?.SetSelected(true);
        }
    }
    
    /// <summary>
    /// Clear all selections
    /// </summary>
    public void ClearSelection()
    {
        // Deselect current hexagon and celestial body
        _selectedHexagon?.SetSelected(false);
        _selectedCelestialBody?.SetSelected(false);
        
        // Reset references
        _selectedHexagon = null;
        _selectedCelestialBody = null;
        
        // Raise events for other systems
        UniverseEvents.RaiseHexagonSelected(null);
        UniverseEvents.RaiseCelestialBodySelected(null);
    }
    
    /// <summary>
    /// Get the currently selected hexagon
    /// </summary>
    public Hexagon.Hexagon? GetSelectedHexagon()
    {
        return _selectedHexagon;
    }
    
    /// <summary>
    /// Get the currently selected celestial body
    /// </summary>
    public CelestialBody? GetSelectedCelestialBody()
    {
        return _selectedCelestialBody;
    }

    // Only handle input here if it hasn't been handled anywhere else
    public override void _UnhandledInput(InputEvent @event)
    {
        if (@event is InputEventMouseButton { Pressed: true, ButtonIndex: MouseButton.Left })
        {
            var globalMouse = GetGlobalMousePosition();
            var coords = HexUtils.WorldToAxial(globalMouse);
            
            // Find hexagon at mouse position
            Hexagon.Hexagon? hexagon = _hexGridManager.GetHexagon(coords);
            
            if (hexagon != null)
            {
                SelectHexagon(hexagon);
            }
        }
    }
}
