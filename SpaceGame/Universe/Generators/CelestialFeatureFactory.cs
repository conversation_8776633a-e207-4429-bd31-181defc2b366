using Godot;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe.Generators;

/// <summary>
/// Factory class for creating various types of celestial features
/// </summary>
public static class CelestialFeatureFactory
{
    // Maximum safe orbit radius to ensure features stay within hex boundaries
    // Based on a slightly smaller distance than the hex edge (0.866 * HEX_SIZE)
    private const float MAX_SAFE_ORBIT_RADIUS = 70.0f; // About 70% of HEX_SIZE

    /// <summary>
    /// Creates a moon feature
    /// </summary>
    public static CelestialFeature CreateMoon(string name, float scale = 0.3f, float orbitRadius = 50f, float orbitSpeed = 0.2f)
    {
        // Ensure orbit radius is within safe bounds
        orbitRadius = Mathf.Min(orbitRadius, MAX_SAFE_ORBIT_RADIUS);
        
        var moon = new CelestialFeature
        {
            Name = name,
            Type = CelestialFeatureType.Moon,
            TexturePath = CelestialBodyTextureManager.GetTexturePathForFeature(CelestialFeatureType.Moon, name),
            Scale = scale,
            OrbitRadius = orbitRadius,
            OrbitSpeed = orbitSpeed,
            RotationSpeed = 0.5f
        };
        
        return moon;
    }
    
    /// <summary>
    /// Creates a ring feature
    /// </summary>
    public static CelestialFeature CreateRings(string name, float scale = 1.0f)
    {
        var rings = new CelestialFeature
        {
            Name = name,
            Type = CelestialFeatureType.Rings,
            TexturePath = CelestialBodyTextureManager.GetTexturePathForFeature(CelestialFeatureType.Rings, name),
            Scale = scale,
        };
        
        return rings;
    }
    
    /// <summary>
    /// Creates a space station feature
    /// </summary>
    public static CelestialFeature CreateStation(string name, float scale = 1.5f, float orbitRadius = 60f, float orbitSpeed = 0.1f)
    {
        // Ensure orbit radius is within safe bounds
        orbitRadius = Mathf.Min(orbitRadius, MAX_SAFE_ORBIT_RADIUS);
        
        var station = new CelestialFeature
        {
            Name = name,
            Type = CelestialFeatureType.Station,
            TexturePath = CelestialBodyTextureManager.GetTexturePathForFeature(CelestialFeatureType.Station, name),
            Scale = scale,
            OrbitRadius = orbitRadius,
            OrbitSpeed = orbitSpeed
        };
        
        return station;
    }
    
    /// <summary>
    /// Creates a dyson sphere feature
    /// </summary>
    public static CelestialFeature CreateDysonSphere(string name, float scale = 1.8f, float orbitRadius = 0.0f)
    {
        var dysonSphere = new CelestialFeature
        {
            Name = name,
            Type = CelestialFeatureType.DysonSphere,
            TexturePath = CelestialBodyTextureManager.GetTexturePathForFeature(CelestialFeatureType.DysonSphere, name),
            Scale = scale,
            RotationSpeed = 0.05f,
            OrbitRadius = orbitRadius
        };
        
        return dysonSphere;
    }
} 