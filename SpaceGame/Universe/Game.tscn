[gd_scene load_steps=7 format=3 uid="uid://b6h7g8j4k5l6m"]

[ext_resource type="Theme" uid="uid://c6udgeioox4qf" path="res://SpaceGame/Assets/SpaceGameTheme.tres" id="1_l1t8f"]
[ext_resource type="Script" uid="uid://fxp0pp6anqgd" path="res://SpaceGame/Universe/Game.cs" id="2_cv1br"]
[ext_resource type="PackedScene" uid="uid://dtueqse7ci5ao" path="res://UniverseRoot.tscn" id="3_xn7og"]
[ext_resource type="Script" uid="uid://cpylgaa6dp236" path="res://SpaceGame/Scripts/UniverseRoot.cs" id="4_cv1br"]
[ext_resource type="Script" uid="uid://crgtnvfrhv0jt" path="res://SpaceGame/Battle/BattleCoordinator.cs" id="6_ab33m"]
[ext_resource type="PackedScene" uid="uid://dqa3vxvu3e1pw" path="res://SpaceGame/Scenes/Battle/SpaceBattleScene.tscn" id="7_hqvtc"]

[node name="Game" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_l1t8f")
script = ExtResource("2_cv1br")

[node name="Universe" parent="." node_paths=PackedStringArray("_battleCoordinator") instance=ExtResource("3_xn7og")]
script = ExtResource("4_cv1br")
_battleCoordinator = NodePath("../BattleRoot/BattleCoordinator")

[node name="BattleRoot" type="Node2D" parent="."]
unique_name_in_owner = true
process_mode = 4

[node name="BattleCoordinator" type="Node" parent="BattleRoot"]
script = ExtResource("6_ab33m")
_spaceBattleScenePrefab = ExtResource("7_hqvtc")
metadata/_custom_type_script = "uid://crgtnvfrhv0jt"

[editable path="Universe"]
