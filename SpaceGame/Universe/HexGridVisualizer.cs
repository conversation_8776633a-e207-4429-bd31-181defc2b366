using Godot;
using System.Collections.Generic;
using System.Linq;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe;

/// <summary>
/// Handles the visualization of the hex grid used for universe generation
/// </summary>
public partial class HexGridVisualizer : Node2D
{
    private float HexSize => HexUtils.HEX_SIZE;
    
    private HexGrid _hexGrid => HexGrid.GetInstance();
    private bool _showHexGrid = false;
    private Camera2D _camera;
    private (int q, int r)? _selectedHexagon;
    
    /// <summary>
    /// Initializes the visualizer with required references
    /// </summary>
    public void Initialize(Camera2D camera)
    {
        _camera = camera;
    }
    
    /// <summary>
    /// Sets whether the hex grid should be shown
    /// </summary>
    public void SetShowHexGrid(bool show)
    {
        _showHexGrid = show;
        QueueRedraw();
    }
    
    public override void _Draw()
    {
        if (!_showHexGrid || _camera == null)
            return;
            
        DrawHexGrid();
    }
    
    private void DrawHexGrid()
    {
        // Get planets to ensure we draw grid for all of them
        var planets = GetTree().GetNodesInGroup("celestial_bodies")
            .OfType<Planet>()
            .ToList();
        
        // Determine the visible area for camera view
        var viewRect = GetViewport().GetVisibleRect();
        var topLeft = _camera.Position - viewRect.Size * _camera.Zoom / 2;
        var bottomRight = _camera.Position + viewRect.Size * _camera.Zoom / 2;
        
        // Convert to hex coordinates
        var hexStart = HexUtils.WorldToAxial(topLeft);
        var hexEnd = HexUtils.WorldToAxial(bottomRight);
        
        // Add some padding
        int padding = 2;
        int minQ = hexStart.Q - padding;
        int maxQ = hexEnd.Q + padding;
        int minR = hexStart.R - padding;
        int maxR = hexEnd.R + padding;
        
        // Expand range to include all planets
        foreach (var planet in planets)
        {
            var hexCoord = HexUtils.WorldToAxial(planet.Position);
            minQ = Mathf.Min(minQ, hexCoord.Q - 1);
            maxQ = Mathf.Max(maxQ, hexCoord.Q + 1);
            minR = Mathf.Min(minR, hexCoord.R - 1);
            maxR = Mathf.Max(maxR, hexCoord.R + 1);
        }
        
        // Draw each visible hex
        for (int q = minQ; q <= maxQ; q++)
        {
            for (int r = minR; r <= maxR; r++)
            {
                DrawHex(q, r);
            }
        }
    }
    
    private void DrawHex(int q, int r)
    {
        Vector2 center = HexUtils.AxialToWorld(q, r);
        
        bool isSelected = _selectedHexagon.HasValue && _selectedHexagon.Value.q == q && _selectedHexagon.Value.r == r;
        bool isOccupied = _hexGrid.IsOccupied(new HexCoordinates(q, r));
        
        Color fillColor;
        if (isSelected)
        {
            fillColor = isOccupied ? new Color(0, 1, 0, 0.4f) : new Color(1, 1, 1, 0.3f);
        }
        else
        {
            fillColor = isOccupied ? new Color(0, 1, 0, 0.2f) : new Color(1, 1, 1, 0.05f);
        }
        
        Color borderColor = isSelected ? new Color(1, 1, 1, 0.7f) : new Color(1, 1, 1, 0.3f);
        
        // For a flat-topped hexagon, we need to calculate the width and height
        float width = 2 * HexSize;                      // Distance from left to right side
        float height = Mathf.Sqrt(3) * HexSize;         // Distance from top to bottom side
        
        // Define the six corners of a flat-topped hexagon
        var points = new Vector2[6];
        points[0] = center + new Vector2(HexSize, 0);                 // Right
        points[1] = center + new Vector2(HexSize/2, height/2);        // Bottom right
        points[2] = center + new Vector2(-HexSize/2, height/2);       // Bottom left  
        points[3] = center + new Vector2(-HexSize, 0);                // Left
        points[4] = center + new Vector2(-HexSize/2, -height/2);      // Top left
        points[5] = center + new Vector2(HexSize/2, -height/2);       // Top right
        
        // Fill the hex
        DrawPolygon(points, new Color[] { fillColor });
        
        // Draw the border
        for (int i = 0; i < 6; i++)
        {
            // Draw each edge
            DrawLine(points[i], points[(i + 1) % 6], borderColor, isSelected ? 2.0f : 1.0f);
        }
    }
}