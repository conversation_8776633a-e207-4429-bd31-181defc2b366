using Godot;
using SpaceGame.Helpers;

using System;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe;

public partial class Planet : CelestialBody
{
    private string _planetId = string.Empty;
    
    /// <summary>
    /// Initializes the planet from a CelestialBodyData object
    /// </summary>
    public override void Initialize(CelestialBodyData data)
    {
        // Use the deterministic ID from the data instead of generating a random one
        _planetId = data.Id;
        base.Initialize(data);
    }
}