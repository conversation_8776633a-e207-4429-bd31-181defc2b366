using System.Collections.Generic;
using SpaceGame.Helpers;
using SpaceGame.Universe.Hexagon;
using SpaceGame.Universe.Model;

namespace SpaceGame.Universe;

/// <summary>
/// Central registry for all celestial bodies in the universe.
/// Ensures that all bodies are tracked in a single place to prevent overlap.
/// Implemented as a singleton to ensure only one instance exists.
/// </summary>
public class CelestialBodyRegistry
{
    // Singleton instance
    private static CelestialBodyRegistry? _instance;
    
    // Dictionary to track celestial bodies by their hex coordinates
    private readonly Dictionary<HexCoordinates, CelestialBody> _celestialBodies = new();
    
    /// <summary>
    /// Get the singleton instance of CelestialBodyRegistry
    /// </summary>
    public static CelestialBodyRegistry GetInstance()
    {
        return _instance ??= new CelestialBodyRegistry();
    }
    
    // Private constructor to enforce singleton pattern
    private CelestialBodyRegistry()
    {
    }
    
    /// <summary>
    /// Registers a celestial body at the specified hex coordinates
    /// </summary>
    /// <param name="q">Q coordinate in axial system</param>
    /// <param name="r">R coordinate in axial system</param>
    /// <param name="body">The celestial body to register</param>
    /// <returns>True if registration succeeded, false if position is already occupied</returns>
    public bool RegisterBody(HexCoordinates pos, CelestialBody body)
    {
        if (IsOccupied(pos) && _celestialBodies[pos] != body)
        {
            Logger.Error($"Cannot register {body.Name} at ({pos}) - position already occupied by {_celestialBodies[pos].Name}");
            return false;
        }
        
        _celestialBodies[pos] = body;
        
        return true;
    }
    
    /// <summary>
    /// Checks if a hex position is occupied by a celestial body
    /// </summary>
    public bool IsOccupied(HexCoordinates pos)
    {
        return _celestialBodies.ContainsKey(pos);
    }
    
    /// <summary>
    /// Removes a celestial body from the registry
    /// </summary>
    public void RemoveBody(HexCoordinates pos)
    {
        _celestialBodies.Remove(pos);
    }
    
    /// <summary>
    /// Gets all registered celestial bodies
    /// </summary>
    public IEnumerable<KeyValuePair<HexCoordinates, CelestialBody>> GetAllBodies()
    {
        return _celestialBodies;
    }
    
    /// <summary>
    /// Clears all registered celestial bodies
    /// </summary>
    public void Clear()
    {
        foreach (var body in _celestialBodies.Values)
        {
            body.RemoveFromParent();
            body.QueueFree();
        }

        _celestialBodies.Clear();
    }
    
    /// <summary>
    /// Gets the count of registered celestial bodies
    /// </summary>
    public int Count => _celestialBodies.Count;
}
