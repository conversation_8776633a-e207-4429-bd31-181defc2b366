[gd_resource type="Resource" script_class="Commander" load_steps=5 format=3 uid="uid://ruvvccxa4un1"]

[ext_resource type="Script" uid="uid://crp1qvpw0bylx" path="res://SpaceGame/Scripts/StatModifierBlueprint.cs" id="1_mod"]
[ext_resource type="Script" uid="uid://ff5f3mjl34xy" path="res://SpaceGame/Scripts/CommanderBlueprint.cs" id="2_cmd"]

[sub_resource type="Resource" id="StatModifier_Bulwark1"]
script = ExtResource("1_mod")
StatToModify = 0
ModType = 1
Value = 0.1
ApplyToAllShips = false
TargetShipType = 4
TargetWeaponSlotID = ""
TargetWeaponType = 0
ApplyToAllWeapons = false
TargetSystemSlotID = ""
TargetSystemType = 0
ApplyToAllSystems = false

[sub_resource type="Resource" id="StatModifier_Bulwark2"]
script = ExtResource("1_mod")
StatToModify = 10
ModType = 1
Value = 0.1
ApplyToAllShips = false
TargetShipType = 4
TargetWeaponSlotID = ""
TargetWeaponType = 0
ApplyToAllWeapons = false
TargetSystemSlotID = ""
TargetSystemType = 0
ApplyToAllSystems = false

[resource]
script = ExtResource("2_cmd")
CommanderName = "Commander Borin 'Bulwark' Stonehelm"
Description = "Focuses on fleet durability and weapon power for larger vessels."
Modifiers = Array[ExtResource("1_mod")]([SubResource("StatModifier_Bulwark1"), SubResource("StatModifier_Bulwark2")])
