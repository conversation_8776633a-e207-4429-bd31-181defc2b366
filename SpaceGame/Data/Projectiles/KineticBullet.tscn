[gd_scene load_steps=10 format=3 uid="uid://b8agdbm2fv8fv"]

[ext_resource type="PackedScene" uid="uid://bmxvu8ucbb6wo" path="res://SpaceGame/Scenes/Battle/ProjectileBlueprint.tscn" id="1_5cj6o"]
[ext_resource type="Texture2D" uid="uid://b6s5jup4o3ms3" path="res://SpaceGame/Assets/Factions/Federation/Projectiles/Kla'ed - Bullet.png" id="2_ypldc"]
[ext_resource type="PackedScene" uid="uid://c5ggbsqvmqjdv" path="res://SpaceGame/Scenes/Battle/Behaviors/StraightMovementBehavior.tscn" id="3_ypldc"]

[sub_resource type="AtlasTexture" id="AtlasTexture_uu7pn"]
atlas = ExtResource("2_ypldc")
region = Rect2(0, 0, 4, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_ogqis"]
atlas = ExtResource("2_ypldc")
region = Rect2(4, 0, 4, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_lqjy8"]
atlas = ExtResource("2_ypldc")
region = Rect2(8, 0, 4, 16)

[sub_resource type="AtlasTexture" id="AtlasTexture_axr2t"]
atlas = ExtResource("2_ypldc")
region = Rect2(12, 0, 4, 16)

[sub_resource type="SpriteFrames" id="SpriteFrames_c0r2q"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_uu7pn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ogqis")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lqjy8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_axr2t")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_5cj6o"]
radius = 1.0
height = 8.0

[node name="KineticBullet" instance=ExtResource("1_5cj6o")]

[node name="ProjectileSprite" parent="." index="0"]
sprite_frames = SubResource("SpriteFrames_c0r2q")
autoplay = "default"
frame_progress = 0.962015

[node name="CollisionShape2D" parent="HitboxArea" index="0"]
shape = SubResource("CapsuleShape2D_5cj6o")

[node name="StraightMovementBehavior" parent="Behaviors" index="0" instance=ExtResource("3_ypldc")]
SpeedMultiplier = 0.2
