[gd_resource type="Resource" script_class="WeaponData" load_steps=5 format=3 uid="uid://pw8ybtj5aybr"]

[ext_resource type="Resource" uid="uid://ckimsiqbfe7ce" path="res://SpaceGame/Data/Projectiles/KineticProjectileData.tres" id="1_gk313"]
[ext_resource type="Script" uid="uid://bo3g6irisaa78" path="res://SpaceGame/Scripts/Battle/WeaponData.cs" id="1_s0cu3"]
[ext_resource type="PackedScene" uid="uid://b8agdbm2fv8fv" path="res://SpaceGame/Data/Projectiles/KineticBullet.tscn" id="2_e6tkf"]
[ext_resource type="PackedScene" uid="uid://bk0ws65c5es8u" path="res://SpaceGame/Data/Weapons/EmptyWeaponScene.tscn" id="3_npt5k"]

[resource]
script = ExtResource("1_s0cu3")
Id = "KineticCannonMk1"
WeaponName = "Kinetic Cannon MK I"
Type = 1
Damage = 10.0
FireRate = 3.0
ProjectileSpeed = 500.0
Range = 1000.0
WeaponScene = ExtResource("3_npt5k")
ProjectileScene = ExtResource("2_e6tkf")
ProjectileDefinition = ExtResource("1_gk313")
BehaviorSettings = {}
metadata/_custom_type_script = "uid://bo3g6irisaa78"
