[gd_resource type="Resource" script_class="FleetCompositionBonus" load_steps=4 format=3 uid="uid://fv7xxw05181w"]

[ext_resource type="Script" uid="uid://crp1qvpw0bylx" path="res://SpaceGame/Scripts/StatModifierBlueprint.cs" id="1_mod"]
[ext_resource type="Script" path="res://SpaceGame/Scripts/FleetCompositionBonus.cs" id="2_bonus"]

[sub_resource type="Resource" id="StatModifier_CombinedBonus1"]
script = ExtResource("1_mod")
StatToModify = 12
ModType = 0
Value = 100.0
ApplyToAllShips = false
TargetShipType = 2
TargetWeaponSlotID = ""
TargetWeaponType = 0
ApplyToAllWeapons = true
TargetSystemSlotID = ""
TargetSystemType = 0
ApplyToAllSystems = false

[resource]
script = ExtResource("2_bonus")
BonusName = "Combined Arms Support"
Description = "Presence of large combatants (Cruisers+) enhances Frigate weapon range."
RequiredType = 4
MinCount = 1
Modifiers = Array[ExtResource("1_mod")]([SubResource("StatModifier_CombinedBonus1")])
