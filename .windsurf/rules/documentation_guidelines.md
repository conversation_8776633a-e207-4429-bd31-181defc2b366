---
trigger: always_on
---

# Documentation Guidelines for `/docs`

The `/docs` directory provides rapid understanding of project structure, system interactions, and data flow through visual aids with minimal text.

## Core Principles

- **Visuals First**: Use diagrams (component, sequence, state, data flow) to illustrate relationships. Text is supplementary.
- **Extreme Conciseness**: Direct, to-the-point text only.
- **Focus on Interactions**: Show system collaborations clearly.
- **Single-Sentence Class Descriptors**: Class responsibilities should be obvious from context or described in one brief sentence only when necessary.
- **Structural Overview**: Enable quick understanding of architecture and connections.

## Document Types

**System Interaction Diagrams**
- Diagram showing components and data flow for specific features
- Minimal caption (1-2 sentences) explaining purpose

**Architectural Overviews**
- High-level diagram of project layers/modules and responsibilities
- Brief explanation of architectural patterns

**Data Model Summaries**
- Diagrams of key data structures and relationships
- Brief notes on critical fields only when not self-explanatory

## `/docs/state/` Guidelines

- Define structure of shared state objects
- Include: one-sentence purpose description and property list/diagram with types
- Focus on what data is stored, not update mechanics

## Exclusions

- Detailed API descriptions (use code comments instead)
- Lengthy prose explanations
- Implementation details or how-to guides
- Information already clear from code

---
*Success metric: Can someone understand core concepts in under 2 minutes primarily from visuals?*
