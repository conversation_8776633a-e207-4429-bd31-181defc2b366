---
trigger: always_on
---

# Godot C# Game Development Rules

**Core Practices for Godot C# Projects**

**Architecture & Patterns**
- Use Godot's built-in systems (physics, animation, UI)
- Implement state machines for behaviors and game flow
- Design with modular, reusable components
- Use C# events for decoupling systems
- Apply object pooling for frequently created objects
- Prefer data-driven design with `Resource` files

**Performance**
- Profile with Godot tools and `SimpleProfiler`
- Minimize GC in `_Process`/`_PhysicsProcess` loops:
  - Cache node references
  - Avoid string/collection creation
  - Use structs judiciously
- Use `VisibleOnScreenNotifier2D/3D` for off-screen objects
- Choose efficient data structures (Arrays vs Lists, Dictionary for lookups)

**Error Handling**
- Verify null references and resource paths before use
- Use logging levels appropriately (Debug, Info, Warning, Error)
- Provide clear user feedback for errors

**Structure**
- Organize by feature directories
- Use namespaces mirroring directory structure

**Clean Code**
- Write for clarity, consistency, and maintainability
- Document complex decisions
- Optimize without sacrificing readability
- Handle edge cases gracefully
- Design for testability
- Verify with `dotnet build` before completion

---
*This document is a living guide and may be updated as the project evolves.*
