---
trigger: always_on
---

# Memory MCP Usage for Codebase Information

This document outlines how to effectively use the memory MCP server to store and retrieve information about the codebase structure, relationships, and implementations.

## Query Memory First

**CRITICAL RULE**: Always query the memory system first before performing any file searches or code exploration. This is mandatory for any information about:

- File locations and paths
- Class hierarchies and inheritance
- Component relationships and dependencies
- System architectures and integration points
- Implementation patterns and conventions
- Interface implementations

Only resort to direct codebase searches when the memory system does not contain the needed information.

## Relation Types

These relation types should be used to represent relationships between code elements:

- **Extends**: For inheritance relationships (e.g., "PlayerShip Extends Ship")
- **Implements**: For interface implementation (e.g., "Ship Implements IDamageable")
- **Contains**: For composition relationships (e.g., "ShipScene Contains WeaponMount")
- **Uses**: For dependencies or usages (e.g., "BattleManager Uses ShipFactory")
- **Manages**: For control relationships (e.g., "BattleCoordinator Manages BattleManager")
- **ConfiguredBy**: For configuration relationships (e.g., "Ship ConfiguredBy ShipData")
- **RelatesTo**: For conceptual or loose relationships
- **PartOf**: For system/subsystem relationships

## Information to Capture

When storing code information, focus on:

1. **File Location**: Exact path to the file
2. **Key Relationships**: Connections to other code elements
3. **Implementation Details**: Key methods and properties

## Memory Maintenance

- Update information when code changes significantly
- Add new entities for new components
- Remove obsolete information
- Maintain consistency with existing knowledge

## Integration with Documentation

The memory system complements the documentation in `/docs`:
- Memory: implementation details and technical relationships
- Documentation: high-level architecture and conceptual models

---
*This document is a living guide and may be updated as the project evolves.*
