# AI Tools Usage Rules

This document outlines specific rules for using available tools when working with this codebase.

## Code Examples and Documentation

- **Always use Context7 MCP server first** when looking up code examples or documentation
- Context7 contains more recent information than other documentation sources
- Use `mcp0_resolve-library-id` followed by `mcp0_get-library-docs` for obtaining accurate, up-to-date documentation

## Running the Game

- **Always compile the code first** before attempting to run the game. Run `dotnet build` using your own tools, not JetBrains MCP. 
- Verify the build succeeded before proceeding
- **Never** attempt to find or execute the Godot editor executable directly
- **Always** run the game using the `Player` configuration via JetBrains MCP with a **maximum timeout of 30 seconds**:
  ```
  mcp1_run_configuration with configName="Player"
  ```
  - The run configuration will automatically terminate after 30 seconds
  - If you need to terminate earlier, you can check the output after an appropriate time
- To run the game for a specific duration (less than 30 seconds), follow this pattern:
  1. Run the Player configuration
  2. Wait for the desired duration using `mcp1_wait`
  3. Read terminal output after execution

## File Search Strategy

When looking for files in the codebase:

1. **First check memory system** for file location information
2. If not available in memory, use JetBrains MCP tools in this order:
   - `mcp1_find_files_by_name_substring` to locate files by name
   - `mcp1_list_directory_tree_in_folder` to explore project structure
   - `mcp1_search_in_files_content` for content-based searches
3. Only resort to generic search tools if JetBrains MCP methods are unsuccessful
 
## File Content Retrieval Strategy

1. Ensure you have the correct path for a file, otherwise retrieve it first
2. Then, attempt to retrieve file content by yourself
3. If that fails, use `mcp1_get_file_text_by_path`

---
*This document is a living guide and may be updated as the project evolves.*
