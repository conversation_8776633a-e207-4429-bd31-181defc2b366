---
trigger: always_on
---

# Godot C# General Rules

C# coding standards for Godot projects. See `godot_csharp_godot_specific_rules.md` and `godot_csharp_game_development_rules.md` for more.

## Naming & Structure
- `PascalCase`: Classes, interfaces (prefix with `I`), enums, methods, properties, events, constants
- `camelCase`: Local variables, parameters
- Files: One class per file, name matches class, max 500 lines
- Braces: Allman style (new lines)
- Line length: ≤120 characters

## Code Quality
- SOLID: Follow SRP, OCP, LSP, ISP, DIP principles
- Encapsulation: Properties over public fields
- Immutability: Use `readonly` where appropriate
- Typing: Strong typing (enums > strings), use `var` when obvious
- Modern C#: Pattern matching, null operators (`?.`), expression bodies
- LINQ: For readability; mind performance in hot paths
- Async: `async`/`await` for I/O; avoid `async void`
- Errors: `try-catch`, log with `Logger`

## Best Practices
- Comments: Document complex logic only
- Unused code: Delete, don't comment out
- Dependencies: Verified Nuget packages only
- JSON: Use `[JsonPropertyName]` only when needed
- Events: `EventHandler<TEventArgs>` for static events
- Imports: Alphabetize, remove unused, `Logger` in `GlobalUsings.cs`

## Default Standard
For undefined aspects: [MS C# Coding Conventions](https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions)

---
*This document is a living guide and may be updated as the project evolves.*
