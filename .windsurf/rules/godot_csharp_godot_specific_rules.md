---
trigger: always_on
---

# Godot C# Godot-Specific Rules

Essential Godot-specific C# guidelines (complements `godot_csharp_general_rules.md` and `godot_csharp_game_development_rules.md`).

## Guidelines

- **Godot.Object Subclasses**: Declare all Node/Resource subclasses as `partial` with public parameterless constructors.

- **Exports**: Use `[Export]` to expose properties in the editor. Export typed node references (`[Export] public Sprite2D MySprite;`), never `NodePath`.

- **Node References**:
    - Cache references in `_Ready()` with `GetNode<T>(path)`
    - Never use string-based lookups in `_Process`/`_PhysicsProcess`
    - Prefer scene files (`.tscn`) over code for node configuration

- **Input**: Use Input Map (`Input.IsActionPressed()`, `Input.GetVector()`). No hardcoded keycodes.

- **Events**: Use static C# events for decoupling. Always unsubscribe in `_ExitTree()`.

- **Base Class Selection**:
    - `Node`: Processing without position
    - `Node2D`/`Node3D`: When position needed
    - `Resource`: Data containers (add `[GlobalClass]` for editor visibility)

- **Scene Instantiation**: Use `ResourceLoader.Load<PackedScene>("res://path.tscn").Instantiate<NodeType>()`

- **Avoid Redundancy**: Don't override methods that only call `base`

- **Singletons**: Use C# static singletons instead of Godot Autoloads:
    - Implement static `Instance` property
    - Add `Reset()` method for state clearing
    - Only use Autoloads when absolutely necessary (register in Project Settings)

---
*This document is a living guide and may be updated as the project evolves.*