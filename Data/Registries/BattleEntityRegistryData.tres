[gd_resource type="Resource" script_class="EntityRegistryData" load_steps=12 format=3 uid="uid://bvxwg85j631a7"]

[ext_resource type="Resource" path="res://SpaceGame/Resources/AI/DefaultBehaviors/GenericDefaultBehavior.tres" id="1_8g8o0"]
[ext_resource type="Script" uid="uid://bayh67f23fjhj" path="res://SpaceGame/Scripts/Battle/Registry/EntityRegistryData.cs" id="1_t2qwo"]
[ext_resource type="PackedScene" uid="uid://cwnj86jml15wo" path="res://SpaceGame/Assets/Factions/Federation/Fighter/Fighter.tscn" id="1_xj27p"]
[ext_resource type="PackedScene" uid="uid://di5n85lsgqn7" path="res://SpaceGame/Assets/Factions/Federation/Scout/Scout.tscn" id="2_18pw0"]
[ext_resource type="Resource" path="res://SpaceGame/Resources/Battle/AI/CirclerBehavior.tres" id="2_37y6m"]
[ext_resource type="Script" uid="uid://ch1yu4rnxsmer" path="res://SpaceGame/Battle/AI/EnemyBehaviorConfiguration.cs" id="2_nh1ey"]
[ext_resource type="Resource" uid="uid://pw8ybtj5aybr" path="res://SpaceGame/Data/Weapons/KineticCannon.tres" id="3_jt6xb"]
[ext_resource type="Script" uid="uid://bo3g6irisaa78" path="res://SpaceGame/Scripts/Battle/WeaponData.cs" id="3_mqk4r"]
[ext_resource type="Resource" path="res://SpaceGame/Resources/Battle/AI/InterceptorBehavior.tres" id="3_nh1ey"]
[ext_resource type="Resource" uid="uid://ddma25ejviasu" path="res://SpaceGame/Resources/AI/DefaultBehaviors/FighterDefaultBehavior.tres" id="4_gtuac"]
[ext_resource type="Resource" uid="uid://cclvtb167swbl" path="res://SpaceGame/Resources/AI/DefaultBehaviors/ScoutDefaultBehavior.tres" id="5_77vi8"]

[resource]
script = ExtResource("1_t2qwo")
EditorRegisteredShipEntities = Dictionary[String, PackedScene]({
"fighter": ExtResource("1_xj27p"),
"scout": ExtResource("2_18pw0")
})
EditorRegisteredWeaponData = Dictionary[String, ExtResource("3_mqk4r")]({
"KineticCannonMk1": ExtResource("3_jt6xb")
})
EditorRegisteredBehaviors = Dictionary[String, ExtResource("2_nh1ey")]({
"circler": ExtResource("2_37y6m"),
"fighter": ExtResource("4_gtuac"),
"interceptor": ExtResource("3_nh1ey"),
"scout": ExtResource("5_77vi8")
})
DefaultBehavior = ExtResource("1_8g8o0")
