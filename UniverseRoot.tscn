[gd_scene load_steps=6 format=3 uid="uid://dtueqse7ci5ao"]

[ext_resource type="Texture2D" uid="uid://cqvv01t5f3ecf" path="res://SpaceGame/Assets/Backgrounds/pixelart_starfield_diagonal_diffraction_spikes.png" id="1_26g6f"]
[ext_resource type="PackedScene" uid="uid://cxqlq45lojvxu" path="res://SpaceGame/Universe/Hexagon/HexGridManager.tscn" id="2_ekscn"]
[ext_resource type="Script" uid="uid://boyck8c5xve48" path="res://SpaceGame/Universe/Selection/SelectionManager.cs" id="3_1odad"]
[ext_resource type="Theme" uid="uid://c6udgeioox4qf" path="res://SpaceGame/Assets/SpaceGameTheme.tres" id="4_norbg"]
[ext_resource type="PackedScene" uid="uid://clpmrpypk5svs" path="res://SpaceGame/UI/MiniMap/MiniMap.tscn" id="5_wvopa"]

[node name="Universe" type="Node2D"]

[node name="ParallaxBackground" type="Parallax2D" parent="."]
repeat_size = Vector2(320, 320)
repeat_times = 20

[node name="BackgroundSprite" type="Sprite2D" parent="ParallaxBackground"]
texture = ExtResource("1_26g6f")
centered = false
offset = Vector2(240, 160)

[node name="Camera" type="Camera2D" parent="."]
zoom = Vector2(0.4, 0.4)
position_smoothing_enabled = true

[node name="HexGridManager" parent="." instance=ExtResource("2_ekscn")]
unique_name_in_owner = true
physics_interpolation_mode = 2

[node name="SelectionManager" type="Node2D" parent="."]
unique_name_in_owner = true
script = ExtResource("3_1odad")

[node name="GameUI" type="CanvasLayer" parent="."]

[node name="TopRightContainer" type="VBoxContainer" parent="GameUI"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -205.0
offset_bottom = 239.0
grow_horizontal = 0
size_flags_horizontal = 8
size_flags_vertical = 2
alignment = 1

[node name="ButtonContainer" type="HBoxContainer" parent="GameUI/TopRightContainer"]
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 0
alignment = 2

[node name="StartBattleButton" type="Button" parent="GameUI/TopRightContainer/ButtonContainer"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("4_norbg")
text = "Start Battle"

[node name="ShowMinimapButton" type="Button" parent="GameUI/TopRightContainer/ButtonContainer"]
unique_name_in_owner = true
layout_mode = 2
theme = ExtResource("4_norbg")
text = "Toggle Map"

[node name="MiniMap" parent="GameUI/TopRightContainer" instance=ExtResource("5_wvopa")]
unique_name_in_owner = true
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
