# Implementation Plan: Node-Based Projectile Behavior System

This plan outlines the steps needed to implement a flexible, node-based projectile behavior system. Each projectile will be able to have multiple behaviors attached as child nodes, allowing for easy behavior composition and extension.

## Task 1: Create Base Behavior Framework

- [ ] **Create the ProjectileBehavior Base Class**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/ProjectileBehavior.cs`
  - [ ] Implement abstract partial class inheriting from `Node`
  - [ ] Add abstract method `UpdateBehavior(ProjectileEntity projectile, double delta)`
  - [ ] Add documentation comments for the class

- [ ] **Modify ProjectileEntity to Process Behaviors**
  - [ ] Update `/SpaceGame/Scripts/Battle/Entities/ProjectileEntity.cs`
  - [ ] Add property for Direction (Vector2)
  - [ ] Modify _PhysicsProcess to iterate through child ProjectileBehavior nodes and call UpdateBehavior on each
  - [ ] Update the existing velocity and movement code to use the new system
  - [ ] Add helper methods to get/set direction and speed

## Task 2: Implement Basic Behaviors

- [ ] **Create StraightMovementBehavior**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/StraightMovementBehavior.cs`
  - [ ] Implement class inheriting from ProjectileBehavior
  - [ ] Add constructor and necessary properties
  - [ ] Implement UpdateBehavior to move projectile in a straight line based on direction and speed

- [ ] **Create HomingBehavior**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/HomingBehavior.cs`
  - [ ] Implement class inheriting from ProjectileBehavior
  - [ ] Add [Export] property for Target (Node2D)
  - [ ] Add [Export] property for turning speed/rate
  - [ ] Implement UpdateBehavior to adjust direction toward target
  - [ ] Add method to set target from code

- [ ] **Create SpinningBehavior (optional extra)**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/SpinningBehavior.cs`
  - [ ] Implement class inheriting from ProjectileBehavior
  - [ ] Add [Export] properties for rotation speed
  - [ ] Implement UpdateBehavior to rotate the projectile

## Task 3: Update Projectile Scene Templates

- [ ] **Update ProjectileBlueprint.tscn**
  - [ ] Open `/SpaceGame/Scenes/Battle/ProjectileBlueprint.tscn`
  - [ ] Ensure it has the updated ProjectileEntity.cs attached
  - [ ] Add example behavior nodes as children (disabled by default)

- [ ] **Create Example Projectile Scenes**
  - [ ] Create `/SpaceGame/Scenes/Battle/Projectiles/BasicKineticProjectile.tscn`
    - [ ] Inherit from ProjectileBlueprint.tscn
    - [ ] Add and enable StraightMovementBehavior
  - [ ] Create `/SpaceGame/Scenes/Battle/Projectiles/HomingMissile.tscn`
    - [ ] Inherit from ProjectileBlueprint.tscn
    - [ ] Add and enable HomingBehavior

## Task 4: Update Weapon and Factory Integration

- [ ] **Update BattleEntityFactory**
  - [ ] Modify `/SpaceGame/Scripts/Battle/BattleEntityFactory.cs`
  - [ ] Update SpawnProjectileFromWeaponData to handle behavior initialization
  - [ ] Add method to configure behaviors after instantiation

- [ ] **Update WeaponData**
  - [ ] Modify `/SpaceGame/Scripts/Battle/WeaponData.cs`
  - [ ] Add property for default behavior settings (optional)

## Task 5: Documentation Updates

- [ ] **Update or Create Design Document**
  - [ ] Create `/docs/projectile_behavior_system.md`
  - [ ] Include a diagram showing the relationship between ProjectileEntity and behaviors
  - [ ] Document the behavior composition pattern

- [ ] **Update State Documentation**
  - [ ] Update `/docs/state/battle_entity_data.md` if needed

## Notes/Considerations:

- [ ] Behaviors can be attached in scene files or added at runtime
- [ ] Ensure behaviors clean up properly when projectiles are destroyed
- [ ] Consider how behaviors will interact with existing projectile collision and area effect systems
- [ ] Design behaviors to be composable (e.g., combining homing + spinning)