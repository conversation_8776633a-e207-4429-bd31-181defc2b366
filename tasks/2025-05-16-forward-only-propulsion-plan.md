# Implementation Plan: Forward-Only Ship Propulsion

## Overview
Currently, ships can move both forward and backward, which doesn't feel appropriate for space vessels. This plan modifies the propulsion system to only allow forward movement and deceleration, preventing ships from actively moving backward while still allowing them to slow down naturally.

## Task 1: Modify Direct Thrust Input Handling
- [ ] Step 1.1: Update `SetDirectThrustInput` method in `BattleEntityMovementController`
  - [ ] Modify the method to clamp input to a range of 0.0 (no thrust) to 1.0 (forward thrust), eliminating negative values
  - [ ] Update method documentation to reflect new input range

## Task 2: Update Physics Processing for Consistent Behavior
- [ ] Step 2.1: Ensure the `_PhysicsProcess` method respects the forward-only movement constraint
  - [ ] Verify that any velocity calculations don't inadvertently allow backward movement
  - [ ] Ensure consistent behavior between direct control and target-based movement

## Task 3: Adjust Deceleration Behavior
- [ ] Step 3.1: Review deceleration behavior to ensure it feels natural without backward thrust
  - [ ] Verify drag and deceleration parameters provide appropriate stopping behavior
  - [ ] Ensure natural slowdown occurs when no thrust is applied

## Task 4: Update Thruster Visualization
- [ ] Step 4.1: Make sure the thruster visualization component correctly handles the forward-only movement
  - [ ] Verify thruster effects only appear during forward thrust
  - [ ] Ensure thruster visuals remain off during deceleration

## Task 5: Verify AI State Machine Compatibility
- [ ] Step 5.1: Validate that AI state machines function properly with forward-only propulsion
  - [ ] Test `FollowingState` behavior to ensure ships can still follow targets effectively
  - [ ] Verify `WanderingState` behavior continues to function properly
  - [ ] Test `ProtectingState` to ensure combat positioning still works correctly
  - [ ] Check any other AI behaviors that might be affected by the removal of backward movement

## Task 6: Update Input Handling Documentation
- [ ] Step 6.1: Update any related input documentation to clarify the forward-only movement controls
  - [ ] Ensure any player-facing instructions reflect the new movement model

## Documentation Updates
- [ ] Update or create design documents in `/docs/` describing the forward-only propulsion system
  - [ ] Update relevant movement system documentation to reflect the new restriction
  - [ ] Include a brief explanation of the design decision (realism, gameplay balance, etc.)
  - [ ] Document any changes to expected behavior for both player-controlled and AI-controlled entities

## Notes/Considerations:
- [ ] While the AI state machines don't directly use backward movement, their effectiveness may be affected by the change
- [ ] Players will need to adapt to the new movement system, which may require rebalancing speed and maneuverability parameters
- [ ] Consider adding stronger rotation capabilities to compensate for the lack of backward movement
- [ ] If ships need to back away from threats, they'll now have to rotate and move forward instead
