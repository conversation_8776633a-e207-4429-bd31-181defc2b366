# Implementation Plan: Player Fleet Composition

## Phase 1: Define Data Structures

-   [ ] **Create `FleetShipData.cs` (e.g., in `Game/State/Data/` or `Game/Data/Models/`)**
    *   This class will represent an individual ship within the fleet, intended for JSON serialization.
    *   It should be a standard C# public class.
    *   Add a public parameterless constructor.
    *   Define the following public properties:
        *   `public string EntityId { get; set; }` - The ID used to look up the ship in `BattleEntityRegistry`.
        *   `public Godot.Vector2 RelativePosition { get; set; }` - Position relative to the flagship.
        *   `public System.Collections.Generic.Dictionary<string, object> PropertyOverrides { get; set; }` - To store property names and their overridden values. Initialize to `new Dictionary<string, object>()`. (Using `object` for `Variant`'s flexibility in JSON, specific types might be used if known).

-   [ ] **Create `PlayerFleetComposition.cs` (e.g., in `Game/State/Data/` or `Game/Data/Models/`)**
    *   This class will represent the player's entire fleet, intended for JSON serialization.
    *   It should be a standard C# public class.
    *   Add a public parameterless constructor.
    *   Define the following public properties:
        *   `public FleetShipData Flagship { get; set; }` - Data for the player's flagship.
        *   `public System.Collections.Generic.List<FleetShipData> EscortShips { get; set; }` - A list of data for other ships in the fleet. Initialize to `new List<FleetShipData>()`.

## Phase 2: Integrate into GameState

-   [ ] **Modify `Game/State/GameState.cs`**
    *   Ensure `GameState.cs` is a `partial class` and has a public parameterless constructor (as per Godot C# rules for `Godot.Object` derivatives if it isn't already).
    *   Add a new public property for the player's fleet. This property will be populated from JSON deserialization.
        *   `public PlayerFleetComposition PlayerFleet { get; set; }`
    *   The `[Export]` attribute is removed from `PlayerFleet` as `PlayerFleetComposition` is no longer a `Resource`. `GameState` itself might still be a `Resource`, and `PlayerFleet` would be populated/serialized through custom logic or a JSON serialization library when `GameState` is loaded/saved.
    *   Initialize `PlayerFleet` (e.g., `PlayerFleet = new PlayerFleetComposition();` in the constructor or as a default value).

## Phase 3: Implement Override Application Logic (Conceptual)

*   This phase outlines where changes would occur to use the new fleet composition data. Actual implementation will be part of a separate task execution.
-   [ ] **Design/Update Ship Spawning Logic**
    *   Identify or create the system responsible for spawning player ships at the start of a battle (e.g., within `BattleManager.cs` or a new `FleetDeploymentService.cs`).
    *   This system will:
        1.  Read the `PlayerFleet` data from `GameState.cs`.
        2.  For the `Flagship` and each `FleetShipData` in `EscortShips`:
            *   Instantiate the base ship scene/entity using `EntityId` via `BattleEntityRegistry.cs`.
            *   Apply the `RelativePosition` (for escorts) to the instantiated ship relative to the flagship's spawn point.
            *   Iterate through `PropertyOverrides` in `FleetShipData`. For each key-value pair, apply the override to the corresponding property on the instantiated ship. This might involve:
                *   Reflection (e.g., `shipInstance.Set(propertyName, propertyValue)`).
                *   A component-based approach where ships have components that can accept overrides.
                *   An `IConfigurableFromDictionary` interface on ship scripts.

## Phase 4: Documentation Updates

-   [ ] **Update `docs/state/game_state_overview.md` (or create if non-existent)**
    *   Add a section describing the `PlayerFleet` property in `GameState.cs` and its purpose.
    *   Reference `player_fleet_composition_state.md` for details.

-   [ ] **Create `docs/state/player_fleet_composition_state.md`**
    *   Describe the structure of `PlayerFleetComposition.cs` and `FleetShipData.cs`.
    *   Explain each property, its type, and its role (e.g., `EntityId` links to `BattleEntityRegistry`, `RelativePosition` for escorts, `PropertyOverrides` for customization).
    *   Adhere to `documentation_guidelines.md`.

-   [ ] **Create `docs/player_fleet_system.md`**
    *   Provide a high-level overview of the player fleet composition feature.
    *   Explain how `PlayerFleetComposition` data is used to spawn the player's fleet.
    *   Detail how `PropertyOverrides` are intended to be applied to ships.
    *   Include a simple diagram if it helps clarify interactions (e.g., `GameState` -> `PlayerFleetComposition` -> `FleetShipData` -> `BattleEntityRegistry` & Ship Instance).
    *   Adhere to `documentation_guidelines.md`.

## Notes/Considerations:
-   [ ] Ensure all new C# classes follow project coding conventions (`godot_csharp_*.md` rules), including namespaces, `partial` keyword for Godot object derivatives (where applicable, not for these JSON DTOs), and parameterless constructors.
-   [ ] `PropertyOverrides` uses `string` keys for property names and `object` for values. This requires careful management to avoid typos for keys and proper type handling/casting for values during application. Consider using constants for keys if possible.
-   [ ] The actual mechanism for applying `PropertyOverrides` (reflection, interfaces, etc.) will need careful design for robustness and performance.
