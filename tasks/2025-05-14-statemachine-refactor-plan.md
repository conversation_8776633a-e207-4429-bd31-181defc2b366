# Implementation Plan: State Machine System Refactor

This plan outlines the steps to refactor the state machine system for more modularity, strong typing, and improved control flow.

## I. Core State Machine Infrastructure

- [ ] **1. Create `StateBase.cs`**
  - Path: `Game/Scripts/AI/StateMachine/StateBase.cs`
  - Content:
    ```csharp
    using Godot;
    using System;

    namespace SpaceGame.Scripts.AI.StateMachine
    {
        public abstract partial class StateBase<TStateMachineContext, TStateKey> : Node
            where TStateKey : Enum
            where TStateMachineContext : Node // Assuming context is at least a Node
        {
            protected TStateMachineContext Context { get; private set; }

            public StateBase() { }

            public virtual void InitializeState(TStateMachineContext context)
            {
                Context = context;
            }

            public abstract void Enter();
            public abstract void Exit();
            public abstract void Update(double delta);
            public abstract void PhysicsUpdate(double delta);
            public abstract TStateKey? GetNextStateKey(); // For state-driven transitions
        }
    }
    ```

- [ ] **2. Create `StateMachine.cs`**
  - Path: `Game/Scripts/AI/StateMachine/StateMachine.cs`
  - Content:
    ```csharp
    using Godot;
    using System;
    using System.Collections.Generic;
    
    namespace SpaceGame.Scripts.AI.StateMachine
    {
        public abstract partial class StateMachine<TOwnerController, TStateKey, TSelfContext> : Node
            where TStateKey : Enum
            where TOwnerController : Node // The component that owns/directs this state machine
            where TSelfContext : StateMachine<TOwnerController, TStateKey, TSelfContext> // Self-referential context for states
        {
            protected TOwnerController OwnerController { get; private set; }
            protected StateBase<TSelfContext, TStateKey> CurrentState { get; private set; }
            protected Dictionary<TStateKey, StateBase<TSelfContext, TStateKey>> States { get; } = new Dictionary<TStateKey, StateBase<TSelfContext, TStateKey>>();
            private TStateKey _currentStateKey;

            public StateMachine() { }

            public virtual void Initialize(TOwnerController ownerController)
            {
                OwnerController = ownerController;
                RegisterStates();
                if (States.Count == 0)
                {
                    Logger.Warning($"{GetType().Name}: No states registered.");
                    return;
                }
                EnterState(InitialStateKey);
            }

            protected abstract void RegisterStates();
            protected abstract TStateKey InitialStateKey { get; }

            public virtual void EnterState(TStateKey newStateKey)
            {
                if (!States.TryGetValue(newStateKey, out var stateInstance))
                {
                    Logger.Error($"{GetType().Name}: State {newStateKey} not found in registered states.");
                    return;
                }

                CurrentState?.Exit();
                CurrentState = stateInstance;
                _currentStateKey = newStateKey;
                CurrentState.Enter();
                Logger.Debug($"{GetType().Name} entered state {newStateKey}");
            }

            public override void _Process(double delta)
            {
                if (CurrentState == null) return;

                CurrentState.Update(delta);
                TStateKey? nextStateKey = CurrentState.GetNextStateKey();
                if (nextStateKey.HasValue && !nextStateKey.Value.Equals(_currentStateKey))
                {
                    EnterState(nextStateKey.Value);
                }
            }

            public override void _PhysicsProcess(double delta)
            {
                if (CurrentState == null) return;
                CurrentState.PhysicsUpdate(delta);
                // Physics-based transitions could also be handled here if GetNextStateKey is too slow for _Process
            }

            public TStateKey GetCurrentStateKey()
            {
                return _currentStateKey;
            }
            
            // Helper to add states during RegisterStates
            protected void AddState(TStateKey key, StateBase<TSelfContext, TStateKey> stateInstance)
            {
                stateInstance.InitializeState((TSelfContext)this);
                AddChild(stateInstance); // Add state as child for Godot lifecycle management if needed
                States[key] = stateInstance;
            }
        }
    }
    ```

## II. Escort Behavior Implementation

- [ ] **1. Define `EscortStateKey.cs`**
  - Path: `Game/Scripts/AI/Escort/EscortStateKey.cs`
  - Content:
    ```csharp
    namespace SpaceGame.Scripts.AI.Escort
    {
        public enum EscortStateKey
        {
            Protecting,
            Wandering,
            TargetLost, // Example if escort target becomes invalid
            AttackingThreat // Example state for when protecting implies combat
        }
    }
    ```

- [ ] **2. Create `EscortStateBase.cs`**
  - Path: `Game/Scripts/AI/Escort/States/EscortStateBase.cs`
  - Content:
    ```csharp
    using SpaceGame.Scripts.AI.StateMachine;

    namespace SpaceGame.Scripts.AI.Escort.States
    {
        // EscortStateMachine will be the TStateMachineContext
        public abstract partial class EscortStateBase : StateBase<EscortStateMachine, EscortStateKey>
        {
            // EscortStateMachine is already passed as context by StateMachine.AddState
            public EscortStateBase() : base() { }
        }
    }
    ```

- [ ] **3. Create `ProtectingState.cs`**
  - Path: `Game/Scripts/AI/Escort/States/ProtectingState.cs`
  - Implement `Enter, Exit, Update, PhysicsUpdate, GetNextStateKey`.
  - In `Update`, check if `Context.EscortedEntity` is valid. If not, communicate to `Context.OwnerController` (which is `EscortBehaviorComponent`) by calling a method like `HandleEscortedEntityLost()`.

- [ ] **4. Create `WanderingState.cs`**
  - Path: `Game/Scripts/AI/Escort/States/WanderingState.cs`
  - Implement `Enter, Exit, Update, PhysicsUpdate, GetNextStateKey` for wandering behavior.

- [ ] **5. Create `EscortStateMachine.cs`**
  - Path: `Game/Scripts/AI/Escort/EscortStateMachine.cs`
  - Content (structure):
    ```csharp
    using Godot;
    using SpaceGame.Scripts.AI.StateMachine;
    using SpaceGame.Scripts.AI.Escort.States;
    // Assuming EscortBehaviorComponent is in SpaceGame.Scripts.AI.Escort

    namespace SpaceGame.Scripts.AI.Escort
    {
        public partial class EscortStateMachine : StateMachine<EscortBehaviorComponent, EscortStateKey, EscortStateMachine>
        {
            [Export] public Node2D EscortedEntity { get; set; }
            // Other properties like detection range, threat assessment parameters etc.

            public EscortStateMachine() : base() { }

            protected override EscortStateKey InitialStateKey => EscortStateKey.Protecting; // Or make configurable

            protected override void RegisterStates()
            {
                AddState(EscortStateKey.Protecting, new ProtectingState());
                AddState(EscortStateKey.Wandering, new WanderingState());
                // Add other escort-specific states here
            }

            // Example method called by a state
            public void NotifyEscortTargetLost()
            {
                OwnerController.HandleEscortTargetLost(); 
            }
        }
    }
    ```

## III. Controller Components

- [ ] **1. Update `StateMachineControlComponent.cs`**
  - Path: `Game/Scripts/AI/Components/StateMachineControlComponent.cs`
  - This component resides on an entity (e.g., a Ship node).
  - It manages different types of state machines (e.g., `EscortStateMachine`, `AttackStateMachine`), which are added as its children.
  - Content (structure - adapt if updating existing):
    ```csharp
    using Godot;
    using System;
    using System.Collections.Generic;
    using SpaceGame.Scripts.Utility;

    namespace SpaceGame.Scripts.AI.Components
    {
        public partial class StateMachineControlComponent : Node
        {
            private Dictionary<Type, Node> _registeredStateMachines = new Dictionary<Type, Node>();
            // Node is the base for StateMachine<,,,>

            public StateMachineControlComponent() { }

            public T GetStateMachine<T>() where T : Node
            {
                if (_registeredStateMachines.TryGetValue(typeof(T), out var sm))
                {
                    return (T)sm;
                }
                Logger.Warning($"{Name}: State machine of type {typeof(T).Name} not found.");
                return null;
            }

            public void RegisterStateMachine<T>(T stateMachineInstance) where T : Node
            {
                if (stateMachineInstance == null)
                {
                    Logger.Error($"{Name}: Attempted to register a null state machine.");
                    return;
                }
                var type = typeof(T);
                if (_registeredStateMachines.ContainsKey(type))
                {
                    Logger.Warning($"{Name}: State machine of type {type.Name} already registered. Overwriting.");
                    // Consider cleanup of old instance if necessary (e.g., RemoveChild, QueueFree)
                    if (_registeredStateMachines.TryGetValue(type, out var oldSm) && oldSm != null && IsInstanceValid(oldSm))
                    {
                         oldSm.QueueFree(); // Or RemoveChild(oldSm) if ownership is different
                    }
                }
                _registeredStateMachines[type] = stateMachineInstance;
                AddChild(stateMachineInstance); // Manage SM as a child node
                // By default, a new SM is not active; activate explicitly.
                stateMachineInstance.ProcessMode = ProcessModeEnum.Disabled;
                Logger.Debug($"{Name}: Registered state machine: {type.Name} ({stateMachineInstance.Name})");
            }

            public void ActivateStateMachine<T>() where T : Node
            {
                var type = typeof(T);
                if (_registeredStateMachines.TryGetValue(type, out Node sm))
                {
                    sm.ProcessMode = ProcessModeEnum.Inherit; // Or Always, Pausable as appropriate
                    Logger.Debug($"{Name}: Activated state machine: {type.Name} ({sm.Name})");
                }
                else
                {
                    Logger.Warning($"{Name}: Attempt to activate unregistered state machine: {type.Name}");
                }
            }

            public void DeactivateStateMachine<T>() where T : Node
            {
                var type = typeof(T);
                if (_registeredStateMachines.TryGetValue(type, out Node sm))
                {
                    sm.ProcessMode = ProcessModeEnum.Disabled;
                    Logger.Debug($"{Name}: Deactivated state machine: {type.Name} ({sm.Name})");
                }
                 else
                {
                    Logger.Warning($"{Name}: Attempt to deactivate unregistered state machine: {type.Name}");
                }
            }
            
            public bool IsStateMachineActive<T>() where T : Node
            {
                var type = typeof(T);
                if (_registeredStateMachines.TryGetValue(type, out Node sm))
                {
                    return sm.ProcessMode != ProcessModeEnum.Disabled;
                }
                return false;
            }

            // Optional: Method to retrieve or create a specific state machine if not present
            public T GetOrCreateStateMachine<T>(Func<T> factory) where T : Node
            {
                var sm = GetStateMachine<T>();
                if (sm == null)
                {
                    sm = factory();
                    RegisterStateMachine(sm);
                }
                return sm;
            }
        }
    }
    ```

- [ ] **2. Create `EscortBehaviorComponent.cs` (Specific Escort Logic Handler)**
  - Path: `Game/Scripts/AI/Escort/EscortBehaviorComponent.cs`
  - This component is added to entities requiring escort behavior. It owns and configures the `EscortStateMachine` and registers it with `StateMachineControlComponent`.
  - Content (structure):
    ```csharp
    using Godot;
    using SpaceGame.Scripts.AI.Components; // For StateMachineControlComponent
    using SpaceGame.Scripts.Utility;

    namespace SpaceGame.Scripts.AI.Escort
    {
        public partial class EscortBehaviorComponent : Node
        {
            [Export] public NodePath EscortedEntityPath { get; set; }
            private Node2D _escortedEntity;
            private EscortStateMachine _escortStateMachine;
            private StateMachineControlComponent _stateMachineControlComponent;

            public EscortBehaviorComponent() { }

            public override void _Ready()
            {
                if (EscortedEntityPath == null || EscortedEntityPath.IsEmpty)
                {
                    Logger.Error($"{Name}: EscortedEntityPath is not set.");
                    SetProcess(false); 
                    return;
                }
                _escortedEntity = GetNode<Node2D>(EscortedEntityPath);
                if (_escortedEntity == null)
                {
                    Logger.Error($"{Name}: EscortedEntity not found at path: {EscortedEntityPath}");
                    SetProcess(false); 
                    return;
                }

                // Obtain StateMachineControlComponent, assuming it's on the parent entity
                _stateMachineControlComponent = GetParent().GetNode<StateMachineControlComponent>("StateMachineControlComponent"); 
                if (_stateMachineControlComponent == null)
                {
                    Logger.Error($"{Name}: StateMachineControlComponent not found on parent {GetParent().Name}. This component is required.");
                    SetProcess(false);
                    return;
                }

                _escortStateMachine = new EscortStateMachine { Name = "EscortFSM" };
                _escortStateMachine.EscortedEntity = _escortedEntity;
                _escortStateMachine.Initialize(this); // 'this' (EscortBehaviorComponent) is the OwnerController for EscortStateMachine

                _stateMachineControlComponent.RegisterStateMachine(_escortStateMachine);
                _stateMachineControlComponent.ActivateStateMachine<EscortStateMachine>();
            }

            public void HandleEscortTargetLost()
            {
                Logger.Info($"{Name} ({GetParent().Name}): Escort target {_escortedEntity?.Name} lost/destroyed. Deciding next action.");
                // Example: Switch EscortStateMachine to Wandering state
                if (_escortStateMachine != null && _escortStateMachine.GetCurrentStateKey() != EscortStateKey.Wandering)
                {
                     _escortStateMachine.EnterState(EscortStateKey.Wandering);
                }
                // Alternatively, could deactivate EscortStateMachine via _stateMachineControlComponent
                // and activate a different one if needed, e.g.:
                // _stateMachineControlComponent.DeactivateStateMachine<EscortStateMachine>();
                // _stateMachineControlComponent.ActivateStateMachine<SomeOtherStateMachine>();
            }
            
            // Add other methods to handle events from EscortStateMachine or external game events
        }
    }
    ```

## IV. Refactor Existing Systems

- [ ] **1. Update `StateMachineControlComponent`** to support typed state machine management as defined in Section III. Integrate these new capabilities. Path: `Game/Scripts/AI/Components/StateMachineControlComponent.cs`.
- [ ] **2. Adapt specific behavior logic (like escort, attack) into dedicated components** (e.g., `EscortBehaviorComponent`) that create and register their typed state machines (e.g., `EscortStateMachine`) with the `StateMachineControlComponent`.
- [ ] **3. Ensure entities have `StateMachineControlComponent`**. Add specific behavior components (e.g., `EscortBehaviorComponent`) alongside it. These behavior components will instantiate their respective state machines, which are then registered with (and become children of) `StateMachineControlComponent`.

## V. Documentation Updates

- [ ] **1. Create `/docs/ai/state_machine_system_overview.md`**
  - Include diagrams showing interactions between `StateMachineControlComponent`, behavior-specific components (like `EscortBehaviorComponent`), their corresponding typed StateMachines (e.g., `EscortStateMachine`), and `StateBase` derivatives.
  - Explain roles and how to create new typed state machines and behavior components, and how they integrate with `StateMachineControlComponent`.
- [ ] **2. Update `/docs/ai/escort_ship_ai_behavior.md`**
  - Detail `EscortStateMachine` states, transitions, and parameters.
  - Include a diagram of `EscortStateMachine`'s internal state transitions and its communication with `EscortBehaviorComponent`.
- [ ] **3. Create `/docs/state/state_machine_control_component_state.md`**
  - Purpose: Manages active high-level behavior state machines for an entity. Defines how different FSMs are registered, activated, and stored as children.
  - Key Data: Dictionary of registered state machines (by type), potentially a list of active state machine types.
- [ ] **4. Create `/docs/state/escort_behavior_state.md`**
  - Purpose: Defines the state for an entity's escort-specific behavior logic, managed by `EscortBehaviorComponent` and influencing `EscortStateMachine`.

## Notes/Considerations:

- [ ] All new `Node`-derived classes (`StateMachine`, `StateBase`, `StateMachineControlComponent`, `EscortBehaviorComponent`) are marked `partial` and have parameterless public constructors.
- [ ] Namespace usage should align with project structure (e.g., `SpaceGame.Scripts.AI.Components`, `SpaceGame.Scripts.AI.Escort`). Adjust as necessary.
- [ ] The `OwnerController` for a typed `StateMachine` (e.g., `EscortStateMachine`) is its specific behavior component (e.g., `EscortBehaviorComponent`), allowing direct, typed communication for behavior-specific logic. `StateMachineControlComponent` handles the broader management of multiple FSM types.
