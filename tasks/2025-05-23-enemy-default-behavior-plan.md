# Implementation Plan: Enemy Default Behavior System

This plan outlines how to implement a system that automatically attaches appropriate default behaviors to enemy ships when they spawn during battle.

## Task 1: Extend ShipEntityData to Support Default Behaviors
- [x] Modify `ShipEntityData.cs` to include a direct reference to a default behavior configuration:
  - [x] Add a new property `[Export] public EnemyBehaviorConfiguration DefaultBehavior { get; set; }` to store a reference to the behavior configuration

## Task 2: Create Default Behavior Configurations
- [x] Create a directory for default behavior configurations at `/SpaceGame/Resources/AI/DefaultBehaviors` if it doesn't exist
- [x] Create behavior configuration resources for existing ships:
  - [x] Create `FighterDefaultBehavior.tres` with appropriate configurations for fighter ships
  - [x] Create `ScoutDefaultBehavior.tres` with configurations suitable for scout ships (fast, evasive)
  - [x] Create `GenericDefaultBehavior.tres` as a fallback for any ship type without a specific configuration

## Task 3: Extend EntityRegistryData for Behavior Registration
- [x] Modify `EntityRegistryData.cs` to support behavior configurations:
  - [x] Add a new dictionary property for editor-registered behaviors: 
    ```csharp
    [Export] 
    public Dictionary<string, EnemyBehaviorConfiguration> EditorRegisteredBehaviors { get; set; } = new();
    ```
  - [x] Add a default behavior field that will be used as a fallback:
    ```csharp
    [Export]
    public EnemyBehaviorConfiguration DefaultBehavior { get; set; }
    ```

## Task 4: Extend BattleEntityRegistry for Behavior Retrieval
- [x] Modify `BattleEntityRegistry.cs` to support behavior lookup:
  - [x] Add a new method `GetBehavior(string id)` that will:
    - [x] Check the `EditorRegisteredBehaviors` dictionary in `EditorRegistryData`
    - [x] Return the behavior if found
    - [x] If not found, return the `DefaultBehavior` from `EditorRegistryData`
    - [x] Handle appropriate null checking and logging

## Task 5: Update Existing Ship Entity Data Resources
- [ ] Update existing ship entity data resources to reference their appropriate behavior configurations:
  - [ ] Modify the Fighter ship data resource to reference its default behavior configuration
  - [ ] Modify the Scout ship data resource to reference its default behavior configuration
  
  Note: This step will need to be completed in the Godot editor by assigning the behavior configurations to the ship resources.

## Task 6: Create Behavior Application Extension Method
- [x] Add a new extension method to `BattleEntityFactoryExtensions.cs`:
  - [x] Create `AttachDefaultBehavior(this BattleEntityFactory factory, BaseBattleEntity entity)` method
  - [x] This method will:
    - [x] Check if the entity is a `ShipEntity`
    - [x] If it is, check if its `EntityData` has a direct `DefaultBehavior` reference
    - [x] If a behavior is configured, use it
    - [x] If no behavior is configured, try to get a behavior by the entity's type ID from the registry
    - [x] If still not found, use the `DefaultBehavior` from `EntityRegistryData`
    - [x] Call the existing `AttachBehaviorController` method with the resolved behavior

## Task 7: Modify BattleManager to Apply Default Behaviors
- [x] Modify the `SpawnWave` method in `BattleManager.cs` to attach behaviors to spawned enemy ships:
  - [x] After spawning an enemy entity (`enemyEntity = _entityFactory?.CreateBattleEntityById(...)`), call:
  - [x] `_entityFactory?.AttachDefaultBehavior(enemyEntity)`

## Task 8: Add Support for Behavior Override Files
- [ ] Extend the property override system to support behavior overrides:
  - [ ] Update `PropertyOverrideService.cs` to recognize and apply behavior configuration overrides
  - [ ] Allow behavior configuration to be overridden via JSON override files
  - [ ] This provides flexibility for modifying ship behaviors without changing resources
  
  Note: This step will be implemented in a future update as it requires more extensive changes to the property override system.

## Documentation Updates
- [x] Update or create design documents in `/docs/`:
  - [x] Create `/docs/enemy_behavior_system.md` with diagrams showing:
    - Ship entity to behavior configuration relationship
    - Behavior attachment flow during enemy spawning
    - Registry integration for behavior configurations
  - [x] Update `/docs/battle_entity_design.md` to reference the new default behavior feature

## Notes/Considerations:
- [ ] The `EditorRegisteredBehaviors` dictionary in `EntityRegistryData` allows direct configuration in the Godot editor
- [ ] The `DefaultBehavior` field in `EntityRegistryData` provides a fallback for any ship without a specific behavior
- [ ] No programmatic registration at startup - all behaviors will be configured through the Godot editor
- [ ] This approach simplifies behavior management by centralizing it in the editor-configurable registry
