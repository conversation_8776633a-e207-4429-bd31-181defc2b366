# Implementation Plan: Refactor Entity Spawning to Use Direct IDs

This plan outlines the steps to modify the entity spawning mechanism. Spawning will be triggered by providing a direct string `entityId` to the `BattleEntityFactory`, instead of relying on a `RegistryId` property within the `BattleEntityData` resource.

## Task 1: Modify Data Structure
- [ ] Edit `Game/Scripts/Battle/BattleEntityData.cs`:
  - [ ] Remove the `RegistryId` property (`public string RegistryId { get; set; }`).

## Task 2: Refactor BattleEntityFactory
- [ ] Edit `Game/Scripts/Battle/BattleEntityFactory.cs`:
  - [ ] Remove the `CreatePlayerEntity` method entirely.
  - [ ] Remove the `CreateBattleShip` method entirely.
  - [ ] Remove the `CreateEntityFromData` method entirely.
  - [ ] Modify the `CreateBattleEntityById` method:
    - [ ] Add an `Allegiance allegiance = Allegiance.NeutralForces` parameter.
    - [ ] Inside the method, after successfully creating the `entity`, add the line `entity.CurrentAllegiance = allegiance;` to set the allegiance.
    - [ ] Ensure the method still correctly sets `Position` and `Rotation`.

## Task 3: Update Spawning Call Sites
- [ ] Edit `Game/Scripts/Battle/BattleCoordinator.cs`:
  - [ ] Find the line where `_entityFactory.CreatePlayerEntity` is called (around line 87).
  - [ ] Determine the correct string ID for the player's ship (e.g., `"fighter"` or `"scout"`). This might require inspecting how `shipDetailType` was determined or making an assumption (e.g., default to `"fighter"`). **Clarification might be needed here.** Let's assume `"fighter"` for now.
  - [ ] Replace the old call with `_entityFactory.CreateBattleEntityById("fighter", spawnPosition, 0f, Allegiance.PlayerForces);` (adjust the ID string if necessary).

## Documentation Updates
- [ ] Update `/docs/battle_entity_design.md` or related documents to reflect:
    - Removal of `RegistryId` from `BattleEntityData`.
    - Simplification of `BattleEntityFactory` methods.
    - The new pattern of callers providing the `entityId` directly for spawning.
- [ ] No changes anticipated for `/docs/state/` documentation based on this refactor.

## Notes/Considerations:
- The exact string ID to use for the player ship in `BattleCoordinator` needs confirmation. The plan currently assumes `"fighter"`.
- Other parts of the codebase that might have used the removed factory methods will need to be updated to use `CreateBattleEntityById` directly.
- This refactor centralizes the spawning logic around `CreateBattleEntityById`, making the factory simpler but requiring callers to know the specific entity ID they want to spawn.
