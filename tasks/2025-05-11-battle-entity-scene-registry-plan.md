# Implementation Plan: Battle Entity Scene Registry (Autoload Version)

## Task 1: Create `BattleEntityRegistryData` Resource
- [ ] Create a new C# script `BattleEntityRegistryData.cs` that derives from `Godot.Resource`.
  - [ ] Make it a `partial class`.
  - [ ] Add a public parameterless constructor.
  - [ ] Add the `[GlobalClass]` attribute.
  - [ ] Add an exported property for editor mappings:
    ```csharp
    // BattleEntityRegistryData.cs
    using Godot;
    using Godot.Collections;

    [GlobalClass]
    public partial class BattleEntityRegistryData : Resource
    {
        [Export]
        public Dictionary<string, PackedScene> EditorRegisteredBattleEntities { get; set; } = new Dictionary<string, PackedScene>();

        public BattleEntityRegistryData() {}
    }
    ```

## Task 2: Create `BattleEntityRegistry` Autoload Script
- [ ] Create a new C# script `BattleEntityRegistry.cs` that derives from `Node`. This script will be attached to the Autoload node.
  - [ ] Make it a `partial class`.
  - [ ] Add a public parameterless constructor.
  - [ ] Implement the Singleton pattern:
    ```csharp
    // BattleEntityRegistry.cs
    using Godot;
    using System.Collections.Generic; // For C# Dictionary

    public partial class BattleEntityRegistry : Node
    {
        [Export] // Export the data resource for editor assignment
        public BattleEntityRegistryData EditorRegistryData { get; set; }

        private Dictionary<string, PackedScene> _codeRegisteredBattleEntities = new Dictionary<string, PackedScene>();

        public static BattleEntityRegistry Instance { get; private set; }

        public override void _Ready()
        {
            if (Instance != null)
            {
                 GD.PrintErr("BattleEntityRegistry: Attempted to create multiple instances. Destroying self.");
                 QueueFree();
                 return;
            }
            Instance = this;

            if (EditorRegistryData == null)
            {
                GD.PrintWarn("BattleEntityRegistry: No EditorRegistryData assigned in the editor.");
            }
            else
            {
                GD.Print($"BattleEntityRegistry: Loaded {EditorRegistryData.EditorRegisteredBattleEntities?.Count ?? 0} entities from assigned EditorRegistryData.");
            }
        }

        public override void _ExitTree()
        {
             if (Instance == this)
             {
                 Instance = null;
             }
        }

        // --- Instance Methods (formerly static) ---

        public void RegisterBattleEntity(string id, PackedScene scene)
        {
            if (scene == null)
            {
                GD.PrintErr($"BattleEntityRegistry: Attempted to register a null scene for ID '{id}'.");
                return;
            }
            if (_codeRegisteredBattleEntities.ContainsKey(id))
            {
                GD.Print($"BattleEntityRegistry: Battle Entity with ID '{id}' is already code-registered. Overwriting.");
            }
            _codeRegisteredBattleEntities[id] = scene;
        }

        public void RegisterBattleEntity(string id, string scenePath)
        {
            PackedScene scene = GD.Load<PackedScene>(scenePath);
            if (scene != null)
            {
                RegisterBattleEntity(id, scene);
            }
            else
            {
                GD.PrintErr($"BattleEntityRegistry: Failed to load scene from path '{scenePath}' for Battle Entity ID '{id}'.");
            }
        }

        public PackedScene GetBattleEntityScene(string id)
        {
            if (_codeRegisteredBattleEntities.TryGetValue(id, out PackedScene scene))
            {
                return scene;
            }
            if (EditorRegistryData != null && EditorRegistryData.EditorRegisteredBattleEntities.TryGetValue(id, out PackedScene editorScene))
            {
                return editorScene;
            }
            GD.PrintErr($"BattleEntityRegistry: Battle Entity scene with ID '{id}' not found.");
            return null;
        }

        public bool HasBattleEntity(string id)
        {
            if (_codeRegisteredBattleEntities.ContainsKey(id))
            {
                return true;
            }
            return EditorRegistryData != null && EditorRegistryData.EditorRegisteredBattleEntities.ContainsKey(id);
        }

        public void UnregisterBattleEntity(string id)
        {
            _codeRegisteredBattleEntities.Remove(id);
        }

        public void ClearCodeRegisteredBattleEntities()
        {
            _codeRegisteredBattleEntities.Clear();
        }
    }
    ```

## Task 3: Create `BattleEntityRegistryData` Resource File
- [ ] In the Godot Editor:
  - [ ] Create a new Resource of type `BattleEntityRegistryData`.
  - [ ] Save this resource (e.g., as `res://Data/Registries/BattleEntityRegistryData.tres`).
  - [ ] In the inspector, add some initial ID-to-scene mappings to the `EditorRegisteredBattleEntities` dictionary (e.g., `"player_fighter_mk1"` -> `PlayerFighterMk1.tscn`).

## Task 4: Create and Configure Autoload Scene
- [ ] In the Godot Editor:
  - [ ] Create a new scene.
  - [ ] Add a root node of type `Node`. Name it `BattleEntityRegistry`.
  - [ ] Attach the `BattleEntityRegistry.cs` script to this node.
  - [ ] In the Inspector for the `BattleEntityRegistry` node, assign the `BattleEntityRegistryData.tres` file created in Task 3 to the exported `Editor Registry Data` property.
  - [ ] Save this scene (e.g., as `res://Singletons/BattleEntityRegistry.tscn`).
- [ ] Register the Autoload:
  - [ ] Go to `Project -> Project Settings -> Autoload`.
  - [ ] Select `res://Singletons/BattleEntityRegistry.tscn` for the Path.
  - [ ] Set the Node Name to `BattleEntityRegistry` (matching the node name in the scene).
  - [ ] Ensure the 'Enable' checkbox is checked.
  - [ ] Click 'Add'.

## Task 5: Update `BattleEntityFactory.cs`
- [ ] Modify `BattleEntityFactory.cs` to access the registry via its singleton instance.
  ```csharp
  // Example snippet for BattleEntityFactory.cs
  using Godot;

  public partial class BattleEntityFactory : Node // Or your appropriate base class
  {
      // ... other factory code ...

      public Node2D CreateBattleEntityById(string entityId, Vector2 position) // Or your entity base type
      {
          // Access the singleton instance
          if (BattleEntityRegistry.Instance == null)
          {
                GD.PrintErr("BattleEntityFactory: BattleEntityRegistry instance is not available. Ensure it's loaded.");
                return null;
          }

          PackedScene scene = BattleEntityRegistry.Instance.GetBattleEntityScene(entityId);
          if (scene == null)
          {
              // Error already printed by registry
              return null;
          }

          Node2D instance = scene.Instantiate<Node2D>();
          if (instance != null)
          {
              instance.GlobalPosition = position;
              // Add to scene tree (caller or spawning system responsibility)
              // Perform further initialization
          }
          else
          {
               GD.PrintErr($"BattleEntityFactory: Failed to instantiate scene for entity ID '{entityId}'.");
          }
          return instance;
      }
  }
  ```

## Task 6: (Example) Registering Battle Entity Scenes from Code
- [ ] In a suitable script (e.g., a game initialization script), demonstrate registering a battle entity scene via code using the instance:
  ```csharp
  // In some _Ready() or initialization method, AFTER the registry is ready:
  if (BattleEntityRegistry.Instance != null)
  {
        BattleEntityRegistry.Instance.RegisterBattleEntity("special_boss_alpha", "res://Scenes/Battle/Entities/Bosses/AlphaBoss.tscn");

        PackedScene eliteGuardScene = GD.Load<PackedScene>("res://Scenes/Battle/Entities/Elites/EliteGuard.tscn");
        if (eliteGuardScene != null)
        {
            BattleEntityRegistry.Instance.RegisterBattleEntity("elite_guard_type_A", eliteGuardScene);
        }
  }
  else
  {
      GD.PrintErr("Cannot register code entities: BattleEntityRegistry instance not ready.");
  }
  ```

## Documentation Updates
- [ ] Update or create design documents in `/docs/` to describe the `BattleEntityRegistry` as an Autoload singleton.
  - [ ] Explain `BattleEntityRegistryData.cs` and its assignment to the autoload node via the editor.
  - [ ] Detail the `BattleEntityRegistry.cs` script, its instance methods, and how to access the `Instance`.
  - [ ] Explain the process of setting up the autoload scene (`.tscn`) and registering it in Project Settings.
  - [ ] Provide examples of using `BattleEntityRegistry.Instance` for registration and retrieval.
  - [ ] Update the description of how `BattleEntityFactory` uses the registry.
- [ ] Review `/docs/battle_entity_design.md` and related files for consistency with the autoload approach.
- [ ] Check `/docs/state/` documentation again, as autoloads can sometimes hold state implicitly. Ensure any relevant state management aspects are documented.

## Notes/Considerations:
- [ ] This Autoload approach tightly couples the registry to Godot's scene tree and lifecycle.
- [ ] Ensure code that uses `BattleEntityRegistry.Instance` runs *after* the autoload has been initialized (`_Ready` has completed for the registry). Accessing it too early (e.g., in another node's constructor or `_EnterTree`) might result in `Instance` being null.
- [ ] Code registration (`RegisterBattleEntity`) needs to happen after the Autoload's `_Ready` method has set the `Instance`.
