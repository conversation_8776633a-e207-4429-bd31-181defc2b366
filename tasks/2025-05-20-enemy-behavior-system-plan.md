# Implementation Plan: Component-Based Enemy Behavior System

This plan outlines the steps to implement a modular, component-based enemy behavior system for ships in the space game. The system will use a similar approach to the `ProjectileBehavior` system, allowing different behavior components to be attached to enemy ships.

## Phase 1: Core Behavior Framework

- [ ] **Create Behavior Type Enums**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/EnemyBehaviorTypes.cs`
  - [ ] Implement the following code:
    ```csharp
    using System;
    
    namespace SpaceGame.Scripts.Battle.Entities.Behaviors
    {
        /// <summary>
        /// Defines the available movement behavior types for enemy ships.
        /// </summary>
        public enum MovementBehaviorType
        {
            /// <summary>
            /// Directly moves toward target with basic approach logic.
            /// </summary>
            Intercept,
            
            /// <summary>
            /// Orbits around target at a specified distance.
            /// </summary>
            Circle,
            
            /// <summary>
            /// Moves between predefined or dynamically generated patrol points.
            /// </summary>
            Patrol,
            
            /// <summary>
            /// Moves side to side while facing the target.
            /// </summary>
            Strafe,
            
            /// <summary>
            /// Performs random evasive maneuvers to avoid incoming fire.
            /// </summary>
            Evasive
        }
        
        /// <summary>
        /// Defines the available targeting behavior types for enemy ships.
        /// </summary>
        public enum TargetingBehaviorType
        {
            /// <summary>
            /// Targets the closest enemy ship.
            /// </summary>
            Proximity,
            
            /// <summary>
            /// Targets specific ship types according to a priority list.
            /// </summary>
            Priority,
            
            /// <summary>
            /// Targets the enemy ship with the lowest health.
            /// </summary>
            Weakest,
            
            /// <summary>
            /// Targets the enemy ship with the highest threat level.
            /// </summary>
            Strongest,
            
            /// <summary>
            /// Randomly selects targets within detection range.
            /// </summary>
            Random
        }
        
        /// <summary>
        /// Defines the available weapon behavior types for enemy ships.
        /// </summary>
        public enum WeaponBehaviorType
        {
            /// <summary>
            /// Fires whenever the target is in front of the ship.
            /// </summary>
            Opportunistic,
            
            /// <summary>
            /// Fires at optimal range and angle for maximum effectiveness.
            /// </summary>
            Strategic,
            
            /// <summary>
            /// Fires in bursts with cooldown periods between bursts.
            /// </summary>
            Burst,
            
            /// <summary>
            /// Only fires when hit probability is high to conserve ammunition.
            /// </summary>
            Conservative
        }
    }
    ```

- [ ] **Create Base Behavior Component Classes**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/EnemyBehaviorComponent.cs`
  - [ ] Implement the following code:
    ```csharp
    using Godot;
    using System;
    
    namespace SpaceGame.Scripts.Battle.Entities.Behaviors
    {
        /// <summary>
        /// Abstract base class for all enemy behavior components.
        /// Behaviors are attached as child nodes to the ShipBehaviorController and provide modular functionality.
        /// </summary>
        public abstract partial class EnemyBehaviorComponent : Node
        {
            /// <summary>
            /// Whether this behavior component is currently enabled.
            /// </summary>
            [Export]
            public bool IsEnabled { get; set; } = true;
            
            /// <summary>
            /// Called when the behavior is first initialized.
            /// </summary>
            /// <param name="ship">The ship entity this behavior is controlling</param>
            public abstract void Initialize(ShipEntity ship);
            
            /// <summary>
            /// Called during physics processing to update this behavior.
            /// </summary>
            /// <param name="ship">The ship entity this behavior is controlling</param>
            /// <param name="delta">Time elapsed since the last frame</param>
            public abstract void UpdateBehavior(ShipEntity ship, double delta);
            
            /// <summary>
            /// Default constructor required by Godot
            /// </summary>
            public EnemyBehaviorComponent()
            {
            }
            
            /// <summary>
            /// Called when the node exits the scene tree.
            /// Override this in derived classes to clean up resources and unsubscribe from events.
            /// </summary>
            public override void _ExitTree()
            {
                base._ExitTree();
                // Derived classes should override this to perform cleanup
            }
        }
    }
    ```

  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/MovementBehaviorComponent.cs`
  - [ ] Implement the following code:
    ```csharp
    using Godot;
    using System;
    
    namespace SpaceGame.Scripts.Battle.Entities.Behaviors
    {
        /// <summary>
        /// Abstract base class for all movement behavior components.
        /// Handles ship movement and positioning logic.
        /// </summary>
        public abstract partial class MovementBehaviorComponent : EnemyBehaviorComponent
        {
            /// <summary>
            /// The strength of thrust to apply when moving.
            /// </summary>
            [Export]
            public float ThrustStrength { get; set; } = 1.0f;
            
            /// <summary>
            /// The speed at which the ship rotates.
            /// </summary>
            [Export]
            public float RotationSpeed { get; set; } = 2.0f;
            
            /// <summary>
            /// The type of movement behavior this component implements.
            /// </summary>
            public abstract MovementBehaviorType BehaviorType { get; }
            
            /// <summary>
            /// Rotates the ship to face a specific target position.
            /// </summary>
            /// <param name="ship">The ship to rotate</param>
            /// <param name="targetPosition">The position to face</param>
            /// <param name="delta">Time elapsed since the last frame</param>
            /// <returns>The dot product between the ship's forward vector and the direction to target (-1 to 1)</returns>
            protected float RotateTowardPosition(ShipEntity ship, Vector2 targetPosition, double delta)
            {
                // Calculate direction to target
                var directionToTarget = (targetPosition - ship.GlobalPosition).Normalized();
                
                // Calculate desired rotation
                var targetRotation = Mathf.Atan2(directionToTarget.Y, directionToTarget.X);
                
                // Rotate ship toward target
                ship.Rotation = Mathf.LerpAngle(ship.Rotation, targetRotation, (float)delta * RotationSpeed);
                
                // Calculate dot product to see if we're facing the target
                var forwardVector = new Vector2(Mathf.Cos(ship.Rotation), Mathf.Sin(ship.Rotation));
                return forwardVector.Dot(directionToTarget);
            }
        }
    }
    ```

  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/TargetingBehaviorComponent.cs`
  - [ ] Implement the following code:
    ```csharp
    using Godot;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    
    namespace SpaceGame.Scripts.Battle.Entities.Behaviors
    {
        /// <summary>
        /// Abstract base class for all targeting behavior components.
        /// Handles target selection and tracking.
        /// </summary>
        public abstract partial class TargetingBehaviorComponent : EnemyBehaviorComponent
        {
            /// <summary>
            /// The maximum range at which targets can be detected.
            /// </summary>
            [Export]
            public float DetectionRange { get; set; } = 800f;
            
            /// <summary>
            /// The cooldown time between target switches in seconds.
            /// </summary>
            [Export]
            public float TargetSwitchCooldown { get; set; } = 3.0f;
            
            /// <summary>
            /// The type of targeting behavior this component implements.
            /// </summary>
            public abstract TargetingBehaviorType BehaviorType { get; }
            
            /// <summary>
            /// The current target being tracked.
            /// </summary>
            protected ShipEntity CurrentTarget { get; set; }
            
            /// <summary>
            /// Time elapsed since the last target switch.
            /// </summary>
            protected float TimeSinceLastTargetSwitch { get; set; } = 0f;
            
            /// <summary>
            /// Gets the current target being tracked.
            /// </summary>
            /// <returns>The current target, or null if no target is being tracked</returns>
            public ShipEntity GetCurrentTarget() => CurrentTarget;
            
            /// <summary>
            /// Checks if the current target is still valid.
            /// </summary>
            /// <returns>True if the target is valid, false otherwise</returns>
            protected bool IsTargetValid()
            {
                return CurrentTarget != null && CurrentTarget.IsAlive;
            }
            
            /// <summary>
            /// Gets all potential targets within detection range.
            /// </summary>
            /// <param name="ship">The ship that is looking for targets</param>
            /// <returns>A list of potential targets</returns>
            protected List<ShipEntity> GetPotentialTargets(ShipEntity ship)
            {
                return BattleEntityRegistry.Instance.GetEntitiesOfType<ShipEntity>()
                    .Where(s => s.IsAlive && s.Team != ship.Team && 
                           s.GlobalPosition.DistanceTo(ship.GlobalPosition) < DetectionRange)
                    .ToList();
            }
        }
    }
    ```

  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/WeaponBehaviorComponent.cs`
  - [ ] Implement the following code:
    ```csharp
    using Godot;
    using System;
    
    namespace SpaceGame.Scripts.Battle.Entities.Behaviors
    {
        /// <summary>
        /// Abstract base class for all weapon behavior components.
        /// Handles weapon firing logic.
        /// </summary>
        public abstract partial class WeaponBehaviorComponent : EnemyBehaviorComponent
        {
            /// <summary>
            /// The angle threshold for firing weapons (in radians).
            /// Lower values require more precise aiming.
            /// </summary>
            [Export]
            public float FiringAngleThreshold { get; set; } = 0.2f; // ~11 degrees
            
            /// <summary>
            /// The type of weapon behavior this component implements.
            /// </summary>
            public abstract WeaponBehaviorType BehaviorType { get; }
            
            /// <summary>
            /// Reference to the ship's weapon controller.
            /// </summary>
            protected ShipWeaponController WeaponController { get; set; }
            
            /// <summary>
            /// Calculates whether the target is in front of the ship within the firing angle threshold.
            /// </summary>
            /// <param name="ship">The ship that is firing</param>
            /// <param name="target">The target to check</param>
            /// <returns>True if the target is in the firing arc, false otherwise</returns>
            protected bool IsTargetInFiringArc(ShipEntity ship, ShipEntity target)
            {
                if (target == null) return false;
                
                // Calculate direction to target
                var directionToTarget = (target.GlobalPosition - ship.GlobalPosition).Normalized();
                
                // Calculate dot product to check if target is in front of ship
                var forwardVector = new Vector2(Mathf.Cos(ship.Rotation), Mathf.Sin(ship.Rotation));
                var dotProduct = forwardVector.Dot(directionToTarget);
                
                // Return true if target is roughly in front of the ship
                return dotProduct > (1 - FiringAngleThreshold);
            }
        }
    }
    ```

- [ ] **Create Ship Behavior Controller**
  - [ ] Create file `/SpaceGame/Scripts/Battle/Entities/Behaviors/ShipBehaviorController.cs`
  - [ ] Implement the following code:
    ```csharp
    using Godot;
    using System;
    using SpaceGame.Helpers;
    
    namespace SpaceGame.Scripts.Battle.Entities.Behaviors
    {
        /// <summary>
        /// Controller that manages behavior components for a ship.
        /// This should be attached as a child node to a ShipEntity.
        /// </summary>
        public partial class ShipBehaviorController : Node
        {
            /// <summary>
            /// Reference to the ship this controller is attached to.
            /// </summary>
            private ShipEntity _ship;
            
            /// <summary>
            /// Reference to the movement behavior component.
            /// </summary>
            private MovementBehaviorComponent _movementBehavior;
            
            /// <summary>
            /// Reference to the targeting behavior component.
            /// </summary>
            private TargetingBehaviorComponent _targetingBehavior;
            
            /// <summary>
            /// Reference to the weapon behavior component.
            /// </summary>
            private WeaponBehaviorComponent _weaponBehavior;
            
            /// <summary>
            /// Called when the node enters the scene tree for the first time.
            /// </summary>
            public override void _Ready()
            {
                // Get reference to the parent ship
                _ship = GetParent() as ShipEntity;
                if (_ship == null)
                {
                    Logger.Error("ShipBehaviorController must be a child of a ShipEntity");
                    return;
                }
                
                // Find and initialize behavior components
                foreach (var child in GetChildren())
                {
                    if (child is MovementBehaviorComponent movementBehavior)
                    {
                        _movementBehavior = movementBehavior;
                        _movementBehavior.Initialize(_ship);
                        Logger.Debug($"Initialized {_movementBehavior.BehaviorType} movement behavior for {_ship.Name}");
                    }
                    else if (child is TargetingBehaviorComponent targetingBehavior)
                    {
                        _targetingBehavior = targetingBehavior;
                        _targetingBehavior.Initialize(_ship);
                        Logger.Debug($"Initialized {_targetingBehavior.BehaviorType} targeting behavior for {_ship.Name}");
                    }
                    else if (child is WeaponBehaviorComponent weaponBehavior)
                    {
                        _weaponBehavior = weaponBehavior;
                        _weaponBehavior.Initialize(_ship);
                        Logger.Debug($"Initialized {_weaponBehavior.BehaviorType} weapon behavior for {_ship.Name}");
                    }
                }
            }
            
            /// <summary>
            /// Called during physics processing to update behaviors.
            /// </summary>
            /// <param name="delta">Time elapsed since the last frame</param>
            public override void _PhysicsProcess(double delta)
            {
                if (_ship == null || !_ship.IsAlive) return;
                
                // Update targeting first
                _targetingBehavior?.UpdateBehavior(_ship, delta);
                
                // Then update movement based on targeting
                _movementBehavior?.UpdateBehavior(_ship, delta);
                
                // Finally update weapon control
                _weaponBehavior?.UpdateBehavior(_ship, delta);
            }
            
            /// <summary>
            /// Gets the current target from the targeting behavior.
            /// </summary>
            /// <returns>The current target, or null if no target is being tracked</returns>
            public ShipEntity GetCurrentTarget()
            {
                return _targetingBehavior?.GetCurrentTarget();
            }
        }
    }
    ```