# Implementation Plan: Property Override System (Simplified for BattleEntity)

This plan outlines the steps to implement a system for overriding properties directly on `BattleEntity.cs` instances using JSON files, leveraging the existing `BattleEntityRegistry`.

## Phase 1: Core Services

-   [ ] **Create `PropertyOverrideService.cs`**:
    -   Location: `Game/Scripts/Utility/PropertyOverrideService.cs`
    -   Namespace: `SpaceGame.Utility`
    -   Implement as a `static` utility class.
    -   Include methods to apply overrides using C# reflection (e.g., `ApplyOverrides(GodotObject target, Dictionary<string, object> overrides)`).
    -   Handle common Godot/C# type conversions (primitives, `Vector2/3`, `Color`, enums).
-   [ ] **Create `OverrideManager.cs`**:
    -   Location: `Game/Scripts/Managers/OverrideManager.cs`
    -   Namespace: `SpaceGame.Managers`
    -   Implement as a C# **static singleton** (e.g., `public static OverrideManager Instance { get; private set; }`), **not** a Godot Autoload.
    -   Add a static `InitializeOverrides()` method to be called once at game startup.
    -   `InitializeOverrides()` should:
        -   Scan `res://Data/Overrides/` and `user://overrides/` for `.json` files.
        -   Parse JSONs into a structured format (e.g., `Dictionary<string, Dictionary<string, object>>` mapping `EntityTypeId` **(string ID used in BattleEntityRegistry)** to property overrides).
        -   Handle precedence (user overrides game). Store the final merged overrides.
    -   Provide a method like `ApplyGlobalOverrides(BattleEntity target, string entityTypeId)` that uses `PropertyOverrideService` to apply the stored global overrides **directly to the BattleEntity instance**, keyed by the provided `entityTypeId` string.
    -   Include a `Reset()` method if needed for testing or restarting.
-   [ ] **Call `OverrideManager.InitializeOverrides()`**:
    -   Find the main entry point of the game (e.g., in a `Main.cs` or initial scene script's `_Ready`).
    -   Add a call to `OverrideManager.InitializeOverrides()` early in the game's startup sequence.

## Phase 2: Data Structure & Configuration

-   [ ] **Define JSON Structure**:
    -   Finalize and document the JSON structure. It should focus on a top-level key (e.g., `entity_overrides`) containing objects keyed by `EntityTypeId` **(the same string IDs used in `BattleEntityRegistry.cs`)**. Each `EntityTypeId` object will contain key-value pairs for properties defined directly on `BattleEntity.cs`.
    ```json
    {
      "entity_overrides": {
        "FighterShip": { // This ID must match the one in BattleEntityRegistry
          "MaxHealth": 120,
          "MovementSpeed": 350.0 
        },
        "BomberShip": { // This ID must match the one in BattleEntityRegistry
          "MaxHealth": 250
        }
      }
    }
    ```
-   [ ] **Create Override Directories**:
    -   Ensure `res://Data/Overrides/` exists.
    -   Ensure `user://overrides/` is handled (Godot creates `user://` automatically, but ensure the loading code handles its potential absence gracefully).
-   [ ] **Create Example Override Files**:
    -   Add at least one example `.json` file in `res://Data/Overrides/` demonstrating overrides for properties on `BattleEntity.cs` using a valid `EntityTypeId` from the registry.

## Phase 3: Entity Integration

-   [ ] **Modify `BattleEntity.cs`**:
    -   Add `public string InstanceId { get; private set; }`.
    -   Add `public virtual void Initialize(string instanceIdOverride = null)` method:
        -   Assign `InstanceId = instanceIdOverride ?? Guid.NewGuid().ToString();`.
        -   **Important:** This method **no longer calls** `OverrideManager`. It should only contain logic that needs to run *after* global overrides have already been applied (e.g., setting `CurrentHealth = MaxHealth` based on the potentially overridden `MaxHealth`).
    -   Add `public virtual Dictionary<string, object> GetInstanceDataForSave()`:
        -   Return a dictionary containing only runtime-modified state **defined on BattleEntity.cs** (e.g., `CurrentHealth`, `GlobalPosition`, status effects).
    -   Add `public virtual void ApplyInstanceSaveData(Dictionary<string, object> saveData)`:
        -   Use `PropertyOverrideService.ApplyOverrides(this, saveData)` to apply the loaded instance-specific data.

## Phase 4: Workflow Integration

-   [ ] **Update Instantiation Logic (e.g., `BattleEntityFactory.cs` or wherever `BattleEntityRegistry` is used)**:
    -   When creating an entity:
        1.  Get the `entityTypeId` string (e.g., "FighterShip").
        2.  Use `BattleEntityRegistry` to get the `PackedScene` for the `entityTypeId`.
        3.  Instantiate the scene: `var instance = scene.Instantiate<BattleEntity>();`.
        4.  **Apply global overrides:** `OverrideManager.Instance?.ApplyGlobalOverrides(instance, entityTypeId);`.
        5.  **Perform post-override initialization:** `instance.Initialize();`.
        6.  Return or use the fully initialized `instance`.
-   [ ] **Update Save/Load System (`SaveLoadManager.cs` or equivalent)**:
    -   **Saving**: When saving an entity, retrieve its `InstanceId` (from `entity.InstanceId`). Also retrieve/store the original `entityTypeId` string that was used to create it (this might need to be passed to the entity or stored alongside it when created). Store the `entityTypeId`, `InstanceId`, and the dictionary from `entity.GetInstanceDataForSave()`.
    -   **Loading**: When loading an entity:
        1.  Determine `entityTypeId` and `savedInstanceId` from save data.
        2.  Use `BattleEntityRegistry` to get the `PackedScene` for `entityTypeId`.
        3.  Instantiate the scene: `var instance = scene.Instantiate<BattleEntity>();`.
        4.  **Apply global overrides:** `OverrideManager.Instance?.ApplyGlobalOverrides(instance, entityTypeId);`.
        5.  **Perform post-override initialization:** `instance.Initialize(savedInstanceId);` (pass the saved ID).
        6.  **Apply instance-specific saved data:** `instance.ApplyInstanceSaveData(savedDataDictionary);`.

## Documentation Updates

-   [ ] Update or create design documents in `/docs/` detailing the simplified Property Override System architecture, emphasizing the reliance on `BattleEntityRegistry` IDs and the updated instantiation flow. Ensure adherence to `documentation_guidelines.md`.
-   [ ] Update or create state documentation in `/docs/state/` reflecting that the saved state for an entity must now include its original `entityTypeId` string. Ensure adherence to `documentation_guidelines.md`.

## Notes/Considerations:

-   [ ] Reflection used in `PropertyOverrideService` can have performance costs if used excessively in per-frame updates, but should be acceptable for load/initialization.
-   [ ] Error handling during JSON parsing and property application needs to be robust.
-   [ ] This simplified version does not override properties on attached `Resource` objects or child nodes automatically.
-   [ ] Ensure the `entityTypeId` string used for creation is accessible during the save process for that specific entity instance.
