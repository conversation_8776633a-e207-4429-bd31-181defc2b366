# Implementation Plan: IJsonPopulatable for Property Overrides (V2 Approach)

This plan details the steps to create a new `PropertyOverrideServiceV2` that uses an `IJsonPopulatable` interface. This service will leverage `System.Text.Json` for more robust and type-safe override application, reducing the direct use of reflection for nested objects. The original `PropertyOverrideService` will be phased out later.

## Phase 1: Define the Core Interface and Supporting Structures

- [ ] **Step 1.1: Create `IJsonPopulatable.cs`**
    - [ ] Create a new C# file named `IJsonPopulatable.cs` (e.g., in `SpaceGame/Scripts/Utility/Interfaces/` or a similar appropriate location).
    - [ ] Define the public interface `IJsonPopulatable`:
      ```csharp
      using System.Text.Json;

      public interface IJsonPopulatable
      {
          void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions);
      }
      ```

- [ ] **Step 1.2: Define `JsonSerializerOptions` for `PropertyOverrideServiceV2`**
    - [ ] In the new `PropertyOverrideServiceV2.cs` (to be created in Phase 2), define `DefaultSerializerOptions` (e.g., as `public static readonly`) that will be used by the service and passed to `PopulateFromJson` implementations. These options should include `PropertyNameCaseInsensitive = true`, `AllowTrailingCommas = true`, `ReadCommentHandling = JsonCommentHandling.Skip`, and `JsonStringEnumConverter`.

## Phase 2: Create `PropertyOverrideServiceV2`

- [ ] **Step 2.1: Create `PropertyOverrideServiceV2.cs`**
    - [ ] Create a new C# file named `PropertyOverrideServiceV2.cs` in the `SpaceGame/Scripts/Utility/` directory.
    - [ ] Make it a `public static class PropertyOverrideServiceV2`.

- [ ] **Step 2.2: Implement `ApplyOverrides` Method in `PropertyOverrideServiceV2`**
    - [ ] Create a public static method `ApplyOverrides(GodotObject target, Dictionary<string, object> overrides, JsonSerializerOptions serializerOptions = null)` in `PropertyOverrideServiceV2`.
    - [ ] Use the `serializerOptions` passed in, or default to `PropertyOverrideServiceV2.DefaultSerializerOptions` if null.
    - [ ] For each `(propertyName, propertyValue)` in `overrides`:
        - [ ] Attempt to get the `PropertyInfo` for `propertyName` on the `target` object using reflection.
        - [ ] Get the current value of the property: `currentPropertyValue = propertyInfo.GetValue(target)`.
        - [ ] **If `currentPropertyValue` implements `IJsonPopulatable` AND `propertyValue` is a `JsonElement` (or can be converted if it's a dictionary from initial parsing):**
            - [ ] Cast `currentPropertyValue` to `IJsonPopulatable`.
            - [ ] Call `((IJsonPopulatable)currentPropertyValue).PopulateFromJson((JsonElement)propertyValue, usedSerializerOptions);`.
        - [ ] **Else (if the property is a simple type, or its value does not implement `IJsonPopulatable`):**
            - [ ] Use logic similar to the original service: attempt to deserialize/convert `propertyValue` to the `propertyInfo.PropertyType` (e.g., using `JsonSerializer.Deserialize` if `propertyValue` is a `JsonElement` along with `usedSerializerOptions`, or direct conversion for simple types) and set it using `propertyInfo.SetValue()`. 
            - [ ] This path handles leaf nodes or objects not (yet) participating in the `IJsonPopulatable` interface.

- [ ] **Step 2.3: Implement Collection Handling in `PropertyOverrideServiceV2`**
    - [ ] Create helper methods within `PropertyOverrideServiceV2` if needed (e.g., `ApplyOverridesToCollectionItems`), or integrate logic directly into `ApplyOverrides`.
    - [ ] If a target property is a collection (e.g., `IList`, `Godot.Collections.Array`) and the `propertyValue` from overrides is a `JsonElement` of `JsonValueKind.Array`:
        - [ ] Iterate through the target collection and corresponding `JsonElement` overrides from `propertyValue.EnumerateArray()`.
        - [ ] If a collection item is `IJsonPopulatable`, call `PopulateFromJson` on that item with its corresponding `JsonElement` from the override array.
        - [ ] Decide on and implement a strategy for collections:
            - Full replacement: The override JSON array defines the entire new collection.
            - Partial update by index: Update items at corresponding indices. Add new items if the override array is longer. Optionally remove items if the override array is shorter.

## Phase 3: Implement `IJsonPopulatable` in Key Data Classes

- [ ] **Step 3.1: Identify Candidate Classes**
    - [ ] Review existing data structures that are frequently targets of property overrides (e.g., `EntityData`, `WeaponData`, `ShieldData`, `EngineData`, and other custom `Resource` or `GodotObject` subclasses).
    - [ ] Prioritize classes that have nested objects or complex collections.

- [ ] **Step 3.2: Implement `IJsonPopulatable` for each candidate class**
    - For each identified class (e.g., `ExampleData.cs`):
        - [ ] Modify the class definition to implement `IJsonPopulatable`.
        - [ ] Add `using System.Text.Json;`.
        - [ ] Implement the `PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)` method as detailed in the original plan (handling simple properties, nested `IJsonPopulatable` objects, and collections with chosen strategies).

## Phase 4: Integration and Testing of `PropertyOverrideServiceV2`

- [ ] **Step 4.1: Update Call Sites to Use `PropertyOverrideServiceV2`**
    - [ ] Identify key places where property overrides are applied (e.g., `OverrideManager.ApplyGlobalOverrides`).
    - [ ] Temporarily, or for testing, modify these call sites to use `PropertyOverrideServiceV2.ApplyOverrides` instead of the original service. This might involve conditional logic or a temporary switch.
- [ ] **Step 4.2: Thorough Testing**
    - [ ] Test the new service with various override configurations, including nested objects, lists of simple types, and lists of complex types that implement `IJsonPopulatable`. Verify correct behavior.

## Phase 5: Deprecation and Replacement of Original `PropertyOverrideService`

- [ ] **Step 5.1: Mark Original `PropertyOverrideService` as Obsolete**
    - [ ] Once `PropertyOverrideServiceV2` is confirmed to work correctly and reliably, add `[Obsolete("Use PropertyOverrideServiceV2 instead.")]` attribute to the original `PropertyOverrideService` class and its public methods.
- [ ] **Step 5.2: Full Migration**
    - [ ] Systematically update all remaining call sites in the codebase to use `PropertyOverrideServiceV2`.
- [ ] **Step 5.3: Remove Original `PropertyOverrideService`**
    - [ ] After a suitable deprecation period and confirmation that all call sites are updated, delete `PropertyOverrideService.cs`.

## Phase 6: Documentation Updates

- [ ] **Step 6.1: Update `docs/property_override_system.md`**
    - [ ] Document the new `PropertyOverrideServiceV2` and the `IJsonPopulatable` interface.
    - [ ] Explain the rationale for V2 and the transition plan.
    - [ ] Provide clear examples of how to implement `IJsonPopulatable` and how `PropertyOverrideServiceV2` uses it.
    - [ ] Update diagrams to reflect `PropertyOverrideServiceV2`.
    - [ ] Clearly state if the original `PropertyOverrideService` is deprecated or removed.
- [ ] **Step 6.2: Review `/docs/state/` for Impacts**
    - [ ] Verify if state documentation needs updates regarding how overridden values are determined with the new service.

## Notes/Considerations:

- [ ] **Error Handling**: `PopulateFromJson` implementations and `PropertyOverrideServiceV2` must have robust error handling.
- [ ] **Performance**: Monitor performance, though `System.Text.Json` with reduced reflection should be efficient.
- [ ] **Compatibility**: Aim for `PropertyOverrideServiceV2` to be compatible with existing JSON override file structures.
- [ ] **Instantiation**: Ensure `GodotObject` derived nested `IJsonPopulatable` objects are instantiated before `PopulateFromJson` is called.
