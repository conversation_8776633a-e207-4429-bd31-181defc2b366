# Implementation Plan: Component-Based Enemy Behavior System

This plan outlines the steps to create a modular, component-based enemy behavior system for the game. Enemies will have a `ShipBehaviorController` node, which will manage child behavior component nodes for movement, targeting, and weapon control. Behavior configurations will be data-driven using Godot `Resource` files.

## Phase 1: Define Core Behavior Enums and Base Classes

- [ ] **Create Behavior Type Enums**:
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/BehaviorTypes.cs`
  - [ ] In `BehaviorTypes.cs`, define the following public enums:
    - `MovementBehaviorType` (e.g., `Intercept`, `Circle`, `Patrol`, `Strafe`, `Evasive`)
    - `TargetingBehaviorType` (e.g., `Proximity`, `Priority`, `Weakest`, `Strongest`, `Random`)
    - `WeaponBehaviorType` (e.g., `Opportunistic`, `Strategic`, `Burst`, `Conservative`)

- [ ] **Create Base Behavior Component Classes**:
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/Components/EnemyBehaviorComponent.cs`
    - [ ] Define `public abstract partial class EnemyBehaviorComponent : Node`
    - [ ] Add an `[Export] public bool IsEnabled { get; set; } = true;` property.
    - [ ] Add `public abstract void Initialize(ShipEntity ship);` method.
    - [ ] Add `public abstract void UpdateBehavior(ShipEntity ship, double delta);` method.
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/Components/MovementBehaviorComponent.cs`
    - [ ] Define `public abstract partial class MovementBehaviorComponent : EnemyBehaviorComponent`
    - [ ] Add `[Export] public float ThrustStrength { get; set; } = 1.0f;`
    - [ ] Add `[Export] public float RotationSpeed { get; set; } = 2.0f;`
    - [ ] Add `public abstract MovementBehaviorType BehaviorType { get; }` property.
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/Components/TargetingBehaviorComponent.cs`
    - [ ] Define `public abstract partial class TargetingBehaviorComponent : EnemyBehaviorComponent`
    - [ ] Add `[Export] public float DetectionRange { get; set; } = 800f;`
    - [ ] Add `protected ShipEntity CurrentTarget { get; set; }`
    - [ ] Add `public ShipEntity GetCurrentTarget() => CurrentTarget;`
    - [ ] Add `public abstract TargetingBehaviorType BehaviorType { get; }` property.
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/Components/WeaponBehaviorComponent.cs`
    - [ ] Define `public abstract partial class WeaponBehaviorComponent : EnemyBehaviorComponent`
    - [ ] Add `[Export] public float FiringAngleThreshold { get; set; } = 0.2f;`
    - [ ] Add `public abstract WeaponBehaviorType BehaviorType { get; }` property.

## Phase 2: Implement Ship Behavior Controller

- [ ] **Create `ShipBehaviorController` Script**:
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/ShipBehaviorController.cs`
  - [ ] Define `public partial class ShipBehaviorController : Node`
  - [ ] Implement fields to store references to `_ship`, `_movementBehavior`, `_targetingBehavior`, and `_weaponBehavior`.
  - [ ] In `_Ready()`:
    - [ ] Get the parent `ShipEntity`.
    - [ ] Iterate through child nodes to find and assign the respective behavior components (`MovementBehaviorComponent`, `TargetingBehaviorComponent`, `WeaponBehaviorComponent`).
    - [ ] Call `Initialize(ship)` on each found behavior component.
  - [ ] In `_PhysicsProcess(double delta)`:
    - [ ] If the ship is valid and alive:
      - [ ] Call `UpdateBehavior(ship, delta)` on `_targetingBehavior` (if not null and enabled).
      - [ ] Call `UpdateBehavior(ship, delta)` on `_movementBehavior` (if not null and enabled).
      - [ ] Call `UpdateBehavior(ship, delta)` on `_weaponBehavior` (if not null and enabled).

## Phase 3: Implement Concrete Behavior Components

- [ ] **Implement Movement Behaviors**:
  - [ ] Create `/SpaceGame/Scripts/Battle/AI/Components/Movement/InterceptMovementBehavior.cs`
    - [ ] Inherit from `MovementBehaviorComponent`.
    - [ ] Implement `BehaviorType` property to return `MovementBehaviorType.Intercept`.
    - [ ] Add `[Export] public float DesiredDistance { get; set; } = 300f;`
    - [ ] Implement `Initialize()` to get a reference to the `TargetingBehaviorComponent` from the parent `ShipBehaviorController`.
    - [ ] Implement `UpdateBehavior()` logic to move towards the target provided by the `TargetingBehaviorComponent`, maintaining `DesiredDistance`.
  - [ ] Create `/SpaceGame/Scripts/Battle/AI/Components/Movement/CircleMovementBehavior.cs`
    - [ ] Inherit from `MovementBehaviorComponent`.
    - [ ] Implement `BehaviorType` property to return `MovementBehaviorType.Circle`.
    - [ ] Add `[Export] public float CircleDistance { get; set; } = 400f;`
    - [ ] Add `[Export] public float CircleSpeed { get; set; } = 1f;`
    - [ ] Implement `Initialize()` to get a reference to the `TargetingBehaviorComponent`.
    - [ ] Implement `UpdateBehavior()` logic to circle around the target.
  - [ ] *(Optional)* Create other movement behaviors (e.g., `PatrolMovementBehavior`, `StrafeMovementBehavior`, `EvasiveMovementBehavior`) following the same pattern.

- [ ] **Implement Targeting Behaviors**:
  - [ ] Create `/SpaceGame/Scripts/Battle/AI/Components/Targeting/ProximityTargetingBehavior.cs`
    - [ ] Inherit from `TargetingBehaviorComponent`.
    - [ ] Implement `BehaviorType` property to return `TargetingBehaviorType.Proximity`.
    - [ ] Add `[Export] public float TargetSwitchCooldown { get; set; } = 3.0f;`
    - [ ] Implement `Initialize()`.
    - [ ] Implement `UpdateBehavior()` logic to find and set `CurrentTarget` to the closest hostile entity within `DetectionRange`, respecting `TargetSwitchCooldown`.
  - [ ] Create `/SpaceGame/Scripts/Battle/AI/Components/Targeting/PriorityTargetingBehavior.cs`
    - [ ] Inherit from `TargetingBehaviorComponent`.
    - [ ] Implement `BehaviorType` property to return `TargetingBehaviorType.Priority`.
    - [ ] Add `[Export] public string[] PriorityShipTypes { get; set; } = new string[] { "flagship", "cruiser" };`
    - [ ] Add `[Export] public float TargetSwitchCooldown { get; set; } = 5.0f;`
    - [ ] Implement `Initialize()`.
    - [ ] Implement `UpdateBehavior()` logic to find targets, prioritizing `PriorityShipTypes`, then closest.
  - [ ] *(Optional)* Create other targeting behaviors (e.g., `WeakestTargetingBehavior`, `StrongestTargetingBehavior`) following the same pattern.

- [ ] **Implement Weapon Behaviors**:
  - [ ] Create `/SpaceGame/Scripts/Battle/AI/Components/Weapon/OpportunisticWeaponBehavior.cs`
    - [ ] Inherit from `WeaponBehaviorComponent`.
    - [ ] Implement `BehaviorType` property to return `WeaponBehaviorType.Opportunistic`.
    - [ ] Implement `Initialize()` to get references to the `TargetingBehaviorComponent` and the ship's `ShipWeaponController`.
    - [ ] Implement `UpdateBehavior()` logic to fire weapons if the target is within `FiringAngleThreshold`.
  - [ ] Create `/SpaceGame/Scripts/Battle/AI/Components/Weapon/StrategicWeaponBehavior.cs`
    - [ ] Inherit from `WeaponBehaviorComponent`.
    - [ ] Implement `BehaviorType` property to return `WeaponBehaviorType.Strategic`.
    - [ ] Add `[Export] public float OptimalFiringDistance { get; set; } = 400f;`
    - [ ] Add `[Export] public float DistanceTolerance { get; set; } = 200f;`
    - [ ] Implement `Initialize()` to get references to `TargetingBehaviorComponent` and `ShipWeaponController`.
    - [ ] Implement `UpdateBehavior()` logic to fire weapons if the target is within `FiringAngleThreshold` and optimal range.
  - [ ] *(Optional)* Create other weapon behaviors (e.g., `BurstWeaponBehavior`, `ConservativeWeaponBehavior`) following the same pattern.

## Phase 4: Implement Behavior Configuration Resource

- [ ] **Create `EnemyBehaviorConfiguration` Resource Script**:
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/EnemyBehaviorConfiguration.cs`
  - [ ] Define `[GlobalClass] public partial class EnemyBehaviorConfiguration : Resource, IJsonPopulatable`
  - [ ] Add `[Export] public string BehaviorName { get; set; } = "Default";`
  - [ ] Add `[Export]` properties for each behavior type enum (e.g., `public MovementBehaviorType MovementBehaviorType { get; set; }`).
  - [ ] Add `[Export]` properties for all configurable parameters from the concrete behavior components (e.g., `ThrustStrength`, `CircleDistance`, `DetectionRange`, `PriorityShipTypes`, `OptimalFiringDistance`).
  - [ ] Implement the `PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)` method to allow overriding configuration values from JSON. Ensure to handle enum deserialization correctly (string or int).

## Phase 5: Implement Behavior Factory

- [ ] **Create `EnemyBehaviorFactory` Script**:
  - [ ] Create a new C# script file: `/SpaceGame/Scripts/Battle/AI/EnemyBehaviorFactory.cs`
  - [ ] Define `public static class EnemyBehaviorFactory`
  - [ ] Implement `public static ShipBehaviorController CreateBehaviorController(EnemyBehaviorConfiguration config)` method:
    - [ ] Instantiate a new `ShipBehaviorController`.
    - [ ] Based on `config.MovementBehaviorType`, instantiate the corresponding concrete movement behavior, set its properties from `config`, and add it as a child to the controller.
    - [ ] Based on `config.TargetingBehaviorType`, instantiate the corresponding concrete targeting behavior, set its properties from `config`, and add it as a child to the controller.
    - [ ] Based on `config.WeaponBehaviorType`, instantiate the corresponding concrete weapon behavior, set its properties from `config`, and add it as a child to the controller.
    - [ ] Return the configured `ShipBehaviorController`.

## Phase 6: Documentation Updates

- [ ] **Update `/docs/` Directory**:
  - [ ] Create or update a design document (e.g., `/docs/enemy_ai_system.md`) detailing the new component-based enemy behavior system.
    - [ ] Include diagrams showing the relationships between `ShipEntity`, `ShipBehaviorController`, and the various `EnemyBehaviorComponent` types.
    - [ ] Explain how to create new behaviors and configure them using `EnemyBehaviorConfiguration` resources.
    - [ ] Ensure the document adheres to `.windsurf/rules/documentation_guidelines.md`.
- [ ] **Update `/docs/state/` Directory**:
  - [ ] Create or update a document (e.g., `/docs/state/enemy_behavior_state.md`) to describe the structure of `EnemyBehaviorConfiguration.cs` if it's considered a significant shared state object.
    - [ ] List key properties and their types.
    - [ ] Briefly describe the purpose of the configuration.
    - [ ] Ensure the document adheres to `.windsurf/rules/documentation_guidelines.md`.

## Notes/Considerations:
- [ ] Ensure all new C# classes adhere to the project's coding conventions (namespaces, naming, `partial` for Godot types, etc.).
- [ ] Remember that `ShipEntity` will need to have a `ShipBehaviorController` node as a child, which in turn will have the specific behavior component nodes as its children. This can be set up in enemy ship scenes.
- [ ] The `EnemyBehaviorFactory` can be used by other systems (e.g., `BattleEntityFactory` or a spawner system) to dynamically create and attach behaviors to enemy ships based on loaded configurations.