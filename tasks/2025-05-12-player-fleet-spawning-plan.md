# Implementation Plan: Player Fleet Spawning & Overrides

This plan details the implementation of spawning the player's fleet based on `PlayerFleetComposition` data in `GameState` and applying property overrides using `PropertyOverrideService`.

## Phase 1: Implement Player Fleet Spawning in BattleManager

-   [ ] **Modify `Game/Battle/BattleManager.cs`**
    *   Add necessary `using` statements (e.g., `SpaceGame.State`, `SpaceGame.Utility`, `SpaceGame.Scripts.Battle.Registry`).
    *   Create a new public method: `public void SpawnPlayerFleet(Vector2 flagshipSpawnPosition)`.
        *   **Retrieve GameState & Fleet Data**:
            *   Access the current game state: `var gameState = GameStateManager.Instance.CurrentGameState;` (assuming `GameStateManager` is accessible and provides `CurrentGameState`).
            *   Perform null checks: If `gameState` or `gameState.PlayerFleet` is null, log an error using `Logger.Error()` and return early.
            *   Store `gameState.PlayerFleet` in a local variable (e.g., `var fleetData = gameState.PlayerFleet;`).
        *   **Spawn Flagship**:
            *   Check if `fleetData.Flagship` is null or `fleetData.Flagship.EntityId` is null/empty. If so, log an error and potentially return or skip flagship spawning.
            *   Instantiate the flagship: `var flagshipNode = BattleEntityRegistry.Instance.InstantiateEntity(fleetData.Flagship.EntityId);`.
            *   Check if `flagshipNode` is null. If so, log an error and return or skip.
            *   Cast to `BattleEntity` (or the base type of your ship instances): `var flagshipEntity = flagshipNode as BattleEntity;`. If cast fails, log error and handle.
            *   Set global position: `flagshipEntity.GlobalPosition = flagshipSpawnPosition;`.
            *   Apply overrides: `PropertyOverrideService.Instance.ApplyOverrides(flagshipEntity, fleetData.Flagship.PropertyOverrides);`.
            *   Add to scene: `AddChild(flagshipEntity);` (or add to a specific container node managed by `BattleManager`).
        *   **Spawn Escorts**:
            *   Check if `fleetData.EscortShips` is null. If so, skip escort spawning.
            *   Loop through each `escortShipData` in `fleetData.EscortShips`.
                *   Check if `escortShipData.EntityId` is null/empty. If so, log a warning and continue to the next escort.
                *   Instantiate escort: `var escortNode = BattleEntityRegistry.Instance.InstantiateEntity(escortShipData.EntityId);`.
                *   Check if `escortNode` is null. If so, log an error and continue.
                *   Cast to `BattleEntity`: `var escortEntity = escortNode as BattleEntity;`. If cast fails, log error and continue.
                *   Calculate position: `escortEntity.GlobalPosition = flagshipSpawnPosition + escortShipData.RelativePosition;`.
                *   Apply overrides: `PropertyOverrideService.Instance.ApplyOverrides(escortEntity, escortShipData.PropertyOverrides);`.
                *   Add to scene: `AddChild(escortEntity);` (or to the same container as the flagship).
    *   Determine where `SpawnPlayerFleet` will be called (e.g., in an existing method that initializes a battle, after the player's spawn point is determined).

## Phase 2: Review PropertyOverrideService (No changes if already sufficient)

-   [ ] **Review `Game/Scripts/Utility/PropertyOverrideService.cs`**
    *   Verify that the existing `ApplyOverrides(GodotObject target, Dictionary<string, object> overrides)` method and its internal type conversion logic are robust enough to handle the types of properties and values expected from `FleetShipData.PropertyOverrides`. This includes common primitives (`int`, `float`, `bool`, `string`), Godot types (`Vector2`, `Vector3`, `Color`), and enums.
    *   No specific code changes are mandated by this step if the service already fulfills these requirements from its prior implementation plan for the property override system.

## Phase 3: Documentation Updates

-   [ ] **Update `docs/player_fleet_system.md`**
    *   In the "Data Flow & Spawning Process" section, update the textual description to accurately reflect that `BattleManager.SpawnPlayerFleet` is the method responsible.
    *   Ensure the explanation of how `PropertyOverrideService` is invoked is clear.
    *   Review the Mermaid diagram in this file. If the new details (like the `SpawnPlayerFleet` method or specific interactions) significantly alter the flow, update the diagram. For example, `BattleManager` would be a more central actor in the spawning logic part of the diagram.

-   [ ] **Create/Update `docs/battle_manager_overview.md`** (Create if it does not exist, or update relevant sections if it does)
    *   **File Location**: `docs/battle_manager_overview.md`
    *   **Content if Creating**: 
        *   Briefly describe the overall purpose of `BattleManager.cs`.
        *   Add a section for "Key Responsibilities" or "Features".
    *   **Add/Update Section for `SpawnPlayerFleet`**: 
        *   Title: `Player Fleet Spawning` (or similar).
        *   Describe the `SpawnPlayerFleet(Vector2 flagshipSpawnPosition)` method.
        *   Explain its role: Deploys the player's pre-configured fleet (flagship and escorts) at the start of a battle scenario.
        *   Detail its process: Reads fleet data from `GameState.PlayerFleet`, instantiates ships via `BattleEntityRegistry`, positions them, and applies `PropertyOverrides` via `PropertyOverrideService`.
        *   **Optional Diagram**: If helpful, include a simple Mermaid sequence diagram illustrating `BattleManager.SpawnPlayerFleet`'s interactions:
            ```mermaid
            sequenceDiagram
                participant Caller
                participant BM as BattleManager
                participant GSM as GameStateManager
                participant BER as BattleEntityRegistry
                participant POS as PropertyOverrideService
                participant ShipInstance as BattleEntity

                Caller->>BM: SpawnPlayerFleet(spawnPos)
                BM->>GSM: Get CurrentGameState
                GSM-->>BM: GameState (with PlayerFleet)
                BM->>BER: InstantiateEntity(flagshipId)
                BER-->>BM: flagshipNode
                BM->>POS: ApplyOverrides(flagshipNode, overrides)
                BM->>BM: AddChild(flagshipNode)
                loop For each Escort
                    BM->>BER: InstantiateEntity(escortId)
                    BER-->>BM: escortNode
                    BM->>POS: ApplyOverrides(escortNode, overrides)
                    BM->>BM: AddChild(escortNode)
                end
            ```
    *   Ensure adherence to `documentation_guidelines.md` (conciseness, visuals if impactful).
