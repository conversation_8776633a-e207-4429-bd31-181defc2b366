# Implementation Plan: Escort Ship Modular Behavior System (v3 - Condensed)

This plan outlines implementing updateable AI behaviors (<PERSON><PERSON>, Protect, Attack) using Firebelley's `DelegateStateMachine` and a `StateMachineControlComponent`.

**Core Principles:**
- Uses `DelegateStateMachine`'s `State` delegates and `StateFlows`.
- No common base state class. Behaviors are plain C# modules.
- `StateMachineControlComponent` manages behaviors.
- Behavior Modules provide `EnterBehavior()`, `UpdateBehavior()`, `ExitBehavior()` methods.
- Delta time for updates accessed via `BattleEntity` (e.g., `_entity.GetProcessDeltaTime()`).

## Phase 1: Foundational Elements

- [ ] 1.1. **Prerequisite**: `Firebelley.GodotUtilities` NuGet package installed.
- [ ] 1.2. **Directory Structure**:
    - `Game/Scripts/AI/Components/` (for `StateMachineControlComponent`)
    - `Game/Scripts/AI/Behaviors/Escort/` (for Escort-specific behavior modules)
    - `Game/Scripts/AI/Core/` (for `BehaviorType` enum)
- [ ] 1.3. **Define `BehaviorType` Enum** (`Game/Scripts/AI/Core/BehaviorType.cs`):
    ```csharp
    // namespace SpaceGame.AI.Core
    // public enum BehaviorType { None, Wander, ProtectEntity, AttackTarget /*, ...*/ }
    ```

## Phase 2: Implement Behavior Modules

Plain C# classes, not Godot Nodes unless essential. Each in its own file within `Game/Scripts/AI/Behaviors/Escort/`.

- [ ] 2.1. **`EscortWanderBehavior.cs`**:
    -   Constructor: `public EscortWanderBehavior(BattleEntity entity, StateMachineControlComponent controllerContext)`
    -   Methods: `public void EnterBehavior()`, `public void UpdateBehavior()`, `public void ExitBehavior()`.
    -   Logic: Select and move towards wander points. `UpdateBehavior` uses `_entity.GetProcessDeltaTime()`.
- [ ] 2.2. **`EscortProtectBehavior.cs`**:
    -   Constructor: `public EscortProtectBehavior(BattleEntity entity, BattleEntity entityToProtect, StateMachineControlComponent controllerContext)`
    -   Methods: As above, plus `public void SetEntityToProtect(BattleEntity target)`.
    -   Logic: Maintain position relative to `entityToProtect`.
- [ ] 2.3. **`EscortAttackBehavior.cs`**:
    -   Constructor: `public EscortAttackBehavior(BattleEntity entity, StateMachineControlComponent controllerContext)`
    -   Methods: As above, plus `public void SetAttackTarget(BattleEntity target)`.
    -   Logic: Pursue and attack target.

## Phase 3: Implement StateMachineControlComponent

- [ ] 3.1. **`StateMachineControlComponent.cs`** (`Game/Scripts/AI/Components/`):
    -   Derives from `EntityControlComponent`.
    -   Fields:
        -   `private DelegateStateMachine<BehaviorType> _behaviorStateMachine;`
        -   Instances of behavior modules (e.g., `private EscortWanderBehavior _escortWanderBehavior;`).
    -   `[Export]` properties for configuration (e.g., `EntityToProtect`, `DefaultBehavior`).
    -   `_Ready()`:
        -   Initialize behavior modules (e.g., `_escortWanderBehavior = new EscortWanderBehavior(this.Entity, this);`).
        -   Initialize `_behaviorStateMachine = new DelegateStateMachine<BehaviorType>();`.
        -   Add states using `_behaviorStateMachine.AddState(BehaviorType.Wander, new DelegateStateMachine<BehaviorType>.StateFlows(normal: _escortWanderBehavior.UpdateBehavior, enterState: _escortWanderBehavior.EnterBehavior, leaveState: _escortWanderBehavior.ExitBehavior));` for each behavior.
        -   Set initial state (e.g., `_behaviorStateMachine.ChangeState(DefaultBehavior);`).
    -   `ProcessControl(float delta)`: Calls `_behaviorStateMachine?.Update();` (Note: `DelegateStateMachine.Update()` is parameterless).
    -   Command Methods: `public void CommandWander()`, `public void CommandProtect(BattleEntity target)`, `public void CommandAttack(BattleEntity target)` to change `_behaviorStateMachine` states.

## Phase 4: Integration and Configuration

- [ ] 4.1. Add `StateMachineControlComponent` node to Escort Ship Scene (`.tscn`). Configure exported properties in the Godot Editor if applicable.
- [ ] 4.2. Dynamically configure the component upon spawning an escort (e.g., in `BattleManager` or spawner logic):
    ```csharp
    // Example: When spawning an escort ship instance
    // var escortControl = escortInstance.GetNode<StateMachineControlComponent>("StateMachineControlComponent");
    // if (playerShip != null) { escortControl.CommandProtect(playerShip); }
    ```

## Phase 5: Documentation Updates

- [ ] 5.1. Create/Update design document `docs/ai/escort_ship_ai_behavior.md`:
    -   Describe the new architecture: `StateMachineControlComponent` and modular Behavior Modules.
    -   Detail how `DelegateStateMachine` is used with `StateFlows`.
    -   Include a Mermaid.js state diagram illustrating `BehaviorType` transitions.
    -   Ensure adherence to `documentation_guidelines.md`.
- [ ] 5.2. If the new AI behaviors introduce or significantly modify any shared/persistent state data relevant outside the escort ship itself, create or update a corresponding document in `docs/state/`, adhering to guidelines.

## Notes/Considerations:
- Behavior modules are plain C# classes instantiated and managed by `StateMachineControlComponent`.
- The `controllerContext` in behavior module constructors allows them to call back to `StateMachineControlComponent` if needed (e.g., to request a state change based on internal logic).
- Ensure robust error handling (null checks for entities, targets) in all modules and the component.
- All new C# files and classes must adhere to project coding conventions (namespaces, naming, `partial` for Godot nodes, etc.).
