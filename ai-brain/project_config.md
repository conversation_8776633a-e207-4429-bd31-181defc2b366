# Project Configuration (LTM)

*This file contains the stable, long-term context for the project.*
*It should be updated infrequently, primarily when core goals, tech, or patterns change.*

---

## Core Goal

Create a 2D space game that is fun and engaging, simple to learn, but hard to master.

The game mainly consists of two parts:

1. **Exploration:** Players will follow a map similar to SLay the Spire, where they can choose different paths, but ultimately reach the same goal point (likely a boss or similar encounter)
2. **Combat:** Players will have a single fleet that they need to guide through the map. By default, players will pilot the biggest ship in the fleet, but they can take control of other ships at will. For each "encounter" on the map, the player will have a specific task to fulfill (i.e. survive, defeat all enemies, mine asteroids, investigate something, etc.).

---

## Tech Stack

-  **Game Engine:** Godot 4.4
-  **Programming Language:** C#
-  **Deployment:** Steamworks SDK for Steam integration, itch.io for indie distribution

---

## Critical Patterns & Conventions

### General Best Practices
- Write clean, maintainable, and modular C# code.
- Use Godot's built-in features and tools wherever possible.
- Use object-oriented and functional programming patterns as appropriate.
- Follow <PERSON><PERSON>'s conventions for C#
- When possible, use the built-in C# features and tools, such as pattern matching, null-coalescing assignment, etc.
- Before finishing your task, build the project using `dotnet build` to ensure it compiles without errors.

### Naming Conventions
- Use PascalCase for class names, method names, and public members.
- Use camelCase for local variables and private fields.
- Use UPPERCASE for constants.
- Prefix interface names with "I" (e.g., 'IUserService').
- Use descriptive variable names and avoid abbreviations.

### C# and .NET Usage
- Use C# 10+ features when appropriate (e.g., record types, pattern matching, null-coalescing assignment).
- Follow the C# Coding Conventions (https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions)
- Use 'var' for implicit typing when the type is obvious.
- Keep scripts focused on single functionalities to promote reuse and testability.
- Ensure to denote nullability in method signatures and variable declarations and use null-coalescing operators where appropriate.
- When attempting to generate test scenarios, stick to C# unit tests, do not attempt to generate tests that require the Godot engine unless explicitly requested

### Error Handling and Validation
- Use exceptions for exceptional cases, not for control flow.
- Implement error handling using try-catch blocks, particularly in file I/O and network operations.

### Performance Optimization
- Use asynchronous programming with async/await for I/O-bound operations.
- Use efficient LINQ queries and avoid N+1 query problems.
- Optimize performance by using object pooling where applicable.

### Godot-Specific Patterns
- Extend from Node for script components that do not require a position in 2D space but require processing every frame. If a position is needed, extend from Node2D instead.
- Extend from Godot's other existing classes when appropriate.
- Use Resource for shared data and configuration. Add [Export] attributes for serialization and editor visibility.
- Implement state-driven architecture for character behaviors and game state management.
- When handling player input, do not use keycodes. Use Godot's input map functionality instead. Create new input map entries when necessary.
- Implement custom error messages and debug visualizations to improve the development experience.
- Any class that extends from Godot.Object or any of its subclasses also has to have the partial keyword.
- Ensure that each class that extends from Godot.Object or any of its subclasses has a public constructor without parameters. Otherwise reloading the assembly in the editor will fail.
- Dot not create any Godot.Nodes or any of it's subclasses in code, but rather create and configure them in the scene file.
- Do not generate empty _Ready() methods that only contain a single 'base._Ready()'.
- Do not use NodePaths. Use direct node references instead.

### Debugging
- Create debug and informational UI elements using ImGui.Net when instructed or when it just makes sense to visually represent game state.

---

## Key Constraints

- The top level `Game` directory is not part of the namespace. Skip it when generating namespace lines in files.
- Use the Logger class provided in this project for logging and debugging (e.g., Logger.Debug, Logger.Warning, Logger.Info, Logger.Error). Never use GD.Print etc directly.
- Use the SimpleProfiler class provided in this project to identify and resolve performance issues.
- Use the C# events over built-in Godot signals for event handling. Add those events as static on the class that raises them. Use the `EventHandler` delegate for event signatures.

---

## Tokenization Settings

*   **Estimation Method:** Character-based
*   **Characters Per Token (Estimate):** 4