### Space Battles Todo

- [X] Create an entity for space battles
- [X] Implement a base ship class with functionality that is going to be used by subclasses
- [X] Create a base class for entities that the player can control directly. The goal here is that it is easy to switch out which entity the player actively controls.
  - It should derive from `BattleEntity`
  - It should gather input and pass it to `BattleEntity`
  - If the player is not actively controlling the entity, it should act as a normal `BattleEntity`
- [X] Create scenes for a few battle entities
  - A basic scene for a `Scout` ship
  - A basic scene for a `Fighter` ship
  - No need to set sprites or animations yet, just a basic scene that can be used to test the ship
- [X] Create a scene represents a space battle
  - It should have a background that is a star field (with Parallax scrolling)
  - It should have a ship that the player can control
  - It should have a few enemy ships
  - It should be able to be shown from `UniverseScene` using a new entry in the `GameViewState`
- [X] Integrate the space battle scene into the game
  - The `Start Battle` button in the `UniverseScene` should start a space battle
  - When the space battle scene is shown, things in `UniverseScene` should be paused (i.e. not process any input, not update any planets, etc.)
  - Th player should be able to control their ship
  - Enemy ships don't need to move or do anything yet
  - The player should be able to return to the `UniverseScene` from the space battle scene and everything in `UniverseScene` should be resumed