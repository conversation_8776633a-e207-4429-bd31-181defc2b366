# Workflow State & Rules (STM + Rules + Log)

*This file contains the dynamic state, embedded rules, active plan, and log for the current session.*
*It is read and updated frequently by the AI during its operational loop.*

---

## State

*Holds the current status of the workflow.*

```yaml
Phase: CONSTRUCT # Current workflow phase (AN<PERSON>YZ<PERSON>, <PERSON><PERSON><PERSON>PRINT, CONSTRUC<PERSON>, VA<PERSON><PERSON><PERSON><PERSON>, BLUEPRINT_REVISE)
Status: COMPLETED # Current status (READY, IN_PROGRESS, BLOCKED_*, NEEDS_*, COMPLETED, COMPLETED_ITERATION)
CurrentTaskID: FixBattleMovement # Identifier for the main task being worked on
CurrentStep: 0 # Identifier for the specific step in the plan being executed
CurrentItem: null # Identifier for the item currently being processed in iteration
```

---

## Plan

*Contains the step-by-step implementation plan generated during the BLUEPRINT phase.*

### Task: Fix Movement in Battle Scene

**Problem Analysis:**
After examining the code, I've identified the following issues:

1. Input actions for movement and rotation are defined in PlayerControlComponent but may not be properly set up in the Godot Input Map.
2. The PlayerControlComponent is not being attached to battle entities.
3. Entities are not being registered with the EntityControlManager.
4. There may be no AI control components attached to enemy entities.

**Implementation Plan:**

1. **Verify and Create Input Actions**
   - Check if the required input actions exist in the project.godot file
   - If not, add the following input actions:
     - `move_forward` (W key, Up arrow)
     - `move_backward` (S key, Down arrow)
     - `rotate_left` (A key, Left arrow)
     - `rotate_right` (D key, Right arrow)
     - `switch_entity_next` (Tab key)
     - `switch_entity_prev` (Shift+Tab)

2. **Create PlayerControlComponent for Battle Entities**
   - Modify the BattleEntityScene.tscn to include a PlayerControlComponent node
   - Ensure the PlayerControlComponent is properly connected to the BattleEntity

3. **Update SpaceBattleScene.cs to Register Entities**
   - Modify the RegisterEntity method to register player entities with the EntityControlManager
   - Add code to set the first player entity as the active controlled entity

4. **Add SimpleAIControlComponent to Enemy Entities**
   - Use the existing SimpleAIControlComponent for enemy entities
   - Ensure it's attached to enemy entities in the battle scene

5. **Fix Entity Initialization**
   - Ensure entities are properly initialized with the correct allegiance
   - Verify that player ships have player control and enemy ships have AI control

---

## Rules

*Embedded rules governing the AI's autonomous operation.*

**# --- Core Workflow Rules ---**

RULE_WF_PHASE_ANALYZE:
**Constraint:** Goal is understanding request/context. NO solutioning or implementation planning.

RULE_WF_PHASE_BLUEPRINT:
**Constraint:** Goal is creating a detailed, unambiguous step-by-step plan. NO code implementation.

RULE_WF_PHASE_CONSTRUCT:
**Constraint:** Goal is executing the `## Plan` exactly. NO deviation. If issues arise, trigger error handling or revert phase.

RULE_WF_PHASE_VALIDATE:
**Constraint:** Goal is verifying implementation against `## Plan` and requirements using tools. NO new implementation.

RULE_WF_TRANSITION_01:
**Trigger:** Explicit user command (`@analyze`, `@blueprint`, `@construct`, `@validate`).
**Action:** Update `State.Phase` accordingly. Log phase change.

RULE_WF_TRANSITION_02:
**Trigger:** AI determines current phase constraint prevents fulfilling user request OR error handling dictates phase change (e.g., RULE_ERR_HANDLE_TEST_01).
**Action:** Log the reason. Update `State.Phase` (e.g., to `BLUEPRINT_REVISE`). Set `State.Status` appropriately (e.g., `NEEDS_PLAN_APPROVAL`). Report to user.

RULE_ITERATE_01: # Triggered by RULE_MEM_READ_STM_01 when State.Status == READY and State.CurrentItem == null, or after VALIDATE phase completion.
**Trigger:** `State.Status == READY` and `State.CurrentItem == null` OR after `VALIDATE` phase completion.
**Action:**
1. Check `## Items` section for more items.
2. If more items:
3. Set `State.CurrentItem` to the next item.
4. Clear `## Log`.
5. Set `State.Phase = ANALYZE`, `State.Status = READY`.
6. Log "Starting processing item [State.CurrentItem]".
7. If no more items:
8. Trigger `RULE_ITERATE_02`.

RULE_ITERATE_02:
**Trigger:** `RULE_ITERATE_01` determines no more items.
**Action:**
1. Set `State.Status = COMPLETED_ITERATION`.
2. Log "Tokenization iteration completed."

**# --- Initialization & Resumption Rules ---**

RULE_INIT_01:
**Trigger:** AI session/task starts AND `workflow_state.md` is missing or empty.
**Action:**
1. Create `workflow_state.md` with default structure.
2. Read `project_config.md` (prompt user if missing).
3. Set `State.Phase = ANALYZE`, `State.Status = READY`.
4. Log "Initialized new session."
5. Prompt user for the first task.

RULE_INIT_02:
**Trigger:** AI session/task starts AND `workflow_state.md` exists.
**Action:**
1. Read `project_config.md`.
2. Read existing `workflow_state.md`.
3. Log "Resumed session."
4. Check `State.Status`: Handle READY, COMPLETED, BLOCKED_*, NEEDS_*, IN_PROGRESS appropriately (prompt user or report status).

RULE_INIT_03:
**Trigger:** User confirms continuation via RULE_INIT_02 (for IN_PROGRESS state).
**Action:** Proceed with the next action based on loaded state and rules.

**# --- Memory Management Rules ---**

RULE_MEM_READ_LTM_01:
**Trigger:** Start of a new major task or phase.
**Action:** Read `project_config.md`. Log action.
RULE_MEM_READ_STM_01:
**Trigger:** Before *every* decision/action cycle.
**Action:**
1. Read `workflow_state.md`.
2. If `State.Status == READY` and `State.CurrentItem == null`:
3. Log "Attempting to trigger RULE_ITERATE_01".
4. Trigger `RULE_ITERATE_01`.

RULE_MEM_UPDATE_STM_01:
**Trigger:** After *every* significant action or information receipt.
**Action:** Immediately update relevant sections (`## State`, `## Plan`, `## Log`) in `workflow_state.md` and save.

RULE_MEM_UPDATE_LTM_01:
**Trigger:** User command (`@config/update`) OR end of successful VALIDATE phase for significant change.
**Action:** Propose concise updates to `project_config.md` based on `## Log`/diffs. Set `State.Status = NEEDS_LTM_APPROVAL`. Await user confirmation.

RULE_MEM_VALIDATE_01:
**Trigger:** After updating `workflow_state.md` or `project_config.md`.
**Action:** Perform internal consistency check. If issues found, log and set `State.Status = NEEDS_CLARIFICATION`.

**# --- Tool Integration Rules (Cursor Environment) ---**

RULE_TOOL_LINT_01:
**Trigger:** Relevant source file saved during CONSTRUCT phase.
**Action:** Instruct Cursor terminal to run lint command. Log attempt. On completion, parse output, log result, set `State.Status = BLOCKED_LINT` if errors.

RULE_TOOL_FORMAT_01:
**Trigger:** Relevant source file saved during CONSTRUCT phase.
**Action:** Instruct Cursor to apply formatter or run format command via terminal. Log attempt.

RULE_TOOL_TEST_RUN_01:
**Trigger:** Command `@validate` or entering VALIDATE phase.
**Action:** Instruct Cursor terminal to run test suite. Log attempt. On completion, parse output, log result, set `State.Status = BLOCKED_TEST` if failures, `TESTS_PASSED` if success.

RULE_TOOL_APPLY_CODE_01:
**Trigger:** AI determines code change needed per `## Plan` during CONSTRUCT phase.

RULE_PROCESS_ITEM_01:
**Trigger:** `State.Phase == CONSTRUCT` and `State.CurrentItem` is not null and current step in `## Plan` requires item processing.
**Action:**
1. **Get Item Text:** Based on `State.CurrentItem`, extract the corresponding 'Text to Tokenize' from the `## Items` section.
2. **Summarize (Placeholder):**  Use a placeholder to generate a summary of the extracted text.  For example, "Summary of [text] is [placeholder summary]".
3. **Estimate Token Count:**
a. Read `Characters Per Token (Estimate)` from `project_config.md`.
b. Get the text content of the item from the `## Items` section. (Placeholder: Implement logic to extract text based on `State.CurrentItem` from the `## Items` table.)
c. Calculate `estimated_tokens = length(text_content) / 4`.
4. **Summarize (Placeholder):** Use a placeholder to generate a summary of the extracted text.  For example, "Summary of [text] is [placeholder summary]". (Placeholder: Replace with actual summarization tool/logic)
5. **Store Results:** Append a new row to the `## TokenizationResults` table with:
*   `Item ID`: `State.CurrentItem`
*   `Summary`: The generated summary. (Placeholder: Implement logic to store the summary.)
*   `Token Count`: `estimated_tokens`.
6. Log the processing actions, results, and estimated token count to the `## Log`. (Placeholder: Implement logging.)
5. Log the processing actions, results, and estimated token count to the `## Log`.

**Action:** Generate modification. Instruct Cursor to apply it. Log action.

**# --- Error Handling & Recovery Rules ---**

RULE_ERR_HANDLE_LINT_01:
**Trigger:** `State.Status` is `BLOCKED_LINT`.
**Action:** Analyze error in `## Log`. Attempt auto-fix if simple/confident. Apply fix via RULE_TOOL_APPLY_CODE_01. Re-run lint via RULE_TOOL_LINT_01. If success, reset `State.Status`. If fail/complex, set `State.Status = BLOCKED_LINT_UNRESOLVED`, report to user.

RULE_ERR_HANDLE_TEST_01:
**Trigger:** `State.Status` is `BLOCKED_TEST`.
**Action:** Analyze failure in `## Log`. Attempt auto-fix if simple/localized/confident. Apply fix via RULE_TOOL_APPLY_CODE_01. Re-run failed test(s) or suite via RULE_TOOL_TEST_RUN_01. If success, reset `State.Status`. If fail/complex, set `State.Phase = BLUEPRINT_REVISE`, `State.Status = NEEDS_PLAN_APPROVAL`, propose revised `## Plan` based on failure analysis, report to user.

RULE_ERR_HANDLE_GENERAL_01:
**Trigger:** Unexpected error or ambiguity.
**Action:** Log error/situation to `## Log`. Set `State.Status = BLOCKED_UNKNOWN`. Report to user, request instructions.

---

## Log

*A chronological log of significant actions, events, tool outputs, and decisions.*
*(This section will be populated by the AI during operation)*

*   `[2025-04-29 18:31:00] Task completed: Integrated the space battle scene into the game.`
*   `[2025-05-05 12:36:13] Resumed session. Previous task IntegrateSpaceBattleScene was completed. Ready for new task.`
*   `[2025-05-07 17:57:05] Initialized autonomous workflow. Read project_config.md, workflow_state.md, and ai_rules.md. Ready for new task.`
*   `[2025-05-07 17:58:37] New task received: Fix movement in battle scene. Player ship cannot be controlled and enemies are not moving.`
*   `[2025-05-07 17:59:30] ANALYZE phase: Examined code in SpaceBattleScene.cs, BattleEntity.cs, PlayerControlComponent.cs, EntityControlManager.cs, and related files.`
*   `[2025-05-07 17:59:45] ANALYZE phase complete: Found issues with player input mapping and entity registration. Transitioning to BLUEPRINT phase.`
*   `[2025-05-07 18:06:00] BLUEPRINT phase: Created implementation plan with 5 steps to fix battle movement issues.`
*   `[2025-05-07 18:06:30] Plan approved with modifications: Using existing SimpleAIControlComponent and removing testing step. Transitioning to CONSTRUCT phase.`
*   `[2025-05-07 18:07:15] CONSTRUCT phase: Step 1 - Checked input actions in project.godot. Found that movement actions exist but switch_entity_next and switch_entity_prev need key bindings.`
*   `[2025-05-07 18:07:45] CONSTRUCT phase: Step 1 completed - Added key bindings for switch_entity_next (Tab) and switch_entity_prev (Shift+Tab).`
*   `[2025-05-07 18:08:00] CONSTRUCT phase: Starting Step 2 - Adding PlayerControlComponent to BattleEntityScene.`
*   `[2025-05-07 18:09:15] CONSTRUCT phase: Step 2 completed - Added PlayerControlComponent and SimpleAIControlComponent to BattleEntityScene.tscn.`
*   `[2025-05-07 18:09:30] CONSTRUCT phase: Starting Step 3 - Updating SpaceBattleScene.cs to register entities with EntityControlManager.`
*   `[2025-05-07 18:10:45] CONSTRUCT phase: Step 3 completed - Updated SpaceBattleScene.cs to register player entities with EntityControlManager and activate AI control for enemy entities.`
*   `[2025-05-07 18:11:00] CONSTRUCT phase: Starting Step 4 - Adding SimpleAIControlComponent to enemy entities.`
*   `[2025-05-07 18:11:30] CONSTRUCT phase: Step 4 already completed - SimpleAIControlComponent was added to BattleEntityScene.tscn in Step 2 and activation code was added in Step 3.`
*   `[2025-05-07 18:11:45] CONSTRUCT phase: Starting Step 5 - Fixing entity initialization to ensure proper allegiance settings.`
*   `[2025-05-07 18:12:15] CONSTRUCT phase: Step 5 completed - Verified that entity initialization with proper allegiance is already correctly implemented in BattleEntityFactory.cs.`
*   `[2025-05-07 18:12:30] Initial implementation completed, but testing revealed an issue with player control activation.`
*   `[2025-05-07 18:18:30] New issue identified: NullReferenceException in EntityControlComponent when activating player control via TreeEntered event. The _entity reference is null when the component is activated.`
*   `[2025-05-07 18:19:15] Fixed player control activation issue by changing the approach in BattleEntityFactory.cs and PlayerControlComponent.cs.`
*   `[2025-05-07 18:19:45] Instead of activating via TreeEntered event, now setting IsPlayerControlled flag and using a delayed activation in the component's _Ready method.`
*   `[2025-05-07 18:20:00] Task completed: Fixed movement in battle scene by adding input bindings, adding control components to entities, ensuring proper entity registration, and fixing player control activation.`

---

## Items

*This section will contain the list of items to be processed.*
*(The format of items is a table)*

*Example (Table):*
*   `| Item ID | Text to Tokenize |`
*   `|---|---|`
*   `| item1 | This is the first item to tokenize. This is a short sentence. |`
*   `| item2 | Here is the second item for tokenization. This is a slightly longer sentence to test the summarization. |`
*   `| item3 | This is item number three to be processed. This is a longer sentence to test the summarization. This is a longer sentence to test the summarization. |`
*   `| item4 | And this is the fourth and final item in the list. This is a very long sentence to test the summarization. This is a very long sentence to test the summarization. This is a very long sentence to test the summarization. This is a very long sentence to test the summarization. |`

---

## TokenizationResults

*This section will store the summarization results for each item.*
*(Results will include the summary and estimated token count)*

*Example (Table):*
*   `| Item ID | Summary | Token Count |`
*   `|---|---|---|`

## TokenizationResults

*This section will store the tokenization results for each item.*
*(Results will include token counts and potentially tokenized text)*

*Example (Table):*
*   `| Item ID | Token Count | Tokenized Text (Optional) |`
*   `|---|---|---|`
*   `| item1 | 10 | ... (tokenized text) ... |`
*   `| item2 | 12 | ... (tokenized text) ... |`
*   `