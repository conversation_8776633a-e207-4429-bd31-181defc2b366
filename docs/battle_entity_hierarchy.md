# Battle Entity Hierarchy

```mermaid
classDiagram
    BaseBattleEntity <|-- ShipEntity
    BaseBattleEntity <|-- ProjectileEntity
    BaseBattleEntityData <|-- ShipEntityData
    BaseBattleEntityData <|-- ProjectileEntityData
    
    BaseBattleEntity --> BaseBattleEntityData : uses
    ShipEntity --> ShipEntityData : uses
    ProjectileEntity --> ProjectileEntityData : uses
    
    IBattleEntity <|.. BaseBattleEntity
    
    class IBattleEntity{
        +string Id
        +float CurrentHealth
        +float MaxHealth
        +bool IsDead
        +Allegiance CurrentAllegiance
        +Vector2 Position
        +float Rotation
        +float ApplyDamage(float, IBattleEntity)
        +float ApplyHealing(float)
        +bool IsHostileTo(IBattleEntity)
        +void Destroy()
    }
    
    class BaseBattleEntity{
        +BaseBattleEntityData EntityData
        -float currentHealth
        +virtual void _Ready()
        +virtual float ApplyDamage()
        +virtual float ApplyHealing()
        +virtual void Destroy()
    }
    
    class ShipEntity{
        +ShipEntityData EntityData
        +Sprite2D EntitySprite
        +CollisionShape2D CollisionShape
        +ThrusterComponent Thruster
        +BattleEntityMovementController MovementController
        +void SetEntityData()
        +Vector2 GetVelocity()
        +void SetVelocity()
        -void InitializeWeaponMounts()
        +ICollection~WeaponMount~ GetWeaponMounts()
    }
    
    class ProjectileEntity{
        +ProjectileEntityData EntityData
        +Sprite2D ProjectileSprite
        +BaseBattleEntity Source
        -float _remainingLifetime
        -Vector2 _velocity
        +void Initialize()
        +void SetVelocity()
        -void CheckCollision()
        +void HandleImpact()
    }
    
    class BaseBattleEntityData{
        +string Id
        +string Name
        +float MaxHealth
        +Allegiance Allegiance
        +BaseEntityType BaseType
        +BaseBattleEntityData Clone()
        +void ApplyState()
    }
    
    class ShipEntityData{
        +float MaxSpeed
        +float Acceleration
        +float Deceleration
        +float DragFactor
        +float RotationSpeed
        +ShipType ShipDetailType
    }
    
    class ProjectileEntityData{
        +float Speed
        +float Lifetime
        +float BaseDamage
        +bool DestroyOnImpact
        +float AreaEffectRadius
        +ProjectileType ProjectileType
    }
```

## Core Components

BaseBattleEntity: Base node for all battle entities with health and allegiance management.
ShipEntity: Controllable ships with weapons, movement systems, and thrusters.
ProjectileEntity: Projectiles with collision detection, damage, and lifetime handling.

## Data Structure

BaseBattleEntityData: Core properties for all entities.
ShipEntityData: Ship-specific properties with movement physics.
ProjectileEntityData: Projectile-specific properties like damage and lifetime.
