# State Machine System Overview

```mermaid
graph TD
    Entity[BattleEntity] --> SMC[StateMachineControlComponent]
    SMC --> |registers| ESM[EscortStateMachine]
    SMC --> |registers| OSM[OtherStateMachine]
    
    EBC[EscortBehaviorComponent] --> |initializes| ESM
    OBC[OtherBehaviorComponent] --> |initializes| OSM
    
    ESM --> |contains| ES1[FollowingState]
    ESM --> |contains| ES2[ProtectingState]
    ESM --> |contains| ES3[WanderingState]
    
    ES1 --> |transitions| ES2
    ES2 --> |transitions| ES1
    ES1 --> |transitions| ES3
    ES3 --> |transitions| ES1
    
    EBC --> |communicates with| SMC
    ESM --> |notifies| EBC
```

## Core Components

- **StateMachineControlComponent**: Central manager for all state machines on an entity
- **BehaviorComponent**: Type-specific component that creates and manages a specific state machine
- **StateMachine**: Strongly-typed FSM that manages state transitions
- **StateBase**: Base class for all states in a state machine

## Interaction Flow

1. Entity has a **StateMachineControlComponent** and one or more **BehaviorComponents**
2. Each **BehaviorComponent** creates its specific **StateMachine** and registers it with the control component
3. **StateMachine** contains and manages transitions between concrete **State** instances
4. States communicate with their parent **StateMachine** via strongly-typed context
5. **StateMachine** communicates with its owner **BehaviorComponent** for entity-level decisions

## Creating New State Machines

1. Define a state key enum (e.g., `AttackStateKey`)
2. Create concrete state classes extending `StateBase<TStateMachine, TStateKey>`
3. Create a state machine class extending `StateMachine<TOwnerController, TStateKey, TSelfContext>`
4. Create a behavior component to initialize and manage the state machine
