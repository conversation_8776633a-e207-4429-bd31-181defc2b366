# Escort Ship AI Behavior System

This document outlines the AI behavior system for escort ships, utilizing a strongly-typed state machine approach.

## Architecture Overview

The system relies on these key components:

* **`StateMachineControlComponent`**: A central manager that registers and activates different state machines on an entity.
* **`EscortBehaviorComponent`**: Owns and initializes the `EscortStateMachine`, handles communication from the state machine.
* **`EscortStateMachine`**: A strongly-typed state machine that manages escort-specific states.
* **State Classes**: (e.g., `FollowingState`, `ProtectingState`, `WanderingState`): Individual state implementations that derive from `StateBase`.

## System Interaction Diagram

```mermaid
graph TD
    A[External Command] -->|SetEscortTarget| B(EscortBehaviorComponent);
    B -->|initializes & owns| C(EscortStateMachine);
    D(StateMachineControlComponent) -->|registers & activates| C;
    
    C -->|contains| E[FollowingState];
    C -->|contains| F[ProtectingState];
    C -->|contains| G[WanderingState];
    
    E -->|transitions| F;
    F -->|transitions| E;
    E -->|transitions| G;
    G -->|transitions| E;
    
    C -.->|NotifyEscortTargetLost| B;
    
    subgraph Escort Ship Entity
        B
        D
    end
```

*`EscortBehaviorComponent` initializes the `EscortStateMachine` and registers it with `StateMachineControlComponent`. States within the state machine handle transitions automatically based on conditions.*

## EscortStateMachine States

The `EscortStateMachine` manages the following states:

```mermaid
stateDiagram-v2
    [*] --> Wandering
    
    Wandering --> Following: EscortTarget set
    Following --> Protecting: Target under attack
    Following --> Wandering: Target lost
    
    Protecting --> Following: Threat eliminated
    Protecting --> Returning: Too far from target
    Protecting --> Wandering: Target lost
    
    Returning --> Following: Reached target
    Returning --> Wandering: Target lost
```

### State Descriptions

* **Wandering**: Default state when no escort target is assigned. Ship moves randomly within an area.
* **Following**: Follows the escorted entity at a safe distance.
* **Protecting**: Positions between the escorted entity and a threat, engaging the threat.
* **Returning**: Returns to the escorted entity if it has moved too far away during protection.

### State Transitions

Transitions occur automatically based on conditions evaluated in each state's `GetNextStateKey()` method:

This system allows for flexible and extendable AI behaviors through strongly-typed state machines. New behavior types can be created by:

1. Defining a new state key enum (e.g., `AttackStateKey`)
2. Creating concrete state classes extending `StateBase<TStateMachine, TStateKey>`
3. Creating a state machine class extending `StateMachine<TOwnerController, TStateKey, TSelfContext>`
4. Creating a behavior component to initialize and manage the state machine

See `state_machine_system_overview.md` for more details on the architecture.
