# Player Fleet System Overview

This document describes the Player Fleet Composition system, which allows defining the player's fleet structure and individual ship configurations through data.

## Core Concept

The system relies on two primary data structures, `PlayerFleetComposition` and `FleetShipData` (persisted as part of `GameState`), to define the player's fleet. When a battle or relevant game segment begins, this data is used to spawn and configure the player's ships.

## Data Flow & Spawning Process

```mermaid
graph TD
    A[GameStateManager] --> B(GameState)
    B --> C(PlayerFleet: PlayerFleetComposition)
    C --> F_Data(Flagship: FleetShipData)
    C --> E_Data_List(EscortShips: List<FleetShipData>)
    E_Data_List --> E_Data(Escort: FleetShipData)
    
    subgraph BattleManager.SpawnPlayerFleet
        direction LR
        BM[BattleManager]
        BER[BattleEntityRegistry]
        POS[PropertyOverrideService]

        C -- Fleet Data --> BM
        BM -- Flagship EntityId --> BER
        BER -- Base Flagship --> BM
        BM -- Flagship Instance + Overrides --> POS
        POS -- Modified Flagship --> BM
        BM -- Add to Scene --> FlagshipInstance[Flagship Instance]

        BM -- Escort EntityId --> BER
        BER -- Base Escort --> BM
        BM -- Escort Instance + Overrides --> POS
        POS -- Modified Escort --> BM
        BM -- Add to Scene --> EscortInstance[Escort Instance]
    end

    FlagshipInstance --> H[Player's Active Fleet in Scene]
    EscortInstance --> H
```

**Process Explanation:**

1.  **Retrieve Data**: The `BattleManager.SpawnPlayerFleet(Vector2 flagshipSpawnPosition)` method is called. It accesses the `PlayerFleet` object from the current `GameState` (via `GameStateManager.Instance.CurrentGameState`).
2.  **Instantiate Flagship**: 
    *   The `EntityId` from `PlayerFleet.Flagship` is used by `BattleManager` to request instantiation from the `BattleEntityRegistry`.
    *   The base ship is instantiated.
    *   Any `PropertyOverrides` defined in `PlayerFleet.Flagship.PropertyOverrides` are applied to the instantiated flagship by `BattleManager` using the `PropertyOverrideService.Instance.ApplyOverrides()` method.
3.  **Instantiate Escorts**: For each `FleetShipData` in `PlayerFleet.EscortShips`:
    *   The `EntityId` is used by `BattleManager` to instantiate the base escort ship via `BattleEntityRegistry`.
    *   The `RelativePosition` is applied by `BattleManager` to position the escort ship relative to the flagship's spawn point.
    *   Any `PropertyOverrides` are applied to the instantiated escort ship by `BattleManager` using `PropertyOverrideService.Instance.ApplyOverrides()`.
4.  **Fleet Deployed**: The fully configured flagship and escort ships are registered with the battle scene and added as children to `BattleManager`, forming the player's active fleet.

## Property Overrides

-   **Purpose**: The `PropertyOverrides` dictionary (`Dictionary<string, object>`) within each `FleetShipData` allows for fine-grained customization of individual ships in the fleet without needing to create entirely new base ship definitions.
-   **Application**: When `BattleManager.SpawnPlayerFleet` processes a ship, it passes the instantiated ship and its `PropertyOverrides` dictionary to `PropertyOverrideService.Instance.ApplyOverrides()`. The service then iterates through each key-value pair:
    *   The `key` (string) represents the name of the property on the ship instance to be modified.
    *   The `value` (object) is the new value for that property.
-   **Mechanism**: The `PropertyOverrideService` uses reflection and type conversion logic to set the properties on the target `GodotObject` (e.g., the ship instance or one of its `Resource` properties).

## Key Components

-   **`GameState.PlayerFleet`**: Holds the current fleet configuration.
-   **`PlayerFleetComposition.cs`**: Defines the overall fleet structure (flagship, escorts).
-   **`FleetShipData.cs`**: Defines individual ship data (`EntityId`, relative position, overrides).
-   **`BattleEntityRegistry.cs`**: Provides base ship scenes/stats based on `EntityId`.
-   **`BattleManager.cs` (specifically `SpawnPlayerFleet` method)**: Orchestrates the instantiation, positioning, and configuration process, utilizing `BattleEntityRegistry` and `PropertyOverrideService`.
-   **`PropertyOverrideService.cs`**: Applies property overrides to ship instances based on the provided dictionary.
