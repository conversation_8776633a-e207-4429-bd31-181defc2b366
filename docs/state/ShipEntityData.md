# ShipEntityData

Primary data container for ship entities, defining movement properties and weapon loadouts.

```mermaid
classDiagram
    class ShipEntityData {
        +float MaxSpeed
        +float Acceleration
        +float Deceleration
        +float DragFactor
        +float RotationSpeed
        +float MovementThreshold
        +float RotationThreshold
        +float SlowingDistance
        +ShipType ShipDetailType
        +Array~WeaponSlotData~ WeaponSlots
    }
```

- **Movement Physics**: MaxSpeed, Acceleration, Deceleration, DragFactor, RotationSpeed
- **Movement Thresholds**: MovementThreshold, RotationThreshold, SlowingDistance
- **Ship Classification**: ShipDetailType (Fighter, Corvette, etc.)
- **Armament**: WeaponSlots array defining what weapons mount where
