# Enemy Behavior Configuration

Defines behavior parameters for enemy ships.

## Structure

```
EnemyBehaviorConfiguration
├─ BehaviorName: string - Identifier for this behavior set
├─ MovementBehaviorType: enum - How the ship navigates
├─ TargetingBehaviorType: enum - How targets are selected
├─ WeaponBehaviorType: enum - How weapons are utilized
├─ Movement Parameters
│  ├─ ThrustStrength: float - Forward acceleration power
│  ├─ RotationSpeed: float - Turn rate
│  ├─ OptimalDistance: float - Preferred distance from target
│  ├─ CircleDistance: float - Orbit radius
│  └─ CircleSpeed: float - Orbit velocity
├─ Targeting Parameters
│  ├─ DetectionRange: float - Maximum target detection distance
│  ├─ TargetUpdateInterval: float - Seconds between target reassessments
│  └─ PriorityShipTypes: ShipType[] - Preferred target types
└─ Weapon Parameters
   ├─ FiringAngleThreshold: float - Maximum angle for weapon firing
   ├─ OptimalFiringDistance: float - Preferred firing range
   ├─ DistanceTolerance: float - Acceptable range deviation
   ├─ BurstDuration: float - Firing period length
   └─ CooldownDuration: float - Wait period between bursts
```
