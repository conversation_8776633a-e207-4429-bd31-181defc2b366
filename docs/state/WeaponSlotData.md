# WeaponSlotData

Defines a specific weapon to be mounted at a designated point on a ship.

```mermaid
classDiagram
    class WeaponSlotData {
        +string MountPointId
        +string WeaponId
        +bool DisplayVisuals
    }
```

- **MountPointId**: ID matching a `WeaponMount` node on the ship scene
- **WeaponId**: ID of the `WeaponData` resource to spawn
- **DisplayVisuals**: Controls visibility of weapon's visual components
