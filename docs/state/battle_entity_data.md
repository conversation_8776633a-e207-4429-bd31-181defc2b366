# Battle Entity Data Structures

```mermaid
classDiagram
    BaseBattleEntityData <|-- ShipEntityData
    BaseBattleEntityData <|-- ProjectileEntityData
    ProjectileEntity o-- ProjectileBehavior
    ProjectileBehavior <|-- StraightMovementBehavior
    ProjectileBehavior <|-- HomingBehavior
    ProjectileBehavior <|-- SpinningBehavior
    
    class BaseBattleEntityData{
        +string Id: Unique identifier
        +string Name: Display name
        +float MaxHealth: Maximum health points
        +Allegiance Allegiance: Team alignment
        +BaseEntityType BaseType: Entity category
    }
    
    class ShipEntityData{
        +float MaxSpeed: Top speed
        +float Acceleration: Speed gain rate
        +float Deceleration: Speed decrease rate
        +float DragFactor: Passive slowdown
        +float RotationSpeed: Turn rate
        +ShipType ShipDetailType: Ship classification
    }
    
    class ProjectileEntityData{
        +float Speed: Movement speed
        +float Lifetime: Self-destruct timer
        +float BaseDamage: Damage on impact
        +bool DestroyOnImpact: Remove after collision
        +float AreaEffectRadius: Splash damage range
        +ProjectileType ProjectileType: Projectile classification
    }
    
    class ProjectileBehavior{
        <<abstract>>
        +UpdateBehavior(ProjectileEntity, double)
    }
```

Data structures defining properties for battle objects. Base class provides common attributes like health and allegiance, while specialized derived classes add type-specific properties for ships (movement physics) and projectiles (damage behavior). ProjectileEntity now uses composable behavior nodes to control movement and special effects.
