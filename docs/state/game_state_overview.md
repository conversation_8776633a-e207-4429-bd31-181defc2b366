# GameState Overview

This document provides a high-level overview of the `GameState.cs` class structure and its key properties. For detailed information on specific complex properties, refer to their dedicated state documentation files.

## Core Properties

-   `Id`: (string) Unique identifier for the game state instance.
-   `DataVersion`: (int) Version number for the game state data format, used for migration and compatibility.
-   `Factions`: (`Dictionary<FactionId, FactionState>`) Stores data for all factions in the game. See `faction_state.md` (if available) for `FactionState` details.
-   `MetaProgress`: (`MetaProgressionData`) Contains data related to player's overall progression. See `meta_progression_data.md` (if available) for details.
-   `CurrentUniverse`: (`UniverseModel`) Holds the model of the current game universe, including celestial bodies and their states.

## Player Fleet Composition

-   **`PlayerFleet`**: (`PlayerFleetComposition`)
    *   **Purpose**: Stores the composition of the player's fleet, including the flagship and escort ships, their relative positions, and any property overrides.
    *   **Details**: This data is used to determine how the player's fleet is constructed and configured at the start of encounters or when deploying into a game scene.
    *   For a detailed breakdown of `PlayerFleetComposition` and its constituent `FleetShipData`, please refer to [`player_fleet_composition_state.md`](./player_fleet_composition_state.md).
