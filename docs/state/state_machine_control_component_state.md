# State Machine Control Component State

Manages active high-level behavior state machines for an entity.

```mermaid
graph TD
    SMC[StateMachineControlComponent] --> |stores| RD[RegisteredDictionary]
    SMC --> |tracks| AS[ActiveSet]
    
    RD --> |Type→Node mapping| SM1[StateMachine1]
    RD --> |Type→Node mapping| SM2[StateMachine2]
    
    AS --> |contains Types of| SM1
```

## Key Data
- Dictionary of registered state machines by Type
- Set of active state machine Types
