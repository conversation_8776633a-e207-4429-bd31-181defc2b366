# Escort Behavior State

Defines the state for an entity's escort-specific behavior logic.

```mermaid
graph TD
    EBC[EscortBehaviorComponent] --> |references| EE[EscortedEntity]
    EBC --> |owns| ESM[EscortStateMachine]
    ESM --> |tracks| CS[CurrentState]
    ESM --> |references| CT[CurrentThreat]
    ESM --> |stores| IUA[IsTargetUnderAttack]
```

## Key Data
- EscortedEntity: Reference to the entity being escorted
- CurrentThreat: Reference to the current threat to the escorted entity
- IsTargetUnderAttack: Boolean indicating if the escorted entity is under attack
