# BattleEntityStateData Structure

`BattleEntityStateData` stores the dynamic, serializable state of a battle entity instance.

## Properties

*   `string Id`: Unique identifier for the entity instance.
*   `string Name`: Display name of the entity.
*   `float MaxHealth`: Maximum health points.
*   `float Speed`: Movement speed.
*   `float RotationSpeed`: Rotational speed.
*   `Allegiance Allegiance`: Faction or team alignment. (Note: `Allegiance` is an enum, likely from `SpaceGame.Scripts.Battle.Allegiance`)
*   `string TexturePath`: Path to the entity's visual texture.
*   `bool UseParticleThrusters`: Flag for thruster particle effects.
*   `float CollisionRadius`: Radius for collision detection.
*   `BaseEntityType BaseType`: Categorizes the entity's fundamental type (e.g., Ship, Projectile). (Note: `BaseEntityType` is an enum from `SpaceGame.Scripts.Battle.Enums.BaseEntityType`)
*   `ShipType ShipDetailType`: Specifies the particular type of ship if `BaseType` is `Ship` (e.g., <PERSON>, Scout). This field is relevant only when `BaseType` is `Ship`. (Note: `ShipType` is an enum from `SpaceGame.Scripts.ShipType`)

## Usage

1.  **Saving State**: When the game needs to be saved, relevant data from active `BattleEntityData` instances (or their runtime counterparts) is transferred to `BattleEntityStateData` objects. These state objects are then serialized (e.g., to JSON) as part of the overall game save file.
2.  **Loading State**: When a game save is loaded, `BattleEntityStateData` objects are deserialized from the save file. Then, for each entity:
    *   A `BattleEntityData` resource might be loaded (if it's a template).
    *   The `ApplyState(BattleEntityStateData stateData)` method on the `BattleEntityData` instance is called, passing the deserialized `BattleEntityStateData`. This updates the `BattleEntityData` with the saved values.

## Relationship with `BattleEntityData`

-   `BattleEntityData` is now a `Godot.Resource`. It can be created and configured in the Godot editor. It contains the `ApplyState` method to receive state updates.
-   `BattleEntityStateData` is a simple C# class used purely for holding data to be serialized. It does not have Godot-specific behaviors.

This separation allows `BattleEntityData` to be used as a template or base configuration, while `BattleEntityStateData` captures the specific dynamic state of an entity instance during gameplay.
