# Player Fleet Composition State (`PlayerFleetComposition.cs`, `FleetShipData.cs`)

This document outlines the structure of data objects used to define the player's fleet composition. These are primarily intended for serialization and for configuring the player's fleet within `GameState.cs`.

## `PlayerFleetComposition.cs`

**Purpose**: Represents the entirety of the player's fleet.

**Structure**:

```mermaid
graph TD
    PFC[PlayerFleetComposition] --> FSDA[Flagship: FleetShipData]
    PFC --> FSDList[EscortShips: List&lt;FleetShipData&gt;]
    FSDList --> FSDZ[FleetShipData ...]
```

**Properties**:

-   **`Flagship`**: `FleetShipData`
    *   Role: Defines the player's main command ship.
-   **`EscortShips`**: `List<FleetShipData>`
    *   Role: A collection of `FleetShipData` objects, each defining an escort ship in the fleet.

## `FleetShipData.cs`

**Purpose**: Represents data for a single ship within the fleet.

**Structure**:

```mermaid
graph TD
    FSD[FleetShipData] --> EntityId[EntityId: string]
    FSD --> RelPos[RelativePosition: Vector2]
    FSD --> PropOverrides[PropertyOverrides: Dictionary&lt;string, object&gt;]
```

**Properties**:

-   **`EntityId`**: `string`
    *   Role: Identifier used to look up the base ship definition (e.g., in `BattleEntityRegistry`). This determines the ship's type/class.
-   **`RelativePosition`**: `Godot.Vector2`
    *   Role: Specifies the ship's position relative to the flagship's spawn point. Not typically used for the flagship itself.
-   **`PropertyOverrides`**: `Dictionary<string, object>`
    *   Role: A flexible dictionary to customize specific properties of the instantiated ship (e.g., modify weapon stats, health, speed). Keys are property names, values are the new values.
