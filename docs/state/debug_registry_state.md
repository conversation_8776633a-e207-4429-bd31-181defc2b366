# Debug Registry State

The `DebugRegistry` maintains a dictionary of registered objects for runtime inspection.

```mermaid
classDiagram
    class DebugRegistry {
        -Dictionary~string, (object, string)~ registeredObjects
    }
```

## Structure

- **Key**: Unique string identifier for each registered object
- **Object**: The actual object instance being registered
- **Category**: String grouping for organizational purposes

Objects are stored by key and can be retrieved by key or filtered by category.
