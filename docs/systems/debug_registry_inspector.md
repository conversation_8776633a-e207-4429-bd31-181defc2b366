# Debug Registry and ImGui Inspector

```mermaid
graph TD
    A[DebugRegistry] -- "Stores objects" --> B[Registered Objects]
    C[Game Systems] -- "Register objects" --> A
    A -- "Notifies changes" --> D[DebugInspectorPanel]
    D -- "Retrieves objects" --> A
    D -- "Inspects members" --> E[ReflectionHelper]
    D -- "Renders UI" --> F[ImGui]
    E -- "Modifies objects" --> B
```

## Purpose

The Debug Registry system allows runtime inspection and modification of game objects through an ImGui interface.

## Usage

### Registering Objects

```csharp
// Register an object with a unique key and optional category
DebugRegistry.RegisterObject("playerShip", playerShipEntity, "Ships");

// Unregister when no longer needed
DebugRegistry.UnregisterObject("playerShip");
```

### Accessing the Inspector

Press F1 to toggle the debug UI. Select objects from the left panel to inspect and modify their properties in the right panel.
