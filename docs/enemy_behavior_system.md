# Enemy Behavior System

```mermaid
graph TD
    ShipEntityData[ShipEntityData] -->|references| DefaultBehavior[EnemyBehaviorConfiguration]
    EntityRegistryData[EntityRegistryData] -->|contains| BehaviorRegistry[EditorRegisteredBehaviors]
    EntityRegistryData -->|contains| FallbackBehavior[DefaultBehavior]
    BattleEntityFactory[BattleEntityFactory] -->|creates| EnemyShip[Enemy Ship Entity]
    BattleManager[BattleManager] -->|calls| AttachDefaultBehavior[AttachDefaultBehavior]
    AttachDefaultBehavior -->|applies to| EnemyShip
    EnemyShip -->|has| ShipData[ShipEntityData]
    ShipData -->|checked for| DirectBehavior[Direct Behavior Reference]
    EntityRegistry[BattleEntityRegistry] -->|provides| TypeBasedBehavior[Type-Based Behavior]
    DirectBehavior -->|if exists| BehaviorController[ShipBehaviorController]
    TypeBasedBehavior -->|if no direct behavior| BehaviorController
    FallbackBehavior -->|if no type behavior| BehaviorController
```

## Behavior Attachment Flow

```mermaid
flowchart TD
    A[Enemy Ship Spawned] --> B{Has Direct\nDefaultBehavior?}
    B -->|Yes| C[Use Ship's DefaultBehavior]
    B -->|No| D{Registry has behavior\nfor ship type?}
    D -->|Yes| E[Use Type-Based Behavior]
    D -->|No| F[Use Registry's DefaultBehavior]
    C --> G[Create and Attach\nBehaviorController]
    E --> G
    F --> G
```

## Key Components

- **ShipEntityData**: Contains direct reference to a default behavior configuration
- **EntityRegistryData**: Stores editor-registered behaviors and a fallback default behavior
- **BattleEntityRegistry**: Provides behavior lookup by entity type ID
- **BattleEntityFactoryExtensions**: Contains the AttachDefaultBehavior method
- **BattleManager**: Automatically applies behaviors when spawning enemy ships
