# Projectile Behavior System

```mermaid
graph TD
    PE[ProjectileEntity] --> PB[ProjectileBehavior]
    PB --> SMB[StraightMovementBehavior]
    PB --> HB[HomingBehavior]
    PB --> SB[SpinningBehavior]
    PB --> OB[Other Behaviors...]
```

## System Overview

ProjectileEntity delegates movement and special behaviors to composable behavior nodes.

## Behavior Composition

Multiple behaviors can be attached to a single projectile, either:
- In scene templates
- At runtime via BattleEntityFactory

## Core Components

- **ProjectileBehavior**: Abstract base class with UpdateBehavior method
- **StraightMovementBehavior**: Basic linear movement
- **HomingBehavior**: Target-seeking with configurable turning rate
- **SpinningBehavior**: Rotation independent of movement direction
