# Ship Trail System

## Overview

The Ship Trail System provides visually appealing movement trails for spaceships in the battle system. It consists of two main components:

1. **ShipTrailComponent** - Creates smooth, fading Line2D trails that show the ship's movement path
2. **Enhanced ThrusterComponent** - Improved thruster effects with primary and secondary particle systems

## Features

### ShipTrailComponent

- **Smooth Line2D Trails**: Uses Godot's Line2D with gradient effects for smooth, anti-aliased trails
- **Velocity-Based Activation**: Trails only appear when ships are moving above a configurable threshold
- **Configurable Fading**: Trails fade out over time with customizable fade rates
- **Performance Optimized**: Automatically manages trail point count and removes old points
- **Customizable Appearance**: Trail color, width, and length can be configured per ship

### Enhanced ThrusterComponent

- **Dual Particle Systems**: Primary thrusters for normal movement, secondary for high-intensity movement
- **Intensity-Based Effects**: Particle emission scales with thruster intensity
- **Improved Particle Materials**: Better-looking particle effects with gradients and proper scaling
- **Threshold-Based Secondary Effects**: Secondary thrusters activate only at high intensity levels

## Usage

### Adding Trails to Ships

1. **Automatic Setup**: Ships using `ShipEntity.tscn` as a base automatically include the trail component
2. **Manual Setup**: Add `ShipTrailComponent.tscn` as a child node to any ship
3. **Configuration**: Adjust trail properties in the inspector or via code

### Trail Configuration

```gdscript
# Access the trail component
var trail = ship.TrailComponent as ShipTrailComponent

# Configure trail appearance
trail.SetTrailColor(Color.CYAN)
trail.SetTrailWidth(4.0)
trail.TrailEnabled = true

# Clear trails
trail.ClearTrail()
```

### Thruster Configuration

```gdscript
# Access the thruster component
var thruster = ship.Thruster

# Set thruster mode to particles for better effects
thruster.Mode = ThrusterComponent.ThrusterMode.Particle

# Configure secondary thruster threshold
thruster.SecondaryThrusterThreshold = 0.7
```

## Configuration Parameters

### ShipTrailComponent

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `MaxTrailPoints` | int | 50 | Maximum number of points in the trail |
| `MinPointDistance` | float | 5.0 | Minimum distance before adding a new trail point |
| `TrailWidth` | float | 3.0 | Width of the trail line |
| `TrailColor` | Color | Light Blue | Color of the trail |
| `FadeRate` | float | 0.02 | How quickly the trail fades (0.0 = never, 1.0 = immediate) |
| `MinVelocityThreshold` | float | 10.0 | Minimum velocity to show trails |
| `TrailEnabled` | bool | true | Whether trails are enabled |

### Enhanced ThrusterComponent

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `Mode` | ThrusterMode | Sprite | Sprite or Particle mode |
| `SecondaryThrusterThreshold` | float | 0.7 | Intensity threshold for secondary effects |
| `MaxIntensity` | float | 1.0 | Maximum thruster intensity |

## Ship-Specific Configurations

### Scout Ship

- **Trail Color**: Light blue (`Color(0.2, 0.6, 1, 0.7)`)
- **Trail Width**: 2.5 pixels
- **Max Points**: 40
- **Secondary Thruster Threshold**: 0.6 (activates earlier for agile ship)

### Fighter Ship

- **Trail Color**: Orange-red (for aggressive appearance)
- **Trail Width**: 3.0 pixels
- **Max Points**: 50
- **Secondary Thruster Threshold**: 0.7

## Testing

Use the `TrailTestScene.tscn` to test and preview trail effects:

1. Open `SpaceGame/Scenes/Debug/TrailTestScene.tscn`
2. Run the scene
3. Use WASD to move the ship and see trails in action
4. Use Q/E to rotate
5. Press T to toggle trails on/off
6. Press R to clear trails

## Performance Considerations

- **Trail Points**: Keep `MaxTrailPoints` reasonable (30-60) for good performance
- **Update Frequency**: Trails update every frame but use efficient algorithms
- **Memory Management**: Old trail points are automatically cleaned up
- **Particle Count**: Secondary thrusters use fewer particles to maintain performance

## Integration with Existing Systems

The trail system integrates seamlessly with:

- **Movement Controllers**: Automatically responds to ship velocity changes
- **Battle System**: Works with all existing ship types and behaviors
- **Visual Effects**: Complements existing thruster and weapon effects
- **AI Systems**: No changes needed - trails automatically follow AI-controlled ships

## Customization Examples

### Creating Custom Trail Colors by Allegiance

```gdscript
func SetTrailColorByAllegiance(ship: ShipEntity):
    var trail = ship.TrailComponent as ShipTrailComponent
    if trail == null:
        return
        
    match ship.CurrentAllegiance:
        Allegiance.PlayerForces:
            trail.SetTrailColor(Color.CYAN)
        Allegiance.EnemyForces:
            trail.SetTrailColor(Color.RED)
        Allegiance.NeutralForces:
            trail.SetTrailColor(Color.YELLOW)
```

### Dynamic Trail Intensity

```gdscript
func UpdateTrailIntensity(ship: ShipEntity):
    var trail = ship.TrailComponent as ShipTrailComponent
    if trail == null:
        return
        
    # Make trails more visible at higher speeds
    var speed = ship.Velocity.Length()
    var maxSpeed = ship.EntityData.MaxSpeed
    var intensity = speed / maxSpeed
    
    var color = trail.TrailColor
    color.A = 0.3 + (intensity * 0.5)  # Alpha from 0.3 to 0.8
    trail.SetTrailColor(color)
```

## Future Enhancements

Potential improvements for the trail system:

1. **Texture-Based Trails**: Support for textured trails instead of solid colors
2. **Trail Branching**: Multiple trail lines for ships with multiple engines
3. **Environmental Effects**: Trails that react to space weather or fields
4. **Trail Persistence**: Trails that last longer in certain areas
5. **Performance Profiling**: Built-in performance monitoring and auto-adjustment
