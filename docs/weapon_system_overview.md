# Weapon System Overview

The weapon system enables ships to be equipped with customizable weapons that can be attached to specific mounting points.

```mermaid
graph TD
    ShipEntityData[ShipEntityData] -->|contains| WeaponSlots[WeaponSlots Array]
    WeaponSlots -->|references| WeaponSlotData[WeaponSlotData]
    WeaponSlotData -->|defines| MountPoint[Mount Point ID]
    WeaponSlotData -->|references| WeaponID[Weapon ID]
    
    BattleEntityRegistry[BattleEntityRegistry] -->|stores| WeaponData[WeaponData]
    WeaponID -->|looked up in| BattleEntityRegistry
    
    ShipWeaponController[ShipWeaponController] -->|uses| WeaponSlots
    ShipWeaponController -->|finds| WeaponMount[WeaponMount]
    ShipWeaponController -->|spawns via| BattleEntityFactory[BattleEntityFactory]
    
    WeaponMount -->|receives| SpawnedWeapon[Spawned Weapon]
    BattleEntityFactory -->|instantiates| SpawnedWeapon
    
    PropertyOverrideService[PropertyOverrideService] -->|modifies| SpawnedWeapon
```

## Core Components

- **ShipEntityData**: Contains weapon loadout information via its `WeaponSlots` array.
- **WeaponSlotData**: Links a specific weapon to a specific mount point.
- **WeaponData**: Defines weapon properties and scene to instantiate.
- **BattleEntityRegistry**: Stores and retrieves weapon definitions by ID.
- **BattleEntityFactory**: Spawns weapon instances using data from the registry.
- **ShipWeaponController**: Orchestrates weapon mounting by finding mount points and spawning weapons.
- **WeaponMount**: Attachment point for a weapon on a ship.
- **PropertyOverrideService**: Applies customizations to spawned weapons.

## Workflow

1. Ship definition includes weapon slots with mount point IDs and weapon IDs
2. ShipWeaponController finds all weapon mounts on the ship
3. For each weapon slot, the controller:
   - Locates the corresponding mount point
   - Requests the weapon data from the registry
   - Spawns the weapon via the factory
   - Attaches the weapon to the mount
   - Applies any property overrides

## Implementation Notes

- Weapons are automatically spawned and mounted when a ship is created
- Weapons can have their visuals hidden via the `DisplayVisuals` property
- Property overrides can customize weapon behavior and appearance without modifying the base weapon definition
