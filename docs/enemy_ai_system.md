# Enemy AI System

```mermaid
graph TD
    ShipEntity --> ShipBehaviorController
    ShipBehaviorController --> TargetingBehaviorComponent
    Ship<PERSON>ehaviorController --> MovementBehaviorComponent
    Ship<PERSON>ehaviorController --> WeaponBehaviorComponent
    TargetingBehaviorComponent --> |"Finds targets"| CurrentTarget
    MovementBehaviorComponent --> |"Uses"| CurrentTarget
    WeaponBehaviorComponent --> |"Attacks"| CurrentTarget
    EnemyBehaviorFactory --> |"Creates"| ShipBehaviorController
    EnemyBehaviorConfiguration --> |"Configures"| EnemyBehaviorFactory
```

## Component Interaction

```mermaid
sequenceDiagram
    participant Ship as ShipEntity
    participant Controller as ShipBehaviorController
    participant Targeting as TargetingBehaviorComponent
    participant Movement as MovementBehaviorComponent
    participant Weapon as WeaponBehaviorComponent
    
    Ship->>Controller: _PhysicsProcess(delta)
    Controller->>Targeting: UpdateBehavior(ship, delta)
    Targeting->>Targeting: Find target
    Controller->>Movement: UpdateBehavior(ship, delta)
    Movement->>Targeting: GetCurrentTarget()
    Movement->>Ship: SetThrust/Rotate
    Controller->>Weapon: UpdateBehavior(ship, delta)
    Weapon->>Targeting: GetCurrentTarget()
    Weapon->>Ship: FireWeapons
```

## Behavior Types

- **Movement**: Intercept, Circle
- **Targeting**: Proximity, Priority
- **Weapon**: Opportunistic, Strategic

## Configuration

Enemy behaviors are configured via `EnemyBehaviorConfiguration` resources, which can be created in the editor or loaded from JSON.

```mermaid
graph LR
    JSON[JSON Override] --> |PopulateFromJson| Config[EnemyBehaviorConfiguration]
    Config --> Factory[EnemyBehaviorFactory]
    Factory --> |CreateBehaviorController| Controller[ShipBehaviorController]
```
