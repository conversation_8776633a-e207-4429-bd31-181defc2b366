# Property Override System

This document outlines the architecture for overriding `BattleEntity` properties using external JSON configuration files. The system now offers two approaches: the original reflection-based method and a new type-safe approach using the `IJsonPopulatable` interface.

## System Interaction

The following diagram shows how components interact to load and apply property overrides:

```mermaid
graph TD
    A[Game Initialization] -- Calls --> B(OverrideManager.InitializeOverrides);
    B -- Loads & Parses --> C{JSON Files in res://Data/Overrides/};
    C -- Populates --> D[OverrideManager._globalOverrides Cache];
    E[Entity Spawning/Creation] -- Requests Overrides For --> B;
    B -- Retrieves From --> D;
    
    %% Decision point for which approach to use
    B -- Checks if entity implements --> H{IJsonPopulatable?};
    H -- Yes --> I[Use PopulateFromJson];
    H -- No --> J[Use PropertyOverrideServiceV2];
    
    I -- Type-safe property updates --> G(BattleEntity Instance);
    J -- Reflection-based updates --> G;
```

**Flow:**
1.  During game initialization (`Game.cs`), `OverrideManager.InitializeOverrides()` is called.
2.  `OverrideManager` reads all `*.json` files from `res://Data/Overrides/` (and potentially `user://overrides/`).
3.  Each JSON file is parsed (expecting `JsonOverrideFileFormat`), and its `entity_overrides` are stored in a global cache (`_globalOverrides`) within `OverrideManager`. Entity IDs (e.g., "Fighter") from the JSON are stored and looked up case-insensitively (typically normalized to lowercase internally).
4.  When a `BattleEntity` is instantiated, the spawning system (e.g., `BattleEntityFactory`) calls `entity.SetRegistryId(registryId)` to assign its unique registry identifier. Subsequently, in its `_Ready()` method, the `BattleEntity` calls `OverrideManager.ApplyGlobalOverrides(this, _battleEntityRegistryId)` to apply the properties.
5.  `OverrideManager` retrieves the specific overrides for the given `_battleEntityRegistryId` (case-insensitively) from its cache.
6.  The `OverrideManager` then checks if the entity implements the `IJsonPopulatable` interface:
    - If it does, it uses the `PopulateFromJson` method directly for type-safe property updates.
    - If not, it falls back to `PropertyOverrideServiceV2.ApplyOverrides()` which uses reflection but with improved handling of nested objects.

*Note: The `BattleEntity.EntityTypeId` public property has been removed. Entity identification for overrides is now handled via the `SetRegistryId(string registryId)` method (called by the spawning system) and an internal `_battleEntityRegistryId` field within `BattleEntity`.* 

## JSON Override File Structure

Override files define which properties of which entity types should be changed. The structure is as follows:

```json
{
  "entity_overrides": {
    "YourEntityRegistryId_1": { // e.g., "Fighter"
      "PropertyName1": "value1", // Direct property of the entity
      "NestedObjectProperty": {   // e.g., "EntityData"
        "NestedPropertyA": 123.45,
        "NestedPropertyB": "some_string"
      },
      "AnotherProperty": true,
      "EnumProperty": "EnumValueAsString"
    },
    "YourEntityRegistryId_2": {
      "PropertyNameX": "some_other_value"
    }
  }
}
```

-   The top-level key is `"entity_overrides"`.
-   This contains a dictionary where each key is a **Registry ID** (string, e.g., "Fighter") identifying the entity type. While these IDs are matched case-insensitively by the system, using a consistent casing (e.g., PascalCase like `Fighter`) in JSON files is recommended for readability.
-   Each Registry ID maps to another dictionary where keys are property names (strings). 
    -   If the value is a simple type (string, number, boolean), it's set directly on the entity.
    -   If the value is an object (another dictionary), it represents a nested property (e.g., `EntityData`). The `PropertyOverrideService` will get this nested object and recursively apply the overrides within it.
-   `PropertyOverrideServiceV2` attempts to convert values to appropriate types using `System.Text.Json` and falls back to reflection when needed.
-   Classes implementing `IJsonPopulatable` handle their own property updates in a type-safe manner.

**Example (`example_overrides.json`):**
```json
{
  "entity_overrides": {
    "fighter": { // Note: The system is case-insensitive for this ID, but consistency is good.
      "EntityData": {
        "MaxHealth": 150.0,
        "Speed": 220.0,
        "RotationSpeed": 6.28319,
        "Allegiance": "PlayerForces"
      }
    }
  }
}
```
This overrides properties within the `EntityData` object for entities identified by the registry ID "fighter". For instance, `EntityData.MaxHealth` will be set to `150.0`.

## IJsonPopulatable Interface

The new `IJsonPopulatable` interface provides a type-safe way for objects to handle their own property overrides:

```csharp
public interface IJsonPopulatable
{
    void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions);
}
```

### Benefits of IJsonPopulatable

1. **Type Safety**: Properties are updated with proper type checking at compile time.
2. **Performance**: Reduced reflection usage for better performance.
3. **Control**: Classes have full control over how their properties are updated.
4. **Nested Objects**: Better handling of complex nested objects and collections.

### Implementation Example

Key data classes like `WeaponData`, `ShipEntityData`, and `ProjectileEntityData` implement this interface to handle their own property updates:

```csharp
public partial class WeaponData : Resource, IJsonPopulatable
{
    // Properties...
    
    public void PopulateFromJson(JsonElement jsonOverrides, JsonSerializerOptions serializerOptions)
    {
        foreach (var property in jsonOverrides.EnumerateObject())
        {
            string propertyName = property.Name;
            JsonElement propertyValue = property.Value;
            
            switch (propertyName.ToLowerInvariant())
            {
                case "damage":
                    if (propertyValue.ValueKind == JsonValueKind.Number)
                        Damage = propertyValue.GetSingle();
                    break;
                // Other properties...
            }
        }
    }
}
```

## Transition Strategy

The system currently supports both approaches:

1. **IJsonPopulatable**: Used for objects that implement the interface.
2. **PropertyOverrideServiceV2**: Used as a fallback for objects that don't implement the interface.

Over time, more classes will be updated to implement `IJsonPopulatable` for improved type safety and performance.
