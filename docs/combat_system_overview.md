# Combat System

```mermaid
graph TD
    Projectile[Projectile] -->|Collision| DamageInfo[DamageInfo]
    DamageInfo -->|Apply| DamageSystem[DamageSystem]
    DamageSystem -->|Calculate| ShieldDamage[Shield Damage]
    DamageSystem -->|Calculate| HullDamage[Hull Damage]
    ShieldDamage -->|Apply| Ship[Ship Entity]
    HullDamage -->|Apply| Ship
    Ship -->|If Destroyed| DestroyEvent[Ship Destroyed Event]
    Ship -->|If Damaged| DamageEvent[Ship Damaged Event]
    DestroyEvent -->|Notify| BattleSystem[Battle System]
    DamageEvent -->|Notify| BattleSystem
```

## Component Interactions

```mermaid
graph LR
    ProjectileEntity[ProjectileEntity] -->|Detects| Collision
    Collision -->|Creates| DamageInfo
    DamageInfo -->|Processed by| DamageSystem
    DamageSystem -->|Modifies| ShipEntity
    ShipEntity -->|Fires| BattleEvents
    BattleEvents -->|Notifies| Listeners
```

## Damage Flow

```mermaid
flowchart TD
    Start[Projectile Hit] --> HasShields{Has Shields?}
    HasShields -->|Yes| ShieldDamage[Apply Shield Damage]
    HasShields -->|No| DirectDamage[Apply Hull Damage]
    ShieldDamage --> ShieldsRemaining{Shields Remaining?}
    ShieldsRemaining -->|Yes| End[End]
    ShieldsRemaining -->|No| OverflowDamage[Apply Overflow to Hull]
    DirectDamage --> CheckDestroyed{Hull <= 0?}
    OverflowDamage --> CheckDestroyed
    CheckDestroyed -->|Yes| DestroyShip[Destroy Ship]
    CheckDestroyed -->|No| End
    DestroyShip --> End
```

## Targeting System

```mermaid
graph TD
    TargetingBehavior[Targeting Behavior] -->|Scans| DetectionRange[Detection Range]
    DetectionRange -->|Finds| PotentialTargets[Potential Targets]
    PotentialTargets -->|Filters by| Allegiance
    Allegiance -->|Selects| Target[Target]
    Target -->|Informs| MovementBehavior[Movement Behavior]
    Target -->|Informs| WeaponBehavior[Weapon Behavior]
    Target -->|Lost?| FallbackScan[Extended Range Scan]
    FallbackScan -->|Reacquires| Target
```
