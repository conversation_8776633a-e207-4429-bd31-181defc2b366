# BattleManager Overview

`BattleManager.cs` is a crucial Godot Node responsible for orchestrating and managing the state of individual battle instances within the game. It handles the lifecycle of a battle, from initialization and entity spawning to monitoring end conditions.

## Key Responsibilities

- **Battle Initialization**: Sets up the battle environment, including player and enemy forces.
- **Wave Management**: Spawns waves of enemy entities based on `EnemySpawnConfig` data.
- **Player Fleet Spawning**: Deploys the player's pre-configured fleet at the start of a battle.
- **State Tracking**: Monitors the current state of the battle (e.g., Initializing, InProgress, Won, Lost).
- **End Condition Checking**: Determines if win or loss conditions have been met.

## Features

### Player Fleet Spawning

- **Method**: `public void SpawnPlayerFleet(Vector2 flagshipSpawnPosition)`
- **Role**: This method is responsible for deploying the player's fleet, as defined in `GameState.PlayerFleet`, at a specified position when a battle commences.
- **Process**:
    1. Retrieves `PlayerFleetComposition` data from `GameStateManager.Instance.CurrentGameState`.
    2. For the flagship and each escort ship defined:
        a. Requests instantiation of the base ship entity from `BattleEntityRegistry.Instance` using the `EntityId`.
        b. Sets the global position of the instantiated ship (flagship at `flagshipSpawnPosition`, escorts relative to it).
        c. Applies any `PropertyOverrides` defined in the fleet data using `PropertyOverrideService.Instance.ApplyOverrides()`.
        d. Registers the ship with the `SpaceBattleScene` and adds it as a child to the `BattleManager` node.

**Interaction Diagram:**

```mermaid
sequenceDiagram
    participant Caller
    participant BM as BattleManager
    participant GSM as GameStateManager
    participant BER as BattleEntityRegistry
    participant POS as PropertyOverrideService
    participant ShipInstance as BattleEntity

    Caller->>BM: SpawnPlayerFleet(spawnPos)
    BM->>GSM: Get CurrentGameState
    GSM-->>BM: GameState (with PlayerFleet)
    BM->>BER: InstantiateEntity(flagshipId)
    BER-->>BM: flagshipNode
    BM->>POS: ApplyOverrides(flagshipNode, overrides)
    BM->>BM: AddChild(flagshipNode) // And RegisterEntity
    loop For each Escort
        BM->>BER: InstantiateEntity(escortId)
        BER-->>BM: escortNode
        BM->>POS: ApplyOverrides(escortNode, overrides)
        BM->>BM: AddChild(escortNode) // And RegisterEntity
    end
```
