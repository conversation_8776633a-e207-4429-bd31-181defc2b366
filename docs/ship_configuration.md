# Ship Configuration

Ship entities are configured through `ShipEntityData` resources (`.tres` files) which define movement properties, behavior parameters, and weapon loadouts.

```mermaid
graph TD
    ShipEntityData[ShipEntityData.tres] -->|contains| BaseProps[Base Properties]
    ShipEntityData -->|contains| Movement[Movement Physics]
    ShipEntityData -->|contains| Thresholds[Movement Thresholds]
    ShipEntityData -->|contains| WeaponSlots[Weapon Slots Array]
    
    WeaponSlots -->|contains| Slot1[Slot 1: WeaponSlotData]
    WeaponSlots -->|contains| Slot2[Slot 2: WeaponSlotData]
    
    Slot1 -->|defines| Mount1[Mount Point ID]
    Slot1 -->|references| Weapon1[Weapon ID]
    Slot1 -->|controls| Visual1[Display Visuals]
    
    Slot2 -->|defines| Mount2[Mount Point ID]
    Slot2 -->|references| Weapon2[Weapon ID]
    Slot2 -->|controls| Visual2[Display Visuals]
```

## Weapon Loadout Configuration

Ship weapon loadouts are defined through the `WeaponSlots` array property:

```csharp
// Inside ShipEntityData.tres:
[ExportGroup("Armament")]
[Export]
public Godot.Collections.Array<WeaponSlotData> WeaponSlots { get; set; } = new();
```

Each `WeaponSlotData` entry contains:
- `MountPointId`: The name of a `WeaponMount` node in the ship scene
- `WeaponId`: The ID of the weapon to spawn at this mount
- `DisplayVisuals`: Whether to show the weapon's visual components

## Example Ship Definition

```gdscript
# Example of a ShipEntityData.tres resource definition in the Inspector

# Base Properties
Name: "Corvette"
Id: "ship_corvette_01"
BaseType: Ship
ShipDetailType: Corvette

# Movement Physics
MaxSpeed: 80.0
Acceleration: 150.0
Deceleration: 200.0
DragFactor: 0.7
RotationSpeed: 120.0

# Movement Thresholds
MovementThreshold: 5.0
RotationThreshold: 2.0
SlowingDistance: 100.0

# Armament
WeaponSlots: [
  WeaponSlotData:
    MountPointId: "Turret_Main_01"
    WeaponId: "weapon_dual_laser"
    DisplayVisuals: true
  WeaponSlotData:
    MountPointId: "Missile_Bay_Left"
    WeaponId: "weapon_missile_launcher"
    DisplayVisuals: true
  WeaponSlotData:
    MountPointId: "Missile_Bay_Right"
    WeaponId: "weapon_missile_launcher"
    DisplayVisuals: true
]
```
