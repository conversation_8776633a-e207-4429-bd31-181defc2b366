# Battle Entity Design Overview

This document outlines the core components for battle entity data, state, and scene management.

## Core Interaction Diagram

The primary components are `BattleEntityData` (a Godot `Resource` for configuration), `BattleEntityStateData` (a POCO for serializable instance state), `BattleEntityRegistryData` (a `Resource` for scene mappings), and `BattleEntityRegistry` (an Autoload Singleton for managing entity scenes).

```mermaid
graph TD
    subgraph Design & Setup
        A[Godot Editor] -- Configures --> B(BattleEntityData.tres Resource);
        A -- Assigns Data --> F(BattleEntityRegistryData.tres Resource);
        A -- Creates Scene & Attaches Script --> G[BattleEntityRegistry Node in tscn];
        G -- Loads Data --> F;
        A -- Configures Autoload --> G;
    end

    subgraph Runtime
        H(BattleEntityRegistry<br>Autoload Singleton) -- Provides Scene --> I(BattleEntityFactory);
        I -- Instantiates --> C{Battle Entity Node};
        B -- Template For --> C;
        C -- On Save --> D[BattleEntityStateData POCO];
        D -- Serialized --> E[Save Game File];
        E -- Deserialized --> D;
        D -- ApplyState() --> C;
        B -. Loaded to .-> C;
    end
```

## Component Descriptions

*   **`BattleEntityData` (`Resource`)**: Defines the configurable base attributes of an entity type in the Godot editor (e.g., `ScoutData.tres`). Contains logic to apply saved state. Lives in `Game/Scripts/Battle/BattleEntityData.cs`.
    * **`ShipEntityData`** extends this with ship-specific properties including a reference to a default `EnemyBehaviorConfiguration` for automatic behavior assignment.
*   **`BattleEntityStateData` (POCO)**: Holds the dynamic, serializable state of an entity instance for saving/loading (e.g., current health). Lives in `Game/Scripts/Battle/State/BattleEntityStateData.cs`.
*   **`BattleEntityRegistryData` (`Resource`)**: A resource (`.tres` file, e.g., `Data/Registries/BattleEntityRegistryData.tres`) configured in the editor to map entity IDs (strings) to their corresponding scene files (`PackedScene`). Also stores editor-registered behaviors and a fallback default behavior. Lives in `Game/Scripts/Battle/Registry/BattleEntityRegistryData.cs`.
*   **`BattleEntityRegistry` (Autoload Singleton)**: A Node script (`Game/Scripts/Battle/Registry/BattleEntityRegistry.cs`) attached to an autoloaded scene (`Singletons/BattleEntityRegistry.tscn`).
    *   Holds a reference to the `BattleEntityRegistryData` resource, providing access to editor-defined scene mappings.
    *   Provides static `Instance` for global access.
    *   Offers methods (`RegisterBattleEntity`, `GetBattleEntityScene`) to manage and retrieve entity scenes dynamically (both editor-defined and code-registered).
    *   Provides behavior lookup via `GetBehavior(string id)` to retrieve appropriate behavior configurations for entities.
*   **`BattleEntityFactory` (`Node`)**: Responsible for creating entity instances. Callers use its `CreateBattleEntityById(string entityId, Vector2 position, float rotation, Allegiance allegiance)` method, providing the specific string ID of the entity to spawn (obtained from `BattleEntityRegistry.Instance.GetBattleEntityScene()`) and its allegiance. This factory no longer contains specialized methods like `CreatePlayerEntity`. Lives in `Game/Scripts/Battle/BattleEntityFactory.cs`.
*   **`BattleEntityMovementController` (`Node`)**: A component attached to `BattleEntity` instances, responsible for handling kinematic movement and rotation. It uses parameters from `BattleEntityData` and provides a public API for external control. See [Battle Entity Movement System](./systems/battle_entity_movement.md) for details. Lives in `Game/Scripts/Battle/Movement/BattleEntityMovementController.cs`.

## Data Flow Summary

1.  **Design-Time**: `BattleEntityData` resources are defined for entity stats/base info. `BattleEntityRegistryData` resource is defined to map entity IDs to scenes. The `BattleEntityRegistry` scene is created, linked to the script and data resource, and set up as an Autoload.
2.  **Initialization**: The `BattleEntityRegistry` autoload instance becomes available.
3.  **Instantiation**: Callers (e.g., `BattleCoordinator`) invoke `BattleEntityFactory.CreateBattleEntityById()`, providing a string `entityId` (which the factory uses to request a scene from `BattleEntityRegistry.Instance`), initial `position`, `rotation`, and the entity's `allegiance`. The registry returns the corresponding `PackedScene`. The factory then instantiates this scene and sets the provided parameters on the new entity.
4.  **Save**: The current state of the entity node is captured into a `BattleEntityStateData` object, which is then serialized.
5.  **Load**: Serialized `BattleEntityStateData` is deserialized, and its values are applied back to the corresponding entity node (often by re-applying to its `BattleEntityData` or directly to the node's properties).

## Benefits of this Approach

-   **Editor Configurability**: `BattleEntityData` resources allow designers to easily create and tweak entity types in the Godot editor.
-   **Clean Serialization**: `BattleEntityStateData` provides a simple, serializable representation of entity state.
-   **Flexibility**: Allows for both templated entity definitions and individualized instance states.
-   **Adherence to Godot Practices**: Leverages Godot's `Resource` system for asset management while using POCOs for clean state serialization, aligning with best practices.
-   **Scene Management**: `BattleEntityRegistry` provides a centralized system for managing entity scenes, making it easier to add or remove entities without modifying the factory or other components.
